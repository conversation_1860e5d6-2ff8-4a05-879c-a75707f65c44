using System;
using System.Collections.Generic;
using System.ComponentModel;

/// <summary>
/// 🎯 截面类型信息
/// </summary>
public class SectionTypeInfo
{
    public int TypeId { get; set; }
    public string TypeCode { get; set; }        // AS, UAS, CS, LCS, etc.
    public string TypeName { get; set; }        // 等边角钢, 不等边角钢, etc.
    public string TypeNameEn { get; set; }      // Equal Angle Steel
    public string Standard { get; set; }        // GB/T 706-2008
    public string Description { get; set; }
    public int SortOrder { get; set; }
    
    public override string ToString()
    {
        return $"{TypeName} ({TypeCode})";
    }
}

/// <summary>
/// 🎯 标准截面信息 - 完整的截面数据
/// </summary>
public class StandardSectionInfo
{
    #region 基本信息
    
    public int SectionId { get; set; }
    public string TypeCode { get; set; }         // 类型代码
    public string TypeName { get; set; }         // 类型名称
    public string Standard { get; set; }         // 适用标准
    public string SectionCode { get; set; }      // 截面代号: L50×5
    public string SectionName { get; set; }      // 截面名称: 等边角钢L50×5
    
    #endregion
    
    #region 几何参数 (单位: mm)
    
    [DisplayName("宽度/长边")]
    public double B { get; set; }                // 宽度/长边/外径
    
    [DisplayName("高度/短边")]
    public double H { get; set; }                // 高度/短边/壁厚(管材)
    
    [DisplayName("厚度/壁厚")]
    public double T { get; set; }                // 厚度/壁厚
    
    [DisplayName("翼缘厚度")]
    public double T1 { get; set; }               // 翼缘厚度(工字钢、槽钢)
    
    [DisplayName("内圆角半径")]
    public double R { get; set; }                // 内圆角半径
    
    [DisplayName("端部圆角半径")]
    public double R1 { get; set; }               // 端部圆角半径
    
    [DisplayName("扩展参数D")]
    public double D { get; set; }                // 扩展参数1
    
    [DisplayName("扩展参数U")]
    public double U { get; set; }                // 扩展参数2
    
    [DisplayName("扩展参数F")]
    public double F { get; set; }                // 扩展参数3
    
    #endregion
    
    #region 截面特性
    
    [DisplayName("截面面积")]
    [Description("截面面积 (cm²)")]
    public double Area { get; set; }             // 截面面积 (cm²)
    
    [DisplayName("理论重量")]
    [Description("每米理论重量 (kg/m)")]
    public double WeightPerMeter { get; set; }   // 理论重量 (kg/m)
    
    // 惯性矩 (cm⁴)
    [DisplayName("X轴惯性矩")]
    [Description("绕X轴的惯性矩 (cm⁴)")]
    public double Ix { get; set; }
    
    [DisplayName("Y轴惯性矩")]
    [Description("绕Y轴的惯性矩 (cm⁴)")]
    public double Iy { get; set; }
    
    [DisplayName("扭转惯性矩")]
    [Description("扭转惯性矩 (cm⁴)")]
    public double Iz { get; set; }
    
    // 截面模量 (cm³)
    [DisplayName("X轴截面模量")]
    [Description("绕X轴的截面模量 (cm³)")]
    public double Wx { get; set; }
    
    [DisplayName("Y轴截面模量")]
    [Description("绕Y轴的截面模量 (cm³)")]
    public double Wy { get; set; }
    
    // 回转半径 (cm)
    [DisplayName("X轴回转半径")]
    [Description("绕X轴的回转半径 (cm)")]
    public double ix { get; set; }
    
    [DisplayName("Y轴回转半径")]
    [Description("绕Y轴的回转半径 (cm)")]
    public double iy { get; set; }
    
    [DisplayName("扭转回转半径")]
    [Description("扭转回转半径 (cm)")]
    public double iz { get; set; }
    
    // 形心坐标 (mm)
    [DisplayName("形心X坐标")]
    [Description("形心X坐标 (mm)")]
    public double Xc { get; set; }
    
    [DisplayName("形心Y坐标")]
    [Description("形心Y坐标 (mm)")]
    public double Yc { get; set; }
    
    #endregion
    
    #region 元数据
    
    [DisplayName("常用规格")]
    public bool IsCommon { get; set; }           // 是否常用规格
    
    [DisplayName("推荐规格")]
    public bool IsPreferred { get; set; }        // 是否推荐规格
    
    [DisplayName("最小长度")]
    [Description("最小供应长度 (mm)")]
    public double MinLength { get; set; }        // 最小长度 (mm)
    
    [DisplayName("最大长度")]
    [Description("最大供应长度 (mm)")]
    public double MaxLength { get; set; }        // 最大长度 (mm)
    
    [DisplayName("公差等级")]
    public string Tolerance { get; set; }        // 公差等级
    
    [DisplayName("表面处理")]
    public string SurfaceTreatment { get; set; } // 表面处理
    
    [DisplayName("备注")]
    public string Remarks { get; set; }          // 备注信息
    
    #endregion
    
    #region 辅助方法
    
    /// <summary>
    /// 获取完整的截面描述
    /// </summary>
    public string GetFullDescription()
    {
        return $"{SectionName} ({Standard})";
    }
    
    /// <summary>
    /// 获取几何参数描述
    /// </summary>
    public string GetGeometryDescription()
    {
        switch (TypeCode?.ToUpper())
        {
            case "AS": // 等边角钢
                return $"L{B}×{T}" + (R > 0 ? $"×R{R}" : "");
                
            case "UAS": // 不等边角钢
                return $"L{B}×{H}×{T}" + (R > 0 ? $"×R{R}" : "");
                
            case "CS": // 普通槽钢
                return $"[{H}×{B}×{T}×{T1}" + (R > 0 ? $"×R{R}" : "");
                
            case "LCS": // 轻型槽钢
                return $"[{H}×{B}×{T}×{T1}" + (R > 0 ? $"×R{R}" : "");
                
            case "HS": // 工字钢
                return $"I{H}×{B}×{T}×{T1}";
                
            case "RTS": // 矩形钢管
                return $"□{B}×{H}×{T}";
                
            case "STS": // 方形钢管
                return $"□{B}×{T}";
                
            case "CTS": // 圆形钢管
                return $"Φ{B}×{T}";
                
            default:
                return SectionCode;
        }
    }
    
    /// <summary>
    /// 获取重量描述
    /// </summary>
    public string GetWeightDescription()
    {
        return $"{WeightPerMeter:F2} kg/m";
    }
    
    /// <summary>
    /// 克隆截面信息
    /// </summary>
    public StandardSectionInfo Clone()
    {
        return new StandardSectionInfo
        {
            SectionId = SectionId,
            TypeCode = TypeCode,
            TypeName = TypeName,
            Standard = Standard,
            SectionCode = SectionCode,
            SectionName = SectionName,
            
            B = B, H = H, T = T, T1 = T1, R = R, R1 = R1, D = D, U = U, F = F,
            
            Area = Area,
            WeightPerMeter = WeightPerMeter,
            Ix = Ix, Iy = Iy, Iz = Iz,
            Wx = Wx, Wy = Wy,
            ix = ix, iy = iy, iz = iz,
            Xc = Xc, Yc = Yc,
            
            IsCommon = IsCommon,
            IsPreferred = IsPreferred,
            MinLength = MinLength,
            MaxLength = MaxLength,
            Tolerance = Tolerance,
            SurfaceTreatment = SurfaceTreatment,
            Remarks = Remarks
        };
    }
    
    public override string ToString()
    {
        return GetGeometryDescription();
    }
    
    #endregion
}

/// <summary>
/// 🎯 材料信息
/// </summary>
public class MaterialInfo
{
    public int MaterialId { get; set; }
    public string MaterialCode { get; set; }     // Q235, Q355, 6061, etc.
    public string MaterialName { get; set; }     // Q235碳素结构钢
    public string MaterialNameEn { get; set; }   // Q235 Carbon Steel
    public string Standard { get; set; }         // GB/T 700-2006
    
    #region 物理性能
    
    [DisplayName("密度")]
    [Description("材料密度 (kg/m³)")]
    public double Density { get; set; } = 7850;          // 密度 (kg/m³)
    
    [DisplayName("弹性模量")]
    [Description("弹性模量 (MPa)")]
    public double ElasticModulus { get; set; } = 206000;  // 弹性模量 (MPa)
    
    [DisplayName("剪切模量")]
    [Description("剪切模量 (MPa)")]
    public double ShearModulus { get; set; } = 79000;     // 剪切模量 (MPa)
    
    [DisplayName("泊松比")]
    [Description("泊松比")]
    public double PoissonRatio { get; set; } = 0.3;       // 泊松比
    
    #endregion
    
    #region 力学性能
    
    [DisplayName("屈服强度")]
    [Description("屈服强度 (MPa)")]
    public double YieldStrength { get; set; } = 235;      // 屈服强度 (MPa)
    
    [DisplayName("抗拉强度")]
    [Description("抗拉强度 (MPa)")]
    public double TensileStrength { get; set; } = 370;    // 抗拉强度 (MPa)
    
    #endregion
    
    #region 热学性能
    
    [DisplayName("线膨胀系数")]
    [Description("线膨胀系数 (1/°C)")]
    public double ThermalExpansion { get; set; } = 12e-6;  // 线膨胀系数 (1/°C)
    
    [DisplayName("导热系数")]
    [Description("导热系数 (W/m·K)")]
    public double ThermalConductivity { get; set; } = 50; // 导热系数 (W/m·K)
    
    #endregion
    
    public string Description { get; set; }
    
    public override string ToString()
    {
        return $"{MaterialName} ({MaterialCode})";
    }
    
    /// <summary>
    /// 获取材料的完整描述
    /// </summary>
    public string GetFullDescription()
    {
        return $"{MaterialName} ({Standard})";
    }
    
    /// <summary>
    /// 获取力学性能描述
    /// </summary>
    public string GetMechanicalDescription()
    {
        return $"fy={YieldStrength}MPa, fu={TensileStrength}MPa";
    }
}

/// <summary>
/// 🎯 截面参数信息
/// </summary>
public class SectionParameterInfo
{
    public int ParameterId { get; set; }
    public int TypeId { get; set; }
    public string ParameterCode { get; set; }    // B, H, T, R, etc.
    public string ParameterName { get; set; }    // 宽度, 高度, 厚度
    public string ParameterNameEn { get; set; }  // Width, Height, Thickness
    public string Unit { get; set; } = "mm";     // 单位
    public string Description { get; set; }      // 参数描述
    public bool IsRequired { get; set; } = true; // 是否必需参数
    public double DefaultValue { get; set; }     // 默认值
    public double? MinValue { get; set; }        // 最小值
    public double? MaxValue { get; set; }        // 最大值
    public int Precision { get; set; } = 1;      // 精度 (小数位数)
    public int SortOrder { get; set; }
    
    public override string ToString()
    {
        return $"{ParameterName} ({ParameterCode})";
    }
}

/// <summary>
/// 🎯 数据库统计信息
/// </summary>
public class DatabaseStatistics
{
    public int TotalSections { get; set; }
    public int TotalMaterials { get; set; }
    public string DatabaseVersion { get; set; }
    public List<SectionTypeCount> SectionTypeCounts { get; set; } = new List<SectionTypeCount>();
    
    public override string ToString()
    {
        return $"数据库版本: {DatabaseVersion}, 截面总数: {TotalSections}, 材料总数: {TotalMaterials}";
    }
}

/// <summary>
/// 🎯 截面类型统计
/// </summary>
public class SectionTypeCount
{
    public string TypeCode { get; set; }
    public string TypeName { get; set; }
    public int Count { get; set; }
    
    public override string ToString()
    {
        return $"{TypeName}: {Count}个";
    }
}

/// <summary>
/// 🎯 查询条件类
/// </summary>
public class SectionQueryCriteria
{
    public string TypeCode { get; set; }         // 截面类型
    public double? MinB { get; set; }            // 最小B值
    public double? MaxB { get; set; }            // 最大B值
    public double? MinH { get; set; }            // 最小H值
    public double? MaxH { get; set; }            // 最大H值
    public double? MinT { get; set; }            // 最小T值
    public double? MaxT { get; set; }            // 最大T值
    public double? MinArea { get; set; }         // 最小截面积
    public double? MaxArea { get; set; }         // 最大截面积
    public double? MinWeight { get; set; }       // 最小重量
    public double? MaxWeight { get; set; }       // 最大重量
    public bool? IsCommon { get; set; }          // 是否常用规格
    public bool? IsPreferred { get; set; }       // 是否推荐规格
    public string SectionCodePattern { get; set; } // 截面代号模糊匹配
    public string SectionNamePattern { get; set; } // 截面名称模糊匹配
    
    /// <summary>
    /// 是否有任何查询条件
    /// </summary>
    public bool HasCriteria => 
        !string.IsNullOrEmpty(TypeCode) ||
        MinB.HasValue || MaxB.HasValue ||
        MinH.HasValue || MaxH.HasValue ||
        MinT.HasValue || MaxT.HasValue ||
        MinArea.HasValue || MaxArea.HasValue ||
        MinWeight.HasValue || MaxWeight.HasValue ||
        IsCommon.HasValue || IsPreferred.HasValue ||
        !string.IsNullOrEmpty(SectionCodePattern) ||
        !string.IsNullOrEmpty(SectionNamePattern);
}

/// <summary>
/// 🎯 截面对比信息
/// </summary>
public class SectionComparisonInfo
{
    public StandardSectionInfo Section1 { get; set; }
    public StandardSectionInfo Section2 { get; set; }
    
    // 几何参数对比
    public double BDifference => Section2.B - Section1.B;
    public double HDifference => Section2.H - Section1.H;
    public double TDifference => Section2.T - Section1.T;
    
    // 截面特性对比
    public double AreaDifference => Section2.Area - Section1.Area;
    public double WeightDifference => Section2.WeightPerMeter - Section1.WeightPerMeter;
    public double IxDifference => Section2.Ix - Section1.Ix;
    public double IyDifference => Section2.Iy - Section1.Iy;
    
    // 百分比变化
    public double AreaPercentChange => Section1.Area > 0 ? (AreaDifference / Section1.Area) * 100 : 0;
    public double WeightPercentChange => Section1.WeightPerMeter > 0 ? (WeightDifference / Section1.WeightPerMeter) * 100 : 0;
    public double IxPercentChange => Section1.Ix > 0 ? (IxDifference / Section1.Ix) * 100 : 0;
    public double IyPercentChange => Section1.Iy > 0 ? (IyDifference / Section1.Iy) * 100 : 0;
}

/// <summary>
/// 🎯 截面推荐信息
/// </summary>
public class SectionRecommendationInfo
{
    public StandardSectionInfo RecommendedSection { get; set; }
    public double MatchScore { get; set; }        // 匹配度分数 (0-100)
    public string RecommendationReason { get; set; } // 推荐理由
    public List<string> Advantages { get; set; } = new List<string>(); // 优点
    public List<string> Considerations { get; set; } = new List<string>(); // 注意事项
}

/// <summary>
/// 🎯 用户自定义截面信息
/// </summary>
public class UserSectionInfo
{
    public int UserSectionId { get; set; }
    public string UserId { get; set; }
    public string ProjectId { get; set; }
    public string SectionName { get; set; }
    public int? BaseTypeId { get; set; }
    
    // 几何参数 (与StandardSectionInfo相同)
    public double B { get; set; }
    public double H { get; set; }
    public double T { get; set; }
    public double T1 { get; set; }
    public double R { get; set; }
    public double R1 { get; set; }
    public double D { get; set; }
    public double U { get; set; }
    public double F { get; set; }
    
    // 截面特性
    public double Area { get; set; }
    public double WeightPerMeter { get; set; }
    public double Ix { get; set; }
    public double Iy { get; set; }
    public double Wx { get; set; }
    public double Wy { get; set; }
    public double ix { get; set; }
    public double iy { get; set; }
    public double Xc { get; set; }
    public double Yc { get; set; }
    
    // 自定义数据
    public string GeometryData { get; set; }     // JSON格式的几何定义
    public byte[] PreviewImage { get; set; }     // 预览图片
    public string Description { get; set; }
    public string Tags { get; set; }             // 标签，用逗号分隔
    public bool IsTemplate { get; set; }         // 是否为模板
    public bool IsShared { get; set; }           // 是否共享
    
    public DateTime CreatedDate { get; set; }
    public DateTime UpdatedDate { get; set; }
    
    /// <summary>
    /// 转换为标准截面信息
    /// </summary>
    public StandardSectionInfo ToStandardSectionInfo()
    {
        return new StandardSectionInfo
        {
            SectionId = -UserSectionId, // 负数表示用户自定义
            TypeCode = "CUSTOM",
            TypeName = "自定义截面",
            Standard = "User Defined",
            SectionCode = SectionName,
            SectionName = SectionName,
            
            B = B, H = H, T = T, T1 = T1, R = R, R1 = R1, D = D, U = U, F = F,
            
            Area = Area,
            WeightPerMeter = WeightPerMeter,
            Ix = Ix, Iy = Iy,
            Wx = Wx, Wy = Wy,
            ix = ix, iy = iy,
            Xc = Xc, Yc = Yc,
            
            Remarks = Description
        };
    }
}