    /// <summary>
    /// 幕墙龙骨截面类型
    /// </summary>
    [Serializable]
    public class CurtainWallFrameType : DbElementType
    {
        #region 基本属性
        /// <summary>
        /// 用户备注
        /// </summary>
        public string Tag = "";

        /// <summary>
        /// 类型名称
        /// </summary>
        private string _name = "xxxx";
        /// <summary>
        /// 类型名称
        /// </summary>
        public string Name { get { return _name; } set { TransManager.Instance().Push(a => Name = a, _name); _name = value; } }

        /// <summary>
        /// 类型编号
        /// </summary>
        private int _type;
        /// <summary>
        /// 类型编号
        /// </summary>
        public int Type
        {
            get { return _type; }
            set
            {
                TransManager.Instance().Push(a => Type = a, _type = value);
                _type = value;
                //GenerateStandardGeometry();
            }
        }

        /// <summary>
        /// 材料类型
        /// </summary>
        private EleMaterial _material = EleMaterial.Steel;
        /// <summary>
        /// 材料类型
        /// </summary>
        public EleMaterial Material
        {
            get { return _material; }
            set
            {
                TransManager.Instance().Push(a => Material = a, _material = value);
                _material = value;
            }
        }

        // 截面类型描述
        // Type == 1： 圆角矩管
        // Type == 2： 直角矩管
        // Type == 3： 等边角钢
        // Type == 4： 不等边角钢
        // Type == 5： 普通槽钢
        // Type == 6:  轻型槽钢
        // Type == 50: 自定义截面
        #endregion

        #region 几何参数
        /// <summary>
        /// 截面参数 B
        /// </summary>
        private double _b;
        /// <summary>
        /// 截面参数 B
        /// </summary>
        public double B { get { return _b; } set { TransManager.Instance().Push(a => B = a, _b); _b = value; } }

        /// <summary>
        /// 截面参数 H
        /// </summary>
        private double _h;
        /// <summary>
        /// 截面参数 H
        /// </summary>
        public double H { get { return _h; } set { TransManager.Instance().Push(a => H = a, _h); _h = value; } }

        /// <summary>
        /// 截面参数 U
        /// </summary>
        private double _u;
        /// <summary>
        /// 截面参数 U
        /// </summary>
        public double U { get { return _u; } set { TransManager.Instance().Push(a => U = a, _u); _u = value; } }

        /// <summary>
        /// 截面参数 T
        /// </summary>
        private double _t;
        /// <summary>
        /// 截面参数 T
        /// </summary>
        public double T { get { return _t; } set { TransManager.Instance().Push(a => T = a, _t); _t = value; } }

        /// <summary>
        /// 截面参数 D
        /// </summary>
        private double _d;
        /// <summary>
        /// 截面参数 D
        /// </summary>
        public double D { get { return _d; } set { TransManager.Instance().Push(a => D = a, _d); _d = value; } }

        /// <summary>
        /// 截面参数 F
        /// </summary>
        public double _f;
        /// <summary>
        /// 截面参数 F
        /// </summary>
        public double F { get { return _f; } set { TransManager.Instance().Push(a => F = a, _f); _f = value; } }

        /// <summary>
        /// 截面参数 U1
        /// </summary>
        public double _u1;
        /// <summary>
        /// 截面参数 U1
        /// </summary>
        public double U1 { get { return _u1; } set { TransManager.Instance().Push(a => U1 = a, _u1); _u1 = value; } }

        /// <summary>
        /// 截面参数 T1
        /// </summary>
        public double _t1;
        /// <summary>
        /// 截面参数 T1
        /// </summary>
        public double T1 { get { return _t1; } set { TransManager.Instance().Push(a => T1 = a, _t1); _t1 = value; } }

        /// <summary>
        /// 截面参数 D1
        /// </summary>
        public double _d1;
        /// <summary>
        /// 截面参数 D1
        /// </summary>
        public double D1 { get { return _d1; } set { TransManager.Instance().Push(a => D1 = a, _d1); _d1 = value; } }

        /// <summary>
        /// 截面参数 F1
        /// </summary>
        public double _f1;
        /// <summary>
        /// 截面参数 F1
        /// </summary>
        public double F1 { get { return _f1; } set { TransManager.Instance().Push(a => F1 = a, _f1); _f1 = value; } }

        #endregion

        #region 截面特性参数（对标国标数据库或计算）
        /// <summary>
        /// 截面面积（cm²）
        /// </summary>
        public double Area { get; set; } = 0;
        
        /// <summary>
        /// 每米长质量（Kg/m）
        /// </summary>
        public double WeightPerMeter { get; set; } = 0;

        /// <summary>
        /// X轴惯性矩 (cm⁴)
        /// </summary>
        public double MomentOfInertiaX { get; set; } = 0;

        /// <summary>
        /// Y轴惯性矩 (cm⁴)
        /// </summary>
        public double MomentOfInertiaY { get; set; } = 0;

        /// <summary>
        /// X轴截面模量 (cm³)
        /// </summary>
        public double SectionModulusX { get; set; } = 0;

        /// <summary>
        /// Y轴截面模量 (cm³)
        /// </summary>
        public double SectionModulusY { get; set; } = 0;

        /// <summary>
        /// X轴回转半径 (cm)
        /// </summary>
        public double RadiusOfGyrationX { get; set; } = 0;

        /// <summary>
        /// Y轴回转半径 (cm)
        /// </summary>
        public double RadiusOfGyrationY { get; set; } = 0;
        #endregion

        #region  标准几何数据
        /// <summary>
        /// 标准几何线段 (以原点为中心，无旋转）
        /// </summary>
        internal List<DbLine> FrameLines = new List<DbLine>();

        /// <summary>
        /// 标准几何圆形
        /// </summary>
        internal List<DbCircle> FrameCircles = new List<DbCircle>();

        /// <summary>
        /// 标准几何轮廓点（按顺时针排列）
        /// </summary>
        internal List<DbPt> FrameOutPts = new List<DbPt>();
        #endregion

        #region 构造函数
        /// <summary>
        /// 无参构造函数
        /// </summary>
        public CurtainWallFrameType()
        {

        }

        /// <summary>
        /// 统一构造函数
        /// </summary>
        public CurtainWallFrameType(int type, double b, double h, double u, double t, double d, double f, double u1, double t1, double d1, double f1, EleMaterial material = EleMaterial.Steel)
        {
            _type = type;
            _material = Material;
            _b = b; _h = h; _u = u; _t = t; _d = d; _f = f; _u1 = u1; _t1 = t1; _d1 = d1; _f1 = f1;

            //根据type类型生成不同的几何轮廓
            GenerateStandardGeometry();

            // 设置默认名称
            //Name = GetDefaultSectionName();
        }
        #endregion

        #region 标准几何生成
        /// <summary>
        /// 根据type生成标准的几何
        /// </summary>
        public void GenerateStandardGeometry()
        {
            // 清空现有几何
            FrameLines.Clear();
            FrameCircles.Clear();
            FrameOutPts.Clear();

            // 根据type生成不同的几何
            switch(Type)
            {
                case 1: // 圆角矩管
                    GenerateRoundedRectTube();
                    Name = $"钢矩管 {H}X{B}X{T}";
                    break;

                case 2: // 直角矩管
                    GenerateStraightRectTube();
                    Name = $"精致钢矩管 {H}X{B}X{T}";
                    break;

                case 3: // 等边角钢
                    GenerateEqualAngleSteel();
                    Name = $"等边角钢 L{B}×{B}×{T}";
                    break;

                case 4: // 不等边角钢
                    GenerateUnequalAngleSteel();
                    Name = $"不等边角钢 L{H}×{B}×{T}";
                    break;
                case 5: // 普通槽钢
                    GenerateStandardChannelSteel();
                    Name = $"普通槽钢 C{B}×{H}×{T}";
                    break;
                case 6: // 轻型槽钢
                    GenearteLightChannelSteel();
                    Name = $"轻型槽钢 CQ{B}×{H}×{T}";
                    break;
                case 7: // 圆钢管
                    GenerateCircularSteelPipe();
                    Name = $"圆钢管 Φ{B}×{T}";
                    break;
                case 8: // 工字钢
                    GenerateIBeam();
                    Name = $"工字钢 I{B}×{H}×{T}×{U}";
                    break;

                case 50: // 自定义
                         // 自定义截面直接绘制
                    Name = "自定义截面";
                    break;

                default:
                    // 默认按照圆角矩管处理
                    GenerateRoundedRectTube();
                    Name = $"钢矩管 {H}X{B}X{T}";
                    break;
            }
        }

        /// <summary>
        /// 生成圆角钢矩管
        /// </summary>
        private void GenerateRoundedRectTube()
        {
            double halfB = B / 2.0;
            double halfH = H / 2.0;
            double radius = CalculateTubeRadius(T); // 圆角半径

            // 外轮廓点集
            List<DbPt> outerPts = GenerateRoundRectAnglePoints(halfB, halfH, radius);

            // 生成外轮廓线
            ConvertPtsToLines(outerPts);

            // 内轮廓
            if (T > 0)
            {
                List<DbPt> innerPts = GMath.GetOffsetPts(outerPts, T, true);
                if (innerPts != null && innerPts.Count > 0)
                {
                    ConvertPtsToLines(innerPts);
                }
            }

            // 存储完整的轮廓点
            FrameOutPts.Clear();
            FrameOutPts.AddRange(outerPts);
        }

        /// <summary>
        /// 生成普通槽钢几何形状
        /// </summary>
        private void GenerateStandardChannelSteel()
        {
            double halfB = B / 2.0;
            double halfH = H / 2.0;

            // 普通槽钢外轮廓（C形，顺时针）
            FrameOutPts.Add(new DbPt(halfB, halfH));                    // 右上外角
            FrameOutPts.Add(new DbPt(halfB, halfH - U));               // 右上翼缘内边
            FrameOutPts.Add(new DbPt(halfB - F, halfH - U));           // 右上翼缘内角
            FrameOutPts.Add(new DbPt(halfB - F, halfH - U - D));       // 右腹板上端
            FrameOutPts.Add(new DbPt(-halfB + T, halfH - U - D));      // 左腹板上端
            FrameOutPts.Add(new DbPt(-halfB + T, -halfH + U + D));     // 左腹板下端
            FrameOutPts.Add(new DbPt(halfB - F, -halfH + U + D));      // 右腹板下端
            FrameOutPts.Add(new DbPt(halfB - F, -halfH + U));          // 右下翼缘内角
            FrameOutPts.Add(new DbPt(halfB, -halfH + U));              // 右下翼缘内边
            FrameOutPts.Add(new DbPt(halfB, -halfH));                  // 右下外角
            FrameOutPts.Add(new DbPt(-halfB, -halfH));                 // 左下
            FrameOutPts.Add(new DbPt(-halfB, halfH));                  // 左上

            // 生成外轮廓线
            ConvertPtsToLines(FrameOutPts);
        }

        /// <summary>
        /// 生成等边角钢几何形状
        /// </summary>
        /// <summary>
        /// 生成等边角钢几何形状
        /// </summary>
        private void GenerateEqualAngleSteel()
        {
            double halfB = B / 2.0;
            double halfH = H / 2.0;

            // 等边角钢外轮廓点（L形，顺时针）
            FrameOutPts.Add(new DbPt(halfB, halfH));           // 右上
            FrameOutPts.Add(new DbPt(halfB, halfH - T));       // 右上内角
            FrameOutPts.Add(new DbPt(halfB - T, halfH - T));   // 内角转折点
            FrameOutPts.Add(new DbPt(halfB - T, -halfH));      // 右下内角
            FrameOutPts.Add(new DbPt(-halfB, -halfH));         // 左下
            FrameOutPts.Add(new DbPt(-halfB, halfH));          // 左上

            // 生成外轮廓线
            ConvertPtsToLines(FrameOutPts);
        }



        /// <summary>
        /// 生成工字钢几何形状
        /// </summary>
        private void GenerateIBeam()
        {
            double halfB = B / 2.0;  // 翼缘宽度的一半
            double halfH = H / 2.0;  // 截面高度的一半
            double webThickness = T; // 腹板厚度
            double flangeThickness = U; // 翼缘厚度
            double halfWebThickness = webThickness / 2.0;

            // 工字钢外轮廓点（顺时针）
            FrameOutPts.Add(new DbPt(halfB, halfH));                                    // 右上外角
            FrameOutPts.Add(new DbPt(halfB, halfH - flangeThickness));                 // 右上翼缘内边
            FrameOutPts.Add(new DbPt(halfWebThickness, halfH - flangeThickness));      // 右腹板上端
            FrameOutPts.Add(new DbPt(halfWebThickness, -halfH + flangeThickness));     // 右腹板下端
            FrameOutPts.Add(new DbPt(halfB, -halfH + flangeThickness));                // 右下翼缘内边
            FrameOutPts.Add(new DbPt(halfB, -halfH));                                  // 右下外角
            FrameOutPts.Add(new DbPt(-halfB, -halfH));                                 // 左下外角
            FrameOutPts.Add(new DbPt(-halfB, -halfH + flangeThickness));               // 左下翼缘内边
            FrameOutPts.Add(new DbPt(-halfWebThickness, -halfH + flangeThickness));    // 左腹板下端
            FrameOutPts.Add(new DbPt(-halfWebThickness, halfH - flangeThickness));     // 左腹板上端
            FrameOutPts.Add(new DbPt(-halfB, halfH - flangeThickness));                // 左上翼缘内边
            FrameOutPts.Add(new DbPt(-halfB, halfH));                                  // 左上外角

            // 生成外轮廓线
            ConvertPtsToLines(FrameOutPts);
        }

        /// <summary>
        /// 生成圆钢管几何形状
        /// </summary>
        private void GenerateCircularSteelPipe()
        {
            double radius = B / 2.0;  // 外径半径
            double innerRadius = radius - T;  // 内径半径

            // 外圆
            DbCircle outerCircle = new DbCircle(new DbPt(0, 0), radius);
            FrameCircles.Add(outerCircle);

            // 内圆（如果壁厚大于0且内径大于0）
            if (T > 0 && innerRadius > 0)
            {
                DbCircle innerCircle = new DbCircle(new DbPt(0, 0), innerRadius);
                FrameCircles.Add(innerCircle);
            }

            // 生成圆形轮廓点（用于碰撞检测等）
            int pointCount = 36; // 36个点，每10度一个点
            for (int i = 0; i < pointCount; i++)
            {
                double angle = i * 2 * Math.PI / pointCount;
                double x = radius * Math.Cos(angle);
                double y = radius * Math.Sin(angle);
                FrameOutPts.Add(new DbPt(x, y));
            }
        }

        /// <summary>
        /// 生成轻型槽钢几何形状
        /// </summary>
        private void GenearteLightChannelSteel()
        {
            double halfB = B / 2.0;
            double halfH = H / 2.0;

            // 轻型槽钢外轮廓（C形，顺时针）
            FrameOutPts.Add(new DbPt(halfB, halfH));                    // 右上外角
            FrameOutPts.Add(new DbPt(halfB, halfH - U));               // 右上翼缘内边
            FrameOutPts.Add(new DbPt(halfB - F, halfH - U));           // 右上翼缘内角
            FrameOutPts.Add(new DbPt(halfB - F, halfH - U - D));       // 右腹板上端
            FrameOutPts.Add(new DbPt(-halfB + T, halfH - U - D));      // 左腹板上端
            FrameOutPts.Add(new DbPt(-halfB + T, -halfH + U + D));     // 左腹板下端
            FrameOutPts.Add(new DbPt(halfB - F, -halfH + U + D));      // 右腹板下端
            FrameOutPts.Add(new DbPt(halfB - F, -halfH + U));          // 右下翼缘内角
            FrameOutPts.Add(new DbPt(halfB, -halfH + U));              // 右下翼缘内边
            FrameOutPts.Add(new DbPt(halfB, -halfH));                  // 右下外角
            FrameOutPts.Add(new DbPt(-halfB, -halfH));                 // 左下
            FrameOutPts.Add(new DbPt(-halfB, halfH));                  // 左上

            // 生成外轮廓线
            ConvertPtsToLines(FrameOutPts);
        }

        /// <summary>
        /// 生成不等边角钢几何形状
        /// </summary>
        private void GenerateUnequalAngleSteel()
        {
            double halfB = B / 2.0;  // 短边
            double halfH = H / 2.0;  // 长边

            // 不等边角钢外轮廓点（L形，顺时针）
            FrameOutPts.Add(new DbPt(halfB, halfH));           // 右上
            FrameOutPts.Add(new DbPt(halfB, halfH - T));       // 右上内角
            FrameOutPts.Add(new DbPt(halfB - T, halfH - T));   // 内角转折点
            FrameOutPts.Add(new DbPt(halfB - T, -halfH));      // 右下内角
            FrameOutPts.Add(new DbPt(-halfB, -halfH));         // 左下
            FrameOutPts.Add(new DbPt(-halfB, halfH));          // 左上

            // 生成外轮廓线
            ConvertPtsToLines(FrameOutPts);
        }


        /// <summary>
        /// 生成直角矩管几何形状
        /// </summary>
        private void GenerateStraightRectTube()
        {
            double halfB = B / 2.0;
            double halfH = H / 2.0;

            // 外轮廓（顺时针）
            FrameOutPts.Add(new DbPt(halfB, halfH));   // 右上
            FrameOutPts.Add(new DbPt(halfB, -halfH));  // 右下  
            FrameOutPts.Add(new DbPt(-halfB, -halfH)); // 左下
            FrameOutPts.Add(new DbPt(-halfB, halfH));  // 左上

            // 外轮廓线
            FrameLines.Add(new DbLine(halfB, halfH, 0, halfB, -halfH, 0));   // 右边
            FrameLines.Add(new DbLine(halfB, -halfH, 0, -halfB, -halfH, 0));  // 下边
            FrameLines.Add(new DbLine(-halfB, -halfH, 0, -halfB, halfH, 0));  // 左边
            FrameLines.Add(new DbLine(-halfB, halfH, 0, halfB, halfH, 0));    // 上边

            // 内轮廓（如果壁厚大于0）
            if (T > 0 && T < Math.Min(B, H) / 2.0)
            {
                double innerHalfB = halfB - T;
                double innerHalfH = halfH - T;

                // 内轮廓线（逆时针，形成孔洞）
                FrameLines.Add(new DbLine(innerHalfB, innerHalfH, 0, -innerHalfB, innerHalfH, 0));    // 上边
                FrameLines.Add(new DbLine(-innerHalfB, innerHalfH, 0, -innerHalfB, -innerHalfH, 0));  // 左边
                FrameLines.Add(new DbLine(-innerHalfB, -innerHalfH, 0, innerHalfB, -innerHalfH, 0));  // 下边
                FrameLines.Add(new DbLine(innerHalfB, -innerHalfH, 0, innerHalfB, innerHalfH, 0));    // 右边
            }
        }



        #endregion

        #region 辅助几何方法
        /// <summary>
        /// 根据壁厚自动计算圆角半径
        /// 规则：
        /// - 当壁厚t≤3时，r=2*t
        /// - 当3<t≤6时，r=2.5*t  
        /// - 当6<t≤10时，r=3*t
        /// - 当t>10时，r=3*t
        /// </summary>
        /// <returns>计算得到的圆角半径</returns>
        private double CalculateTubeRadius(double thickness)
        {
            if (thickness <= 3)
            {
                return 2 * thickness;
            }
            else if (thickness <= 6)
            {
                return 2.5 * thickness;
            }
            else if (thickness <= 10)
            {
                return 3 * thickness;
            }
            else
            {
                return 3 * thickness;
            }
        }


        /// <summary>
        /// 生成轮廓点（包括弧线中点，逆时针储存）
        /// </summary>
        private List<DbPt> GenerateRoundRectAnglePoints(double halfB, double halfH, double radius)
        {
            List<DbPt> pts = new List<DbPt>();
            // 路径：左下--右下--右上--左上

            // 1、左下角弧线（左上起点至下边终点）
            DbPt leftBottomArcSt = new DbPt(-halfB, -halfH + radius); // 起点
            DbPt leftBottomArcEnd = new DbPt(-halfB + radius, -halfH); // 终点
            DbPt leftBottomArcCen = new DbPt(-halfB + radius, halfH - radius); // 圆心    
            //DbLine leftBottomArc = GMath.GetArcByStCenEnd(leftBottomArcSt, leftBottomArcEnd, new DbPt(-halfB + radius, -halfH + radius));

            // 计算圆弧中点
            DbPt leftBottomArcMid = GMath_CW.GetArcMidByStEndAndCen(leftBottomArcSt, leftBottomArcEnd, leftBottomArcCen, radius);
            leftBottomArcSt.PtType = 0;
            leftBottomArcEnd.PtType = 0;
            pts.Add(leftBottomArcSt);
            pts.Add(leftBottomArcMid);
            pts.Add(leftBottomArcEnd);

            // 2、下边：左下角弧线终点---右下角弧线起点
            DbPt rightBottomArcSt = new DbPt(halfB - radius, -halfH);
            rightBottomArcSt.PtType = 0;
            pts.Add(rightBottomArcSt);

            // 3、右下角弧线（从下边起点到右边终点）
            DbPt rightBottomArcEnd = new DbPt(halfB, -halfH + radius);
            DbPt rightBottomArcCen = new DbPt(halfB - radius, -halfH + radius);
            DbPt rightBottomArcMid = GMath_CW.GetArcMidByStEndAndCen(rightBottomArcSt, rightBottomArcEnd, rightBottomArcCen, radius);
            rightBottomArcEnd.PtType = 0;
            pts.Add(rightBottomArcMid);
            pts.Add(rightBottomArcEnd);

            // 4、右边 右下弧线起点---右上弧中点
            DbPt rightTopArcSt = new DbPt(halfB, halfH - radius);
            rightTopArcSt.PtType = 0;
            pts.Add(rightTopArcSt);

            // 5、右上角弧线
            DbPt rightTopArcEnd = new DbPt(halfB - radius, halfH);
            DbPt rightTopArcCen = new DbPt(halfB - radius, halfH - radius);
            DbPt rightTopArcMid = GMath_CW.GetArcMidByStEndAndCen(rightTopArcSt, rightTopArcEnd, rightTopArcCen, radius);
            rightTopArcEnd.PtType = 0;
            pts.Add(rightTopArcMid);
            pts.Add(rightTopArcEnd);

            // 6、左上角弧线
            DbPt leftTopArcSt = new DbPt(-halfB + radius, halfH);
            leftTopArcSt.PtType = 0;
            pts.Add(leftTopArcSt);
            DbPt leftTopArcEnd = new DbPt(-halfB, halfH - radius);
            DbPt leftTopArcCen = new DbPt(-halfB + radius, halfH - radius);
            DbPt leftTopArcMid = GMath_CW.GetArcMidByStEndAndCen(leftTopArcSt, leftTopArcEnd, leftTopArcCen, radius);
            leftTopArcEnd.PtType = 0;
            pts.Add(leftTopArcMid);
            pts.Add(leftTopArcEnd);

            return pts;
        }

        /// <summary>
        /// 将点集转化为线段并添加到FrameLines
        /// </summary>
        private void ConvertPtsToLines(List<DbPt> pts)
        {
            if (pts == null || pts.Count < 3) return;

            for (int i = 0; i < pts.Count; i++)
            {
                DbPt curtPt = pts[i];
                DbPt nextPt = pts[(i + 1) % pts.Count];

                if (nextPt.PtType == 1)
                {
                    // 创建圆弧线段
                    DbPt endPt = pts[(i + 2) % pts.Count];
                    DbLine arcLine = new DbLine(curtPt, endPt, nextPt);
                    FrameLines.Add(arcLine);
                    i++; // 跳过圆弧中点
                }
                else
                {
                    // 创建直线段
                    DbLine line = new DbLine(curtPt, nextPt);
                    FrameLines.Add(line);
                }
            }
        }


        #endregion

        #region 几何变换方法
        /// <summary>
        /// 返回龙骨的实例对应的线单元
        /// </summary>
        /// <param name="angle">转角，弧度制</param>
        /// <param name="X">X坐标</param>
        /// <param name="Y">Y做表</param>
        /// <returns>构成龙骨的线元素</returns>
        public List<DbLine> RotateAndMove(double angle, double X, double Y)
        {
            List<DbLine> actualLines = new List<DbLine>();

            for (int i = 0; i < FrameLines.Count; i++)
            {
                if (FrameLines[i].IfArc == false)
                {
                    //先基于原点进行旋转
                    DbPt pt1 = GMath.PtRotate(new DbPt(0, 0, 0), FrameLines[i].PtSt, angle);
                    DbPt pt2 = GMath.PtRotate(new DbPt(0, 0, 0), FrameLines[i].PtEnd, angle);

                    //进行平移
                    pt1 = pt1.Move(X, Y);
                    pt2 = pt2.Move(X, Y);
                    actualLines.Add(new DbLine(pt1, pt2, FrameLines[i].LayerId));
                }
                else
                {
                    //先基于原点进行旋转
                    DbPt pt1 = GMath.PtRotate(new DbPt(0, 0, 0), FrameLines[i].PtSt, angle);
                    DbPt pt2 = GMath.PtRotate(new DbPt(0, 0, 0), FrameLines[i].PtEnd, angle);
                    DbPt pt3 = GMath.PtRotate(new DbPt(0, 0, 0), FrameLines[i].PtMid, angle);

                    //进行平移
                    pt1 = pt1.Move(X, Y);
                    pt2 = pt2.Move(X, Y);
                    pt3 = pt3.Move(X, Y);
                    actualLines.Add(new DbLine(pt1, pt2, pt3, FrameLines[i].LayerId));
                }
            }
            return actualLines;
        }

        /// <summary>
        /// 返回龙骨的实例对应的圆单元
        /// </summary>
        /// <param name="X">X坐标</param>
        /// <param name="Y">Y坐标</param>
        /// <returns>构成柱的圆(椭圆)元素</returns>
        public List<DbCircle> RotateAndMove(double X, double Y)
        {
            List<DbCircle> ColCircles = new List<DbCircle>();
            for (int i = 0; i < FrameCircles.Count; i++)
            {
                DbCircle cir = FrameCircles[i].Move(X, Y);
                cir.LayerId = FrameCircles[i].LayerId;
                cir.LayerId = FrameCircles[i].LayerId;
                ColCircles.Add(FrameCircles[i].Move(X, Y));
            }
            return ColCircles;
        }

        /// <summary>
        /// 返回龙骨的实例对应的柱外轮廓点集合
        /// </summary>
        /// <param name="angle">柱转角，弧度制</param>
        /// <param name="X">X坐标</param>
        /// <param name="Y">Y坐标</param>
        /// <returns>柱外轮廓的点(仅适用于非圆形柱)</returns>
        public List<DbPt> RotateAndMovePts(double angle, double X, double Y)
        {
            List<DbPt> pts = new List<DbPt>();

            for (int i = 0; i < FrameOutPts.Count; i++)
            {
                //先基于原点进行旋转
                DbPt pt1 = GMath.PtRotate(new DbPt(0, 0, 0), FrameOutPts[i], angle);

                //进行平移
                pt1 = pt1.Move(X, Y);

                pts.Add(pt1);
            }
            return pts;
        }

        /// <summary>
        /// 返回不为0的参数数量
        /// </summary>
        /// <returns>不为0的参数数量</returns>
        public int GetNotZeroParaNum()
        {
            int count = 0;
            if (B != 0) { count++; }
            if (H != 0) { count++; }
            if (U != 0) { count++; }
            if (T != 0) { count++; }
            if (D != 0) { count++; }
            if (F != 0) { count++; }
            if (U1 != 0) { count++; }
            if (T1 != 0) { count++; }
            if (D1 != 0) { count++; }
            if (F1 != 0) { count++; }
            return count;
        }

        #endregion

        #region 截面信息方法
        /// <summary>
        /// 获取截面名称
        /// </summary>
        public override string GetSecName()
        {
            if (!string.IsNullOrEmpty(Name))
            {
                return Name;
            }
            return GetDefaultSectionName();
        }

        /// <summary>
        /// 获取默认截面名称
        /// </summary>
        private string GetDefaultSectionName()
        {
            switch (Type)
            {
                case 1: return $"圆角矩管 {B}×{H}×{T}";
                case 2: return $"直角矩管 {B}×{H}×{T}";
                case 3: return $"等边角钢 L{B}×{B}×{T}";
                case 4: return $"不等边角钢 L{H}x{B}X{T}";
                case 5: return $"普通槽钢 C{B}×{H}×{T}";
                case 6: return $"轻型槽钢 CQ{B}×{H}×{T}";
                case 50: return $"自定义截面{B}";
                default: return "未知截面";
            }
        }

        #endregion

        #region 虚拟截面方法
        ///// <summary>
        ///// 深度复制截面类型（用于创建虚拟截面）
        ///// </summary>
        //public CurtainWallFrameType Clone()
        //{
        //    var clone = new CurtainWallFrameType(Type, B, H, U, T, D, F, U1, T1, D1, F1, Material);
        //    clone.Name = Name;
        //    clone.Tag = Tag;

        //    // 不复制UniqueId, 虚拟截面不需要全局唯一标识
        //    return clone;
        //}

        ///// <summary>
        ///// 判断两个截面是否相同
        ///// </summary>
        ///// <paramref name="other">待比较类型</paramref>
        ///// <returns>true: 相同 false：不同</returns>
        //public bool IfSame(CurtainWallFrameType other)
        //{
        //    if (other == null) return false;
        //    if (this.Name == other.Name && this.Type == other.Type && this.B == other.B
        //        && this.H == other.H && this.U == other.U && this.T == other.T
        //        && this.D == other.D && this.F == other.F && this.U1 == other.U1
        //        && this.T1 == other.T1 && this.D1 == other.D1 && this.F1 == other.F1 && this.Material == other.Material)
        //    {
        //        return true;
        //    }
        //    else { return false; }

        //}
        #endregion

        #region 数据持久化
        /// <summary>
        /// 保存数据
        /// </summary>
        /// <param name="binaryWriter"></param>
        public override void DataSave(BinaryWriter binaryWriter)
        {
            //版本号
            binaryWriter.Write(0);
            binaryWriter.Write(_uniqueId);
            binaryWriter.Write(CreatorId);
            binaryWriter.Write(Type);
            binaryWriter.Write(Name);
            binaryWriter.Write(B);
            binaryWriter.Write(H);
            binaryWriter.Write(U);
            binaryWriter.Write(T);
            binaryWriter.Write(D);
            binaryWriter.Write(F);
            binaryWriter.Write(U1);
            binaryWriter.Write(T1);
            binaryWriter.Write(D1);
            binaryWriter.Write(F1);
            binaryWriter.Write((int)Material);

            binaryWriter.Write(FrameLines.Count); foreach (DbLine ele in FrameLines) { ele.DataSave(binaryWriter); }
            binaryWriter.Write(FrameCircles.Count); foreach (DbCircle ele in FrameCircles) { ele.DataSave(binaryWriter); }
            binaryWriter.Write(FrameOutPts.Count); foreach (DbPt ele in FrameOutPts) { ele.DataSave(binaryWriter); }

            binaryWriter.Write(Tag);
        }

        /// <summary>
        /// 装载数据
        /// </summary>
        /// <param name="binaryReader"></param>
        public override void DataLoad(BinaryReader binaryReader)
        {
            int VerNum = binaryReader.ReadInt32();
            if (VerNum == 0)
            {
                _uniqueId = binaryReader.ReadString();
                CreatorId = binaryReader.ReadString();
                Type = binaryReader.ReadInt32();
                Name = binaryReader.ReadString();
                B = binaryReader.ReadInt32();
                H = binaryReader.ReadInt32();
                U = binaryReader.ReadInt32();
                T = binaryReader.ReadInt32();
                D = binaryReader.ReadInt32();
                F = binaryReader.ReadInt32();
                U1 = binaryReader.ReadInt32();
                T1 = binaryReader.ReadInt32();
                D1 = binaryReader.ReadInt32();
                F1 = binaryReader.ReadInt32();
                Material = (EleMaterial)binaryReader.ReadInt32();

                for (int i = 0; i < FrameLines.Count; i++)
                {
                    FrameLines.Add(new DbLine(binaryReader));
                }
                for (int i = 0; i < FrameCircles.Count; i++)
                {
                    FrameCircles.Add(new DbCircle(binaryReader));
                }
                for (int i = 0; i < FrameOutPts.Count; i++)
                {
                    FrameOutPts.Add(new DbPt(binaryReader));
                }

                Tag = binaryReader.ReadString();
            }
        }

        /// <summary>
        /// 复制
        /// </summary>
        /// <param name="changeUid">是否改变UniqueId</param>
        /// <returns></returns>
        public virtual CurtainWallFrameType EleCopy(bool changeUid = false)
        {
            CurtainWallFrameType cwType = new CurtainWallFrameType();
            cwType._uniqueId = _uniqueId;
            cwType._name = _name;
            cwType._type = _type;
            cwType._b = _b;
            cwType._h = _h;
            cwType._u = _u;
            cwType._t = _t;
            cwType._d = _d;
            cwType._f = _f;
            cwType._u1 = _u1;
            cwType._t1 = _t1;
            cwType._d1 = _d1;
            cwType._f1 = _f1;
            cwType._material = _material;
            cwType.FrameOutPts.AddRange(FrameOutPts);
            cwType.FrameLines.AddRange(FrameLines);
            cwType.FrameCircles.AddRange(FrameCircles);
            if (changeUid) { cwType._uniqueId = Guid.NewGuid().ToString(); }
            return cwType;
        }
        #endregion
    }

