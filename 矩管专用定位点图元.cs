using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Windows.Media.Media3D;

/// <summary>
/// 矩管专用定位点图元类 - 独立图元版本
/// 功能：专门为矩管图元设计的定位点，提供精确的位置控制和智能拉伸功能
/// 特点：作为独立图元存在，通过绑定系统与矩管关联
/// 架构：符合平台"一个图元一个独立实体"的设计原则
/// </summary>
[Serializable]
[DbElement("矩管定位点", MajorType.Auxiliary)]
public class TubeLocatorPoint : DbElement
{
    #region 定位点类型枚举
    /// <summary>
    /// 矩管定位点类型
    /// </summary>
    public enum TubeLocatorType
    {
        /// <summary>
        /// 上边中点
        /// </summary>
        TopCenter = 0,
        
        /// <summary>
        /// 下边中点
        /// </summary>
        BottomCenter = 1,
        
        /// <summary>
        /// 左边中点
        /// </summary>
        LeftCenter = 2,
        
        /// <summary>
        /// 右边中点
        /// </summary>
        RightCenter = 3,
        
        /// <summary>
        /// 几何中心
        /// </summary>
        GeometryCenter = 4
    }
    #endregion

    #region 定位点参数
    /// <summary>
    /// 定位点位置
    /// </summary>
    private DbPt _position = new DbPt();
    
    /// <summary>
    /// 定位点位置
    /// </summary>
    [Category("位置参数"), DisplayName("位置"), Description("定位点的位置坐标"), ReadOnly(false)]
    public DbPt Position
    {
        get { return _position; }
        set
        {
            if (_position.Equals(value)) return;
            TransManager.Instance().Push(a => Position = a, _position.EleCopy());
            _position = value.EleCopy();
            
            // 位置改变时通知绑定的矩管
            NotifyBoundTubeUpdate();
            _needRecalcGeometry = true;
            ActCutCal2D3D();
        }
    }

    /// <summary>
    /// 定位点类型
    /// </summary>
    private TubeLocatorType _locatorType = TubeLocatorType.BottomCenter;
    
    /// <summary>
    /// 定位点类型
    /// </summary>
    [Category("控制参数"), DisplayName("定位点类型"), Description("定位点在矩管中的位置类型"), ReadOnly(false)]
    public TubeLocatorType LocatorType
    {
        get { return _locatorType; }
        set
        {
            if (_locatorType == value) return;
            TransManager.Instance().Push(a => LocatorType = a, _locatorType);
            _locatorType = value;
            
            // 类型改变时重新计算位置
            if (!string.IsNullOrEmpty(_boundTubeId))
            {
                SyncPositionWithBoundTube();
            }
            
            _needRecalcGeometry = true;
            ActCutCal2D3D();
        }
    }

    /// <summary>
    /// 绑定的矩管ID
    /// </summary>
    private string _boundTubeId = "";
    
    /// <summary>
    /// 绑定的矩管ID
    /// </summary>
    [Category("绑定信息"), DisplayName("绑定矩管"), Description("绑定的矩管图元ID"), ReadOnly(true)]
    public string BoundTubeId
    {
        get { return _boundTubeId; }
        set
        {
            if (_boundTubeId == value) return;
            _boundTubeId = value;
            
            // 绑定改变时重新同步位置
            if (!string.IsNullOrEmpty(_boundTubeId))
            {
                SyncPositionWithBoundTube();
            }
        }
    }

    /// <summary>
    /// 相对偏移X
    /// </summary>
    private double _offsetX = 0;
    
    /// <summary>
    /// 相对偏移X
    /// </summary>
    [Category("位置参数"), DisplayName("X偏移"), Description("相对于基准位置的X偏移"), ReadOnly(false)]
    public double OffsetX
    {
        get { return _offsetX; }
        set
        {
            if (Math.Abs(_offsetX - value) < 0.001) return;
            TransManager.Instance().Push(a => OffsetX = a, _offsetX);
            _offsetX = value;
            
            // 偏移改变时重新计算位置
            if (!string.IsNullOrEmpty(_boundTubeId))
            {
                SyncPositionWithBoundTube();
            }
            
            _needRecalcGeometry = true;
            ActCutCal2D3D();
        }
    }

    /// <summary>
    /// 相对偏移Y
    /// </summary>
    private double _offsetY = 30;
    
    /// <summary>
    /// 相对偏移Y
    /// </summary>
    [Category("位置参数"), DisplayName("Y偏移"), Description("相对于基准位置的Y偏移"), ReadOnly(false)]
    public double OffsetY
    {
        get { return _offsetY; }
        set
        {
            if (Math.Abs(_offsetY - value) < 0.001) return;
            TransManager.Instance().Push(a => OffsetY = a, _offsetY);
            _offsetY = value;
            
            // 偏移改变时重新计算位置
            if (!string.IsNullOrEmpty(_boundTubeId))
            {
                SyncPositionWithBoundTube();
            }
            
            _needRecalcGeometry = true;
            ActCutCal2D3D();
        }
    }

    /// <summary>
    /// 显示大小
    /// </summary>
    private double _displaySize = 8.0;
    
    /// <summary>
    /// 显示大小
    /// </summary>
    [Category("显示参数"), DisplayName("显示大小"), Description("定位点的显示大小"), ReadOnly(false)]
    public double DisplaySize
    {
        get { return _displaySize; }
        set
        {
            if (Math.Abs(_displaySize - value) < 0.001) return;
            TransManager.Instance().Push(a => DisplaySize = a, _displaySize);
            _displaySize = value;
            _needRecalcGeometry = true;
            ActCutCal2D3D();
        }
    }

    /// <summary>
    /// 是否启用
    /// </summary>
    private bool _isEnabled = true;
    
    /// <summary>
    /// 是否启用
    /// </summary>
    [Category("控制参数"), DisplayName("启用状态"), Description("定位点是否启用"), ReadOnly(false)]
    public bool IsEnabled
    {
        get { return _isEnabled; }
        set
        {
            if (_isEnabled == value) return;
            TransManager.Instance().Push(a => IsEnabled = a, _isEnabled);
            _isEnabled = value;
            _needRecalcGeometry = true;
            ActCutCal2D3D();
        }
    }

    /// <summary>
    /// 是否显示
    /// </summary>
    private bool _isVisible = true;
    
    /// <summary>
    /// 是否显示
    /// </summary>
    [Category("显示参数"), DisplayName("显示状态"), Description("定位点是否显示"), ReadOnly(false)]
    public bool IsVisible
    {
        get { return _isVisible; }
        set
        {
            if (_isVisible == value) return;
            TransManager.Instance().Push(a => IsVisible = a, _isVisible);
            _isVisible = value;
            _needRecalcGeometry = true;
            ActCutCal2D3D();
        }
    }
    #endregion

    #region 核心属性
    /// <summary>
    /// 是否需要重新计算几何
    /// </summary>
    private bool _needRecalcGeometry = true;

    /// <summary>
    /// 是否正在进行几何操作
    /// </summary>
    private bool _isGeometryOperation = false;

    /// <summary>
    /// 变换矩阵
    /// </summary>
    private Matrix3D _transformMatrix = Matrix3D.Identity;

    /// <summary>
    /// 是否应用了变换矩阵
    /// </summary>
    private bool _hasTransform = false;
    #endregion

    #region 构造函数
    /// <summary>
    /// 无参构造函数
    /// </summary>
    public TubeLocatorPoint()
    {
        _if3D = false;
        LayerSet("定位点");
    }

    /// <summary>
    /// 标准构造函数
    /// </summary>
    /// <param name="position">定位点位置</param>
    /// <param name="locatorType">定位点类型</param>
    /// <param name="boundTubeId">绑定的矩管ID</param>
    /// <param name="offsetX">X偏移</param>
    /// <param name="offsetY">Y偏移</param>
    public TubeLocatorPoint(DbPt position, TubeLocatorType locatorType = TubeLocatorType.BottomCenter, 
                           string boundTubeId = "", double offsetX = 0, double offsetY = 30)
    {
        _if3D = false;
        LayerSet("定位点");
        
        _position = position.EleCopy();
        _locatorType = locatorType;
        _boundTubeId = boundTubeId;
        _offsetX = offsetX;
        _offsetY = offsetY;
        
        _needRecalcGeometry = true;
        _isGeometryOperation = false;
    }
    #endregion

    #region 核心计算方法
    /// <summary>
    /// 计算控制点
    /// </summary>
    private void CalculateControlPoints()
    {
        ConPts.Clear();
        
        // 添加定位点中心作为控制点
        ConPts.Add(_position.EleCopy());
        
        // 应用变换矩阵
        if (_hasTransform)
        {
            ApplyTransformMatrix();
        }
    }

    /// <summary>
    /// 应用变换矩阵
    /// </summary>
    private void ApplyTransformMatrix()
    {
        if (!_hasTransform || ConPts.Count == 0) return;

        DbPt center = ConPts[0];
        
        // 对控制点应用变换
        for (int i = 0; i < ConPts.Count; i++)
        {
            double relativeX = ConPts[i].X - center.X;
            double relativeY = ConPts[i].Y - center.Y;

            double transformedX = _transformMatrix.M11 * relativeX + _transformMatrix.M12 * relativeY;
            double transformedY = _transformMatrix.M21 * relativeX + _transformMatrix.M22 * relativeY;

            ConPts[i].X = center.X + transformedX;
            ConPts[i].Y = center.Y + transformedY;
        }
    }

    /// <summary>
    /// 生成定位点图形（3mm直径圆+3.5mm中心线）
    /// </summary>
    private void GenerateLocatorGraphics()
    {
        Lines.Clear();
        
        if (!_isVisible || !_isEnabled) return;
        
        DbPt center = _position.EleCopy();
        
        // 定位点参数
        double circleRadius = 1.5; // 3mm直径的圆，半径为1.5mm
        double crossLineHalfLength = 1.75; // 3.5mm中心线的一半长度
        
        // 创建圆形（用多条直线段近似）
        int segments = 16; // 圆形分段16条线段
        for (int i = 0; i < segments; i++)
        {
            double angle1 = 2 * Math.PI * i / segments;
            double angle2 = 2 * Math.PI * (i + 1) / segments;
            
            DbPt pt1 = new DbPt(
                center.X + circleRadius * Math.Cos(angle1),
                center.Y + circleRadius * Math.Sin(angle1)
            );
            DbPt pt2 = new DbPt(
                center.X + circleRadius * Math.Cos(angle2),
                center.Y + circleRadius * Math.Sin(angle2)
            );
            
            DbLine circleLine = new DbLine(pt1, pt2);
            Lines.Add(circleLine);
        }
        
        // 创建十字中心线
        // 水平线
        DbLine horizontalLine = new DbLine(
            new DbPt(center.X - crossLineHalfLength, center.Y),
            new DbPt(center.X + crossLineHalfLength, center.Y)
        );
        Lines.Add(horizontalLine);
        
        // 垂直线
        DbLine verticalLine = new DbLine(
            new DbPt(center.X, center.Y - crossLineHalfLength),
            new DbPt(center.X, center.Y + crossLineHalfLength)
        );
        Lines.Add(verticalLine);
    }

    /// <summary>
    /// 与绑定的矩管同步位置
    /// </summary>
    private void SyncPositionWithBoundTube()
    {
        if (string.IsNullOrEmpty(_boundTubeId)) return;
        
        // 这里需要通过绑定系统获取矩管的位置信息
        // 实际实现时需要调用绑定管理器的方法
        // 暂时保留接口，具体实现在绑定系统中完成
        
        _needRecalcGeometry = true;
    }

    /// <summary>
    /// 通知绑定的矩管更新
    /// </summary>
    private void NotifyBoundTubeUpdate()
    {
        if (string.IsNullOrEmpty(_boundTubeId)) return;
        
        // 这里需要通过绑定系统通知矩管位置已改变
        // 实际实现时需要调用绑定管理器的方法
        // 暂时保留接口，具体实现在绑定系统中完成
    }
    #endregion

    #region 图元操作方法
    /// <summary>
    /// 图元激活计算
    /// </summary>
    public override void Activate()
    {
        Hatchs.Clear();
        Lines.Clear();

        // 根据操作类型决定控制点的处理方式
        if (_needRecalcGeometry || _isGeometryOperation)
        {
            CalculateControlPoints();
            _needRecalcGeometry = false;
        }

        // 生成定位点图形
        GenerateLocatorGraphics();

        // 设置线段属性
        foreach (DbLine line in Lines)
        {
            line.SetStatus(1, 1, 1);
            line.LayerId = PreLayerManage.GetLayerId("定位点");
            line.ColorIndex = 2; // 黄色
            line.StyleIndex = 1; // 实线
        }

        LayerChange(layerManage.GetLayer(_layerId));

        if (_styleIndex >= 0) { foreach (DbLine line in Lines) { line.StyleIndex = _styleIndex; } }
        if (_colorIndex >= 0) { foreach (DbLine line in Lines) { line.ColorIndex = _colorIndex; } }
        if (_widthIndex >= 0) { foreach (DbLine line in Lines) { line.WidthIndex = _widthIndex; } }

        EleArea = new DbEleArea(this);
        CalSolid2D();

        // 重置操作状态
        _isGeometryOperation = false;
    }

    /// <summary>
    /// 返回图元被单独选中时显示的提示内容
    /// </summary>
    public override void EleTips(out string str1, out string str2)
    {
        str1 = "矩管定位点";
        str2 = $"类型:{_locatorType}";
        
        if (!string.IsNullOrEmpty(_boundTubeId))
        {
            str2 += $" [绑定:{_boundTubeId.Substring(0, Math.Min(8, _boundTubeId.Length))}...]";
        }
        else
        {
            str2 += " [未绑定]";
        }
        
        if (!_isEnabled) str2 += " [禁用]";
        if (!_isVisible) str2 += " [隐藏]";
    }

    /// <summary>
    /// 重写移动
    /// </summary>
    public override void EleMove(double X, double Y)
    {
        // 直接移动所有控制点
        foreach (DbPt pt in ConPts)
        {
            pt.MoveSelf(X, Y);
        }

        // 更新位置
        if (ConPts.Count > 0)
        {
            _position = ConPts[0].EleCopy();
        }

        // 通知绑定的矩管更新
        NotifyBoundTubeUpdate();

        _isGeometryOperation = true;
        Activate();
    }

    /// <summary>
    /// 图元拉伸控制
    /// </summary>
    public override void EleMove_s(double X, double Y)
    {
        // 定位点拉伸时执行智能拉伸逻辑
        if (ConPts.Count > 0 && ConPts[0].Status == 1)
        {
            // 移动定位点
            ConPts[0].MoveSelf(X, Y);
            _position = ConPts[0].EleCopy();
            
            // 执行智能拉伸（通过绑定系统）
            PerformSmartStretch(new DbPt(X, Y));
        }

        _isGeometryOperation = true;
        Activate();
    }

    /// <summary>
    /// 执行智能拉伸
    /// </summary>
    /// <param name="delta">拉伸向量</param>
    private void PerformSmartStretch(DbPt delta)
    {
        if (string.IsNullOrEmpty(_boundTubeId)) return;
        
        // 这里需要通过绑定系统执行智能拉伸
        // 实际实现时需要调用绑定管理器的方法
        // 暂时保留接口，具体实现在绑定系统中完成
    }

    /// <summary>
    /// 重写旋转
    /// </summary>
    public override void EleMove_r(DbPt rotCenter, double angle)
    {
        // 旋转所有控制点
        foreach (DbPt pt in ConPts)
        {
            pt.RotateSelf(rotCenter, angle);
        }

        // 更新位置
        if (ConPts.Count > 0)
        {
            _position = ConPts[0].EleCopy();
        }

        _isGeometryOperation = true;
        Activate();
    }

    /// <summary>
    /// 重写镜像
    /// </summary>
    public override void EleMove_m(DbLine mirrorLine)
    {
        // 镜像所有控制点
        foreach (DbPt pt in ConPts)
        {
            pt.MirrorSelf(mirrorLine);
        }

        // 更新位置
        if (ConPts.Count > 0)
        {
            _position = ConPts[0].EleCopy();
        }

        // 更新变换矩阵
        UpdateTransformMatrixForMirror(mirrorLine);

        _isGeometryOperation = true;
        Activate();
    }

    /// <summary>
    /// 更新变换矩阵以包含镜像
    /// </summary>
    private void UpdateTransformMatrixForMirror(DbLine mirrorLine)
    {
        // 计算镜像线的方向向量
        DbPt lineDir = mirrorLine.PtEnd - mirrorLine.PtSt;
        lineDir.Normalize2D();

        // 计算镜像线的法向量
        DbPt normal = new DbPt(-lineDir.Y, lineDir.X);

        // 创建镜像矩阵
        double nx = normal.X;
        double ny = normal.Y;

        Matrix3D mirrorMatrix = new Matrix3D(
            1 - 2 * nx * nx, -2 * nx * ny, 0, 0,
            -2 * nx * ny, 1 - 2 * ny * ny, 0, 0,
            0, 0, 1, 0,
            0, 0, 0, 1
        );

        // 组合变换矩阵
        _transformMatrix = Matrix3D.Multiply(_transformMatrix, mirrorMatrix);
        _hasTransform = true;
    }
    #endregion

    #region 数据持久化
    /// <summary>
    /// 保存数据
    /// </summary>
    public override void DataSave(BinaryWriter binaryWriter)
    {
        // 图元类标识
        binaryWriter.Write(this.GetType().ToString());

        // 版本号
        binaryWriter.Write(1);

        // 图元共有参数
        PubSave(binaryWriter);

        // 图元特有参数
        binaryWriter.Write(_position.X);
        binaryWriter.Write(_position.Y);
        binaryWriter.Write(_position.Z);
        binaryWriter.Write((int)_locatorType);
        binaryWriter.Write(_boundTubeId);
        binaryWriter.Write(_offsetX);
        binaryWriter.Write(_offsetY);
        binaryWriter.Write(_displaySize);
        binaryWriter.Write(_isEnabled);
        binaryWriter.Write(_isVisible);

        // 保存变换矩阵
        binaryWriter.Write(_hasTransform);
        if (_hasTransform)
        {
            binaryWriter.Write(_transformMatrix.M11);
            binaryWriter.Write(_transformMatrix.M12);
            binaryWriter.Write(_transformMatrix.M21);
            binaryWriter.Write(_transformMatrix.M22);
        }
    }

    /// <summary>
    /// 装载数据
    /// </summary>
    public override void DataLoad(BinaryReader binaryReader)
    {
        int verNum = binaryReader.ReadInt32();

        // 图元共有参数
        PubLoad(binaryReader);

        // 图元特有参数
        _position = new DbPt(
            binaryReader.ReadDouble(),
            binaryReader.ReadDouble(),
            binaryReader.ReadDouble()
        );
        _locatorType = (TubeLocatorType)binaryReader.ReadInt32();
        _boundTubeId = binaryReader.ReadString();
        _offsetX = binaryReader.ReadDouble();
        _offsetY = binaryReader.ReadDouble();
        _displaySize = binaryReader.ReadDouble();
        _isEnabled = binaryReader.ReadBoolean();
        _isVisible = binaryReader.ReadBoolean();

        // 加载变换矩阵
        _hasTransform = binaryReader.ReadBoolean();
        if (_hasTransform)
        {
            double m11 = binaryReader.ReadDouble();
            double m12 = binaryReader.ReadDouble();
            double m21 = binaryReader.ReadDouble();
            double m22 = binaryReader.ReadDouble();
            _transformMatrix = new Matrix3D(
                m11, m12, 0, 0,
                m21, m22, 0, 0,
                0, 0, 1, 0,
                0, 0, 0, 1
            );
        }
        else
        {
            _transformMatrix = Matrix3D.Identity;
        }

        // 数据加载后需要重新计算几何
        _needRecalcGeometry = true;
        _isGeometryOperation = false;
    }

    /// <summary>
    /// 深度复制
    /// </summary>
    public override DbElement EleCopy(bool changeUid = false)
    {
        TubeLocatorPoint ele = new TubeLocatorPoint();

        // 图元共有参数
        PubCopy(ele);

        // 图元特有参数
        ele._position = _position.EleCopy();
        ele._locatorType = _locatorType;
        ele._boundTubeId = _boundTubeId;
        ele._offsetX = _offsetX;
        ele._offsetY = _offsetY;
        ele._displaySize = _displaySize;
        ele._isEnabled = _isEnabled;
        ele._isVisible = _isVisible;

        // 复制变换矩阵
        ele._hasTransform = _hasTransform;
        ele._transformMatrix = _transformMatrix;

        // 操作状态不复制
        ele._isGeometryOperation = false;

        if (changeUid) { ele.UniqueId = Guid.NewGuid().ToString(); }

        return ele;
    }
    #endregion

    #region 公共接口方法
    /// <summary>
    /// 绑定到矩管
    /// </summary>
    /// <param name="tubeId">矩管ID</param>
    public void BindToTube(string tubeId)
    {
        _boundTubeId = tubeId;
        SyncPositionWithBoundTube();
    }

    /// <summary>
    /// 解除绑定
    /// </summary>
    public void UnbindFromTube()
    {
        _boundTubeId = "";
    }

    /// <summary>
    /// 设置位置（由绑定系统调用）
    /// </summary>
    /// <param name="newPosition">新位置</param>
    public void SetPosition(DbPt newPosition)
    {
        _position = newPosition.EleCopy();
        _needRecalcGeometry = true;
    }

    /// <summary>
    /// 获取基准位置（不含偏移）
    /// </summary>
    /// <returns>基准位置</returns>
    public DbPt GetBasePosition()
    {
        return new DbPt(_position.X - _offsetX, _position.Y - _offsetY);
    }
    #endregion
} 