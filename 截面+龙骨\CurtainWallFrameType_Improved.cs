using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using GuiDB;
using EBCore;

/// <summary>
/// 🎯 幕墙龙骨截面类型 - 参考结构柱系统架构重新设计
/// 核心理念：通过Type字段支持多种截面类型，统一参数系统，标准几何生成
/// </summary>
[Serializable]
public class CurtainWallFrameType : DbElementType
{
    #region 🔥 核心属性：完全参考结构柱系统

    /// <summary>
    /// 🎯 截面类型编号（核心扩展点）：
    /// 1=圆角矩管, 2=直角矩管, 3=角钢, 4=槽钢, 5=工字钢, 50=自定义
    /// </summary>
    private int _type = 1;
    public int Type 
    { 
        get { return _type; } 
        set 
        { 
            TransManager.Instance().Push(a => Type = a, _type); 
            _type = value;
            // 🔥 关键：Type变化时重新生成几何
            GenerateStandardGeometry();
        } 
    }

    /// <summary>
    /// 🎯 材料类型
    /// </summary>
    private CurtainWallMaterial _material = CurtainWallMaterial.Aluminum;
    public CurtainWallMaterial Material 
    { 
        get { return _material; } 
        set 
        { 
            TransManager.Instance().Push(a => Material = a, _material); 
            _material = value; 
        } 
    }

    /// <summary>
    /// 🎯 统一参数系统（完全参考结构柱）
    /// B=宽度/长边, H=高度/短边, T=壁厚, D=圆角半径
    /// U, F=扩展参数，为后续复杂截面预留
    /// </summary>
    private int _b = 60;
    public int B 
    { 
        get { return _b; } 
        set 
        { 
            TransManager.Instance().Push(a => B = a, _b); 
            _b = value;
            GenerateStandardGeometry(); // 🔥 参数变化时重新生成几何
        } 
    }

    private int _h = 120;
    public int H 
    { 
        get { return _h; } 
        set 
        { 
            TransManager.Instance().Push(a => H = a, _h); 
            _h = value;
            GenerateStandardGeometry();
        } 
    }

    private int _t = 3;
    public int T 
    { 
        get { return _t; } 
        set 
        { 
            TransManager.Instance().Push(a => T = a, _t); 
            _t = value;
            GenerateStandardGeometry();
        } 
    }

    private int _d = 0;
    public int D 
    { 
        get { return _d; } 
        set 
        { 
            TransManager.Instance().Push(a => D = a, _d); 
            _d = value;
            GenerateStandardGeometry();
        } 
    }

    // 🎯 扩展参数（为复杂截面预留）
    public int U { get; set; } = 0;    // 扩展参数1
    public int F { get; set; } = 0;    // 扩展参数2

    /// <summary>
    /// 🎯 标准几何存储（完全参考结构柱）
    /// </summary>
    internal List<DbLine> FrameLines = new List<DbLine>();
    internal List<DbCircle> FrameCircles = new List<DbCircle>();
    internal List<DbPt> FrameOutPts = new List<DbPt>();

    #endregion

    #region 🔥 构造函数：参考结构柱的统一构造方式

    public CurtainWallFrameType() 
    { 
        GenerateStandardGeometry();
    }

    /// <summary>
    /// 🎯 统一构造函数（参考结构柱）
    /// </summary>
    /// <param name="type">截面类型</param>
    /// <param name="b">宽度</param>
    /// <param name="h">高度</param>
    /// <param name="t">壁厚</param>
    /// <param name="d">圆角半径</param>
    /// <param name="u">扩展参数1</param>
    /// <param name="f">扩展参数2</param>
    /// <param name="material">材料</param>
    public CurtainWallFrameType(int type, int b, int h, int t, int d = 0, int u = 0, int f = 0, 
                               CurtainWallMaterial material = CurtainWallMaterial.Aluminum)
    {
        _type = type;
        _material = material;
        _b = b; _h = h; _t = t; _d = d;
        U = u; F = f;
        
        // 🔥 关键：根据type生成对应几何
        GenerateStandardGeometry();
        
        // 🔥 关键：设置默认名称
        Name = GetDefaultSectionName();
    }

    #endregion

    #region 核心方法：标准几何生成（参考结构柱架构）

    /// <summary>
    /// 核心方法：根据Type生成标准几何（参考结构柱的type分支处理）
    /// </summary>
    private void GenerateStandardGeometry()
    {
        // 清空现有几何
        FrameLines.Clear();
        FrameCircles.Clear();
        FrameOutPts.Clear();

        // 核心：根据Type分支生成不同几何（完全参考结构柱模式）
        switch (Type)
        {
            case 1: // 圆角矩管
                GenerateRoundedRectTube();
                Name = $"圆角矩管 {B}×{H}×{T}×R{D}";
                break;
                
            case 2: // 直角矩管
                GenerateStraightRectTube();
                Name = $"直角矩管 {B}×{H}×{T}";
                break;
                
            case 3: // 角钢
                GenerateAngleSteel();
                Name = $"角钢 L{B}×{H}×{T}";
                break;
                
            case 4: // 槽钢
                GenerateChannelSteel();
                Name = $"槽钢 C{B}×{H}×{T}";
                break;
                
            case 5: // 工字钢
                GenerateIBeam();
                Name = $"工字钢 I{B}×{H}×{T}";
                break;
                
            case 50: // 自定义截面
                // 自定义截面由用户绘制，这里不生成几何
                Name = "自定义截面";
                break;
                
            default:
                // 默认按直角矩管处理
                GenerateStraightRectTube();
                Name = $"默认矩管 {B}×{H}×{T}";
                break;
        }
    }

    /// <summary>
    /// 生成圆角矩管几何 - 优化版本（使用GMath.GetOffsetArcPts生成内轮廓）
    /// </summary>
    private void GenerateRoundedRectTube()
    {
        double halfB = B / 2.0;
        double halfH = H / 2.0;
        double radius = Math.Min(D, Math.Min(B, H) / 4.0); // 限制圆角半径

        if (radius <= 0)
        {
            // 无圆角时按直角矩管处理
            GenerateStraightRectTube();
            return;
        }

        // 生成外轮廓点集（包含弧形中点）
        List<DbPt> outerPoints = GenerateRoundedRectanglePoints(halfB, halfH, radius);
        
        // 将外轮廓点转换为线段并添加到FrameLines
        ConvertPointsToLines(outerPoints);
        
        // 内轮廓：使用GMath.GetOffsetArcPts偏移（参考矩管+定位点的实现）
        if (T > 0 && T * 2 < Math.Min(B, H))
        {
            // 现在使用逆时针点集，可以正常使用 true=内偏移
            List<DbPt> innerPoints = GMath.GetOffsetArcPts(outerPoints, T, true);
            if (innerPoints != null && innerPoints.Count > 0)
            {
                ConvertPointsToLines(innerPoints);
            }
        }

        // 存储完整的外轮廓点（包括圆角起点、终点、中点）
        FrameOutPts.Clear();
        FrameOutPts.AddRange(outerPoints);
    }

    /// <summary>
    ///  生成圆角矩形点集（包含弧形中点，逆时针存储，符合GMath.GetOffsetArcPts要求）
    /// </summary>
    private List<DbPt> GenerateRoundedRectanglePoints(double halfB, double halfH, double radius)
    {
        List<DbPt> points = new List<DbPt>();
        
        // 按逆时针方向生成圆角矩形点集（参考矩管+定位点的方式）
        // 路径：左下附近 → 右下附近 → 右上附近 → 左上附近
        
        // 1. 左下角弧线（从左边终点到下边起点）
        DbPt leftBottomArcStart = new DbPt(-halfB, -halfH + radius);    // 左边终点
        DbPt leftBottomArcEnd = new DbPt(-halfB + radius, -halfH);      // 下边起点
        points.Add(leftBottomArcStart);
        
        DbLine leftBottomArc = GMath.GetArcByStCenEnd(
            leftBottomArcStart,
            new DbPt(-halfB + radius, -halfH + radius),
            leftBottomArcEnd
        );
        // 使用官方API计算精确的弧形中点
        DbPt leftBottomMid = GMath.GetArcMidPt(leftBottomArcStart, leftBottomArcEnd, leftBottomArc);
        leftBottomMid.PtType = 1; // 标记为弧形中点
        points.Add(leftBottomMid);
        points.Add(leftBottomArcEnd);
        
        // 2. 下边：左下角弧终点 → 右下角弧起点
        DbPt rightBottomArcStart = new DbPt(halfB - radius, -halfH);
        points.Add(rightBottomArcStart);
        
        // 3. 右下角弧线（从下边终点到右边起点）
        DbPt rightBottomArcEnd = new DbPt(halfB, -halfH + radius);
        DbLine rightBottomArc = GMath.GetArcByStCenEnd(
            rightBottomArcStart,
            new DbPt(halfB - radius, -halfH + radius),
            rightBottomArcEnd
        );
        // 🔥 使用官方API计算精确的弧形中点
        DbPt rightBottomMid = GMath.GetArcMidPt(rightBottomArcStart, rightBottomArcEnd, rightBottomArc);
        rightBottomMid.PtType = 1; // 标记为弧形中点
        points.Add(rightBottomMid);
        points.Add(rightBottomArcEnd);
        
        // 4. 右边：右下角弧终点 → 右上角弧起点
        DbPt rightTopArcStart = new DbPt(halfB, halfH - radius);
        points.Add(rightTopArcStart);
        
        // 5. 右上角弧线（从右边终点到上边起点）
        DbPt rightTopArcEnd = new DbPt(halfB - radius, halfH);
        DbLine rightTopArc = GMath.GetArcByStCenEnd(
            rightTopArcStart,
            new DbPt(halfB - radius, halfH - radius),
            rightTopArcEnd
        );
        // 🔥 使用官方API计算精确的弧形中点
        DbPt rightTopMid = GMath.GetArcMidPt(rightTopArcStart, rightTopArcEnd, rightTopArc);
        rightTopMid.PtType = 1; // 标记为弧形中点
        points.Add(rightTopMid);
        points.Add(rightTopArcEnd);
        
        // 6. 上边：右上角弧终点 → 左上角弧起点
        DbPt leftTopArcStart = new DbPt(-halfB + radius, halfH);
        points.Add(leftTopArcStart);
        
        // 7. 左上角弧线（从上边终点到左边起点）
        DbPt leftTopArcEnd = new DbPt(-halfB, halfH - radius);
        DbLine leftTopArc = GMath.GetArcByStCenEnd(
            leftTopArcStart,
            new DbPt(-halfB + radius, halfH - radius),
            leftTopArcEnd
        );
        // 🔥 使用官方API计算精确的弧形中点
        DbPt leftTopMid = GMath.GetArcMidPt(leftTopArcStart, leftTopArcEnd, leftTopArc);
        leftTopMid.PtType = 1; // 标记为弧形中点
        points.Add(leftTopMid);
        points.Add(leftTopArcEnd);
        
        // 8. 左边：左上角弧终点 → 左下角弧起点（回到起点）
        // 注意：这里不添加起点，因为起点已经在开头添加了
        
        return points;
    }
    
    /// <summary>
    /// 将点集转换为线段并添加到FrameLines（处理弧形中点）
    /// </summary>
    private void ConvertPointsToLines(List<DbPt> points)
    {
        if (points == null || points.Count < 3) return;

        for (int i = 0; i < points.Count; i++)
        {
            DbPt currentPt = points[i];
            DbPt nextPt = points[(i + 1) % points.Count];

            if (nextPt.PtType == 1) // 下一个点是圆弧中点
            {
                // 创建圆弧线段
                DbPt endPt = points[(i + 2) % points.Count];
                DbLine arcLine = new DbLine(currentPt.EleCopy(), endPt.EleCopy(), nextPt.EleCopy());
                FrameLines.Add(arcLine);
                i++; // 跳过圆弧中点
            }
            else
            {
                // 创建直线段
                DbLine line = new DbLine(currentPt.EleCopy(), nextPt.EleCopy());
                FrameLines.Add(line);
            }
        }
    }






    /// <summary>
    /// 生成直角矩管几何
    /// </summary>
    private void GenerateStraightRectTube()
    {
        double halfB = B / 2.0;
        double halfH = H / 2.0;

        // 外轮廓
        GenerateStraightRectangle(halfB, halfH, false);
        
        // 内轮廓（空心）
        if (T > 0 && T * 2 < Math.Min(B, H))
        {
            double innerHalfB = (B - 2 * T) / 2.0;
            double innerHalfH = (H - 2 * T) / 2.0;
            GenerateStraightRectangle(innerHalfB, innerHalfH, true);
        }

        // 存储外轮廓点（直角矩管只有4个角点）
        FrameOutPts.Clear();
        FrameOutPts.Add(new DbPt(halfB, halfH));    // 右上
        FrameOutPts.Add(new DbPt(halfB, -halfH));   // 右下
        FrameOutPts.Add(new DbPt(-halfB, -halfH));  // 左下
        FrameOutPts.Add(new DbPt(-halfB, halfH));   // 左上
    }

    /// <summary>
    /// 生成角钢几何
    /// </summary>
    private void GenerateAngleSteel()
    {
        // L形截面的关键点（以中心为原点）
        var points = new List<DbPt>
        {
            new DbPt(-B/2.0, -H/2.0),                    // 内角点
            new DbPt(B/2.0, -H/2.0),                     // 右下
            new DbPt(B/2.0, -H/2.0 + T),                 // 右下内
            new DbPt(-B/2.0 + T, -H/2.0 + T),            // 内角内点
            new DbPt(-B/2.0 + T, H/2.0),                 // 左上内
            new DbPt(-B/2.0, H/2.0)                      // 左上
        };

        // 添加到轮廓点
        FrameOutPts.AddRange(points);
        
        // 生成线段
        for (int i = 0; i < points.Count; i++)
        {
            int nextIndex = (i + 1) % points.Count;
            FrameLines.Add(new DbLine(points[i], points[nextIndex]));
        }
    }

    /// <summary>
    /// 生成槽钢几何
    /// </summary>
    private void GenerateChannelSteel()
    {
        // C形截面的关键点
        double webThickness = T;      // 腹板厚度
        double flangeWidth = B;       // 翼缘宽度
        double height = H;            // 总高度

        var points = new List<DbPt>
        {
            new DbPt(-flangeWidth/2, -height/2),                    // 左下外
            new DbPt(flangeWidth/2, -height/2),                     // 右下外
            new DbPt(flangeWidth/2, -height/2 + webThickness),      // 右下内
            new DbPt(webThickness/2, -height/2 + webThickness),     // 腹板右下
            new DbPt(webThickness/2, height/2 - webThickness),      // 腹板右上
            new DbPt(flangeWidth/2, height/2 - webThickness),       // 右上内
            new DbPt(flangeWidth/2, height/2),                      // 右上外
            new DbPt(-flangeWidth/2, height/2),                     // 左上外
            new DbPt(-flangeWidth/2, height/2 - webThickness),      // 左上内
            new DbPt(-webThickness/2, height/2 - webThickness),     // 腹板左上
            new DbPt(-webThickness/2, -height/2 + webThickness),    // 腹板左下
            new DbPt(-flangeWidth/2, -height/2 + webThickness)      // 左下内
        };

        FrameOutPts.AddRange(points);
        
        // 生成线段
        for (int i = 0; i < points.Count; i++)
        {
            int nextIndex = (i + 1) % points.Count;
            FrameLines.Add(new DbLine(points[i], points[nextIndex]));
        }
    }

    /// <summary>
    /// 🎯 生成工字钢几何
    /// </summary>
    private void GenerateIBeam()
    {
        // I形截面：上翼缘 + 腹板 + 下翼缘
        double flangeWidth = B;       // 翼缘宽度
        double webHeight = H;         // 腹板高度
        double flangeThickness = T;   // 翼缘厚度
        double webThickness = Math.Max(T/2, 3); // 腹板厚度（通常比翼缘薄）

        var points = new List<DbPt>();
        
        // 下翼缘外轮廓
        points.Add(new DbPt(-flangeWidth/2, -webHeight/2));
        points.Add(new DbPt(flangeWidth/2, -webHeight/2));
        points.Add(new DbPt(flangeWidth/2, -webHeight/2 + flangeThickness));
        
        // 腹板右侧
        points.Add(new DbPt(webThickness/2, -webHeight/2 + flangeThickness));
        points.Add(new DbPt(webThickness/2, webHeight/2 - flangeThickness));
        
        // 上翼缘外轮廓
        points.Add(new DbPt(flangeWidth/2, webHeight/2 - flangeThickness));
        points.Add(new DbPt(flangeWidth/2, webHeight/2));
        points.Add(new DbPt(-flangeWidth/2, webHeight/2));
        points.Add(new DbPt(-flangeWidth/2, webHeight/2 - flangeThickness));
        
        // 腹板左侧
        points.Add(new DbPt(-webThickness/2, webHeight/2 - flangeThickness));
        points.Add(new DbPt(-webThickness/2, -webHeight/2 + flangeThickness));
        points.Add(new DbPt(-flangeWidth/2, -webHeight/2 + flangeThickness));

        FrameOutPts.AddRange(points);
        
        // 生成线段
        for (int i = 0; i < points.Count; i++)
        {
            int nextIndex = (i + 1) % points.Count;
            FrameLines.Add(new DbLine(points[i], points[nextIndex]));
        }
    }

    #endregion

    #region 🔥 辅助几何方法

    /// <summary>
    /// 生成圆角矩形
    /// </summary>
    private void GenerateRoundedRectangle(double halfB, double halfH, double radius, bool isInner)
    {
        // 四个角的圆心
        var corners = new[]
        {
            new DbPt(halfB - radius, halfH - radius),   // 右上
            new DbPt(-halfB + radius, halfH - radius),  // 左上
            new DbPt(-halfB + radius, -halfH + radius), // 左下
            new DbPt(halfB - radius, -halfH + radius)   // 右下
        };

        // 生成圆角（简化处理：用直线段近似）
        for (int i = 0; i < 4; i++)
        {
            double startAngle = i * Math.PI / 2;
            double endAngle = (i + 1) * Math.PI / 2;
            
            int segments = 8; // 每个圆角8段
            for (int j = 0; j < segments; j++)
            {
                double angle1 = startAngle + j * (endAngle - startAngle) / segments;
                double angle2 = startAngle + (j + 1) * (endAngle - startAngle) / segments;
                
                DbPt pt1 = new DbPt(
                    corners[i].X + radius * Math.Cos(angle1),
                    corners[i].Y + radius * Math.Sin(angle1)
                );
                DbPt pt2 = new DbPt(
                    corners[i].X + radius * Math.Cos(angle2),
                    corners[i].Y + radius * Math.Sin(angle2)
                );
                
                FrameLines.Add(new DbLine(pt1, pt2));
            }
        }

        // 生成直边
        FrameLines.Add(new DbLine(new DbPt(halfB - radius, halfH), new DbPt(-halfB + radius, halfH)));     // 上边
        FrameLines.Add(new DbLine(new DbPt(-halfB, halfH - radius), new DbPt(-halfB, -halfH + radius)));   // 左边
        FrameLines.Add(new DbLine(new DbPt(-halfB + radius, -halfH), new DbPt(halfB - radius, -halfH)));   // 下边
        FrameLines.Add(new DbLine(new DbPt(halfB, -halfH + radius), new DbPt(halfB, halfH - radius)));     // 右边
    }

    /// <summary>
    /// 生成直角矩形（顺时针存储）
    /// </summary>
    private void GenerateStraightRectangle(double halfB, double halfH, bool isInner)
    {
        // 按顺时针方向生成线段（从右上角开始）
        FrameLines.Add(new DbLine(halfB, halfH, halfB, -halfH));      // 右边：右上→右下
        FrameLines.Add(new DbLine(halfB, -halfH, -halfB, -halfH));    // 下边：右下→左下
        FrameLines.Add(new DbLine(-halfB, -halfH, -halfB, halfH));    // 左边：左下→左上
        FrameLines.Add(new DbLine(-halfB, halfH, halfB, halfH));      // 上边：左上→右上
    }



    #endregion

    #region  几何变换方法（完全参考结构柱）

    /// <summary>
    /// 旋转和平移标准几何到实际位置（完全参考结构柱的RotateAndMove）
    /// </summary>
    public List<DbLine> RotateAndMove(double angle, double x, double y)
    {
        List<DbLine> actualLines = new List<DbLine>();
        
        foreach (var line in FrameLines)
        {
            DbPt pt1 = GMath.PtRotate(new DbPt(0, 0, 0), line.PtSt, angle);
            DbPt pt2 = GMath.PtRotate(new DbPt(0, 0, 0), line.PtEnd, angle);
            
            pt1 = pt1.Move(x, y);
            pt2 = pt2.Move(x, y);
            
            actualLines.Add(new DbLine(pt1, pt2, line.LayerId));
        }
        
        return actualLines;
    }

    /// <summary>
    /// 🎯 旋转和平移圆形几何
    /// </summary>
    public List<DbCircle> RotateAndMoveCircles(double x, double y)
    {
        List<DbCircle> actualCircles = new List<DbCircle>();
        
        foreach (var circle in FrameCircles)
        {
            DbPt newCenter = circle.CPt.Move(x, y);
            actualCircles.Add(new DbCircle(newCenter, circle.Rad1));
        }
        
        return actualCircles;
    }

    /// <summary>
    /// 🎯 旋转和平移轮廓点
    /// </summary>
    public List<DbPt> RotateAndMovePts(double angle, double x, double y)
    {
        List<DbPt> actualPts = new List<DbPt>();
        
        foreach (var pt in FrameOutPts)
        {
            DbPt rotatedPt = GMath.PtRotate(new DbPt(0, 0, 0), pt, angle);
            actualPts.Add(rotatedPt.Move(x, y));
        }
        
        return actualPts;
    }

    #endregion

    #region 🔥 截面信息方法（参考结构柱）

    /// <summary>
    /// 获取截面名称（参考结构柱的GetSecName）
    /// </summary>
    public override string GetSecName()
    {
        if (!string.IsNullOrEmpty(Name))
        {
            return Name;
        }
        return GetDefaultSectionName();
    }

    /// <summary>
    /// 获取默认截面名称
    /// </summary>
    private string GetDefaultSectionName()
    {
        switch (Type)
        {
            case 1: return D > 0 ? $"圆角矩管 {B}×{H}×{T}×R{D}" : $"圆角矩管 {B}×{H}×{T}";
            case 2: return $"直角矩管 {B}×{H}×{T}";
            case 3: return $"角钢 L{B}×{H}×{T}";
            case 4: return $"槽钢 C{B}×{H}×{T}";
            case 5: return $"工字钢 I{B}×{H}×{T}";
            case 50: return $"自定义截面{B}";
            default: return "未知截面";
        }
    }

    /// <summary>
    /// 获取材料名称
    /// </summary>
    public string GetMaterialName()
    {
        switch (Material)
        {
            case CurtainWallMaterial.Aluminum: return "铝合金";
            case CurtainWallMaterial.Steel: return "钢材";
            case CurtainWallMaterial.StainlessSteel: return "不锈钢";
            case CurtainWallMaterial.CarbonFiber: return "碳纤维";
            default: return "未知材料";
        }
    }

    #endregion

    #region  虚拟截面支持方法（参考结构柱的Clone机制）

    /// <summary>
    /// 深度复制截面类型（用于创建虚拟截面）
    /// </summary>
    public CurtainWallFrameType Clone()
    {
        var clone = new CurtainWallFrameType(Type, B, H, T, D, U, F, Material);
        clone.Name = Name;
        clone.Tag = Tag;
        
        // 注意：不复制UniqueId，虚拟截面不需要全局唯一标识
        return clone;
    }

    /// <summary>
    /// 🎯 判断两个截面是否相同（参考结构柱的IfSame）
    /// </summary>
    public bool IfSame(CurtainWallFrameType other)
    {
        if (other == null) return false;
        return Type == other.Type && B == other.B && H == other.H && 
               T == other.T && U == other.U && D == other.D && F == other.F;
    }

    #endregion

    #region 🔥 数据持久化（参考结构柱的DataSave/DataLoad）

    /// <summary>
    /// 🎯 保存数据（参考结构柱）
    /// </summary>
    public override void DataSave(BinaryWriter binaryWriter)
    {
        base.DataSave(binaryWriter);
        
        // 保存基本属性
        binaryWriter.Write(Type);
        binaryWriter.Write((int)Material);
        
        // 保存统一参数
        binaryWriter.Write(B);
        binaryWriter.Write(H);
        binaryWriter.Write(T);
        binaryWriter.Write(D);
        binaryWriter.Write(U);
        binaryWriter.Write(F);
    }

    /// <summary>
    /// 🎯 加载数据（参考结构柱）
    /// </summary>
    public override void DataLoad(BinaryReader binaryReader)
    {
        base.DataLoad(binaryReader);
        
        // 加载基本属性
        _type = binaryReader.ReadInt32();
        _material = (CurtainWallMaterial)binaryReader.ReadInt32();
        
        // 加载统一参数
        _b = binaryReader.ReadInt32();
        _h = binaryReader.ReadInt32();
        _t = binaryReader.ReadInt32();
        _d = binaryReader.ReadInt32();
        U = binaryReader.ReadInt32();
        F = binaryReader.ReadInt32();
        
        // 🔥 关键：重新生成几何
        GenerateStandardGeometry();
    }

    /// <summary>
    /// 🎯 深度复制（参考结构柱的EleCopy）
    /// </summary>
    public CurtainWallFrameType EleCopy(bool changeUid = false)
    {
        var copy = Clone();
        copy.UniqueId = changeUid ? Guid.NewGuid().ToString() : UniqueId;
        return copy;
    }

    #endregion
}

/// <summary>
/// 🎯 幕墙材料枚举
/// </summary>
public enum CurtainWallMaterial
{
    Aluminum = 1,        // 铝合金
    Steel = 2,           // 钢材
    StainlessSteel = 3,  // 不锈钢
    CarbonFiber = 4      // 碳纤维
} 