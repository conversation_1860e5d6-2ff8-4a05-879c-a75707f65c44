using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

/// <summary>
/// 🎯 数据库使用示例和测试
/// 演示如何使用国标截面数据库系统
/// </summary>
public class DatabaseUsageExamples
{
    private readonly StandardSectionIntegration _integration;
    private readonly DatabaseManager _manager;

    public DatabaseUsageExamples()
    {
        _integration = StandardSectionIntegration.Instance;
        _manager = new DatabaseManager();
    }

    /// <summary>
    /// 运行所有示例
    /// </summary>
    public async Task RunAllExamplesAsync()
    {
        Console.WriteLine("🎯 国标截面数据库使用示例");
        Console.WriteLine("=====================================");
        Console.WriteLine();

        await Example1_BasicUsageAsync();
        Console.WriteLine();
        
        await Example2_FindStandardSectionsAsync();
        Console.WriteLine();
        
        await Example3_SearchAndFilterAsync();
        Console.WriteLine();
        
        await Example4_RecommendationSystemAsync();
        Console.WriteLine();
        
        await Example5_IntegrationWithFrameTypeAsync();
        Console.WriteLine();
        
        await Example6_DataValidationAsync();
        Console.WriteLine();
        
        await Example7_DatabaseManagementAsync();
        Console.WriteLine();
    }

    /// <summary>
    /// 示例1: 基础使用
    /// </summary>
    public async Task Example1_BasicUsageAsync()
    {
        Console.WriteLine("📖 示例1: 基础使用");
        Console.WriteLine("-------------------------------------");

        try
        {
            // 初始化系统
            await _integration.InitializeAsync();
            
            // 获取数据库统计信息
            var statistics = await _integration.GetDatabaseStatisticsAsync();
            Console.WriteLine($"数据库版本: {statistics.DatabaseVersion}");
            Console.WriteLine($"总截面数: {statistics.TotalSections}");
            Console.WriteLine($"材料数: {statistics.TotalMaterials}");
            
            Console.WriteLine();
            Console.WriteLine("截面类型分布:");
            foreach (var typeCount in statistics.SectionTypeCounts)
            {
                Console.WriteLine($"  {typeCount.TypeName}: {typeCount.Count} 个");
            }
            
            // 获取所有截面类型
            var sectionTypes = await _integration.GetAllSectionTypesAsync();
            Console.WriteLine();
            Console.WriteLine("支持的截面类型:");
            foreach (var type in sectionTypes)
            {
                Console.WriteLine($"  {type.TypeCode} - {type.TypeName} ({type.Standard})");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 示例1执行失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 示例2: 查找标准截面
    /// </summary>
    public async Task Example2_FindStandardSectionsAsync()
    {
        Console.WriteLine("🔍 示例2: 查找标准截面");
        Console.WriteLine("-------------------------------------");

        try
        {
            // 查找特定规格的等边角钢
            Console.WriteLine("查找 L50×5 等边角钢:");
            var targetFrameType = new CurtainWallFrameType 
            { 
                Type = 3, // 等边角钢
                B = 50, 
                H = 50, 
                T = 5 
            };
            
            var matchedSection = await _integration.FindMatchingStandardFrameTypeAsync(targetFrameType);
            if (matchedSection != null)
            {
                Console.WriteLine($"✅ 找到匹配截面:");
                Console.WriteLine($"   名称: {matchedSection.Name}");
                Console.WriteLine($"   尺寸: {matchedSection.B}×{matchedSection.H}×{matchedSection.T}mm");
                Console.WriteLine($"   面积: {matchedSection.Area:F2} cm²");
                Console.WriteLine($"   重量: {matchedSection.WeightPerMeter:F2} kg/m");
                Console.WriteLine($"   标准截面ID: {matchedSection.StandardSectionId}");
            }
            else
            {
                Console.WriteLine("❌ 未找到匹配的标准截面");
            }

            Console.WriteLine();
            
            // 验证截面是否为标准规格
            var testSections = new[]
            {
                new { Name = "L75×6", Type = 3, B = 75, H = 75, T = 6 },
                new { Name = "L80×7", Type = 3, B = 80, H = 80, T = 7 },
                new { Name = "L100×999", Type = 3, B = 100, H = 100, T = 999 } // 非标准厚度
            };

            Console.WriteLine("验证截面是否为标准规格:");
            foreach (var test in testSections)
            {
                var frameType = new CurtainWallFrameType { Type = test.Type, B = test.B, H = test.H, T = test.T };
                var isStandard = await _integration.IsStandardSectionAsync(frameType);
                Console.WriteLine($"  {test.Name}: {(isStandard ? "✅ 标准规格" : "❌ 非标准规格")}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 示例2执行失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 示例3: 搜索和过滤
    /// </summary>
    public async Task Example3_SearchAndFilterAsync()
    {
        Console.WriteLine("🔎 示例3: 搜索和过滤");
        Console.WriteLine("-------------------------------------");

        try
        {
            // 获取常用等边角钢
            Console.WriteLine("获取常用等边角钢:");
            var commonAngles = await _integration.GetCommonStandardFrameTypesAsync(3);
            Console.WriteLine($"找到 {commonAngles.Count} 个常用等边角钢:");
            
            foreach (var angle in commonAngles.Take(5)) // 只显示前5个
            {
                var info = await angle.GetStandardInfoAsync();
                if (info != null)
                {
                    Console.WriteLine($"  {info.SectionCode} - {info.WeightPerMeter:F2} kg/m - " +
                                    $"{(info.IsPreferred ? "推荐" : "")}{(info.IsCommon ? "常用" : "")}");
                }
            }

            Console.WriteLine();
            
            // 使用查询条件搜索
            Console.WriteLine("搜索边长在70-100mm之间的等边角钢:");
            var criteria = new SectionQueryCriteria
            {
                TypeCode = "AS", // 等边角钢
                MinB = 70,
                MaxB = 100,
                IsCommon = true // 只要常用规格
            };
            
            var searchResults = await _integration.SearchStandardFrameTypesAsync(criteria);
            Console.WriteLine($"找到 {searchResults.Count} 个匹配的截面:");
            
            foreach (var result in searchResults.Take(8))
            {
                var info = await result.GetStandardInfoAsync();
                if (info != null)
                {
                    Console.WriteLine($"  {info.SectionCode} - 面积:{info.Area:F2}cm² - 重量:{info.WeightPerMeter:F2}kg/m");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 示例3执行失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 示例4: 推荐系统
    /// </summary>
    public async Task Example4_RecommendationSystemAsync()
    {
        Console.WriteLine("💡 示例4: 智能推荐系统");
        Console.WriteLine("-------------------------------------");

        try
        {
            // 创建一个目标截面（非标准规格）
            var targetFrameType = new CurtainWallFrameType
            {
                Type = 3, // 等边角钢
                B = 78,   // 非标准尺寸
                H = 78,
                T = 7,    // 非标准厚度
                Area = 10.5, // 目标面积
                WeightPerMeter = 8.2 // 目标重量
            };

            Console.WriteLine($"目标截面: L{targetFrameType.B}×{targetFrameType.T} (非标准规格)");
            Console.WriteLine($"目标面积: {targetFrameType.Area} cm²");
            Console.WriteLine($"目标重量: {targetFrameType.WeightPerMeter} kg/m");
            Console.WriteLine();

            // 获取推荐的标准截面
            var recommendations = await _integration.GetRecommendedSectionsAsync(targetFrameType, 3);
            
            if (recommendations.Count > 0)
            {
                Console.WriteLine("推荐的标准截面:");
                for (int i = 0; i < recommendations.Count; i++)
                {
                    var rec = recommendations[i];
                    var section = rec.RecommendedSection;
                    
                    Console.WriteLine($"{i + 1}. {section.SectionCode} (匹配度: {rec.MatchScore:F1}%)");
                    Console.WriteLine($"   规格: L{section.B}×{section.T}mm");
                    Console.WriteLine($"   面积: {section.Area:F2} cm² (vs 目标{targetFrameType.Area})");
                    Console.WriteLine($"   重量: {section.WeightPerMeter:F2} kg/m (vs 目标{targetFrameType.WeightPerMeter})");
                    Console.WriteLine($"   推荐理由: {rec.RecommendationReason}");
                    
                    if (rec.Advantages.Count > 0)
                    {
                        Console.WriteLine($"   优点: {string.Join(", ", rec.Advantages)}");
                    }
                    
                    if (rec.Considerations.Count > 0)
                    {
                        Console.WriteLine($"   注意: {string.Join(", ", rec.Considerations)}");
                    }
                    Console.WriteLine();
                }
            }
            else
            {
                Console.WriteLine("❌ 未找到合适的推荐截面");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 示例4执行失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 示例5: 与CurtainWallFrameType集成
    /// </summary>
    public async Task Example5_IntegrationWithFrameTypeAsync()
    {
        Console.WriteLine("🔗 示例5: 与CurtainWallFrameType集成");
        Console.WriteLine("-------------------------------------");

        try
        {
            // 创建一个CurtainWallFrameType实例
            var frameType = new CurtainWallFrameType
            {
                Type = 3, // 等边角钢
                B = 90,
                H = 90,
                T = 8
            };

            Console.WriteLine($"测试截面: L{frameType.B}×{frameType.T}");
            Console.WriteLine();

            // 使用扩展方法查找匹配的标准截面
            var matchedStandard = await frameType.FindMatchingStandardAsync();
            if (matchedStandard != null)
            {
                Console.WriteLine("✅ 找到匹配的标准截面:");
                Console.WriteLine($"   {matchedStandard.Name} - {matchedStandard.WeightPerMeter:F2} kg/m");
            }

            // 验证是否为标准规格
            var isStandard = await frameType.IsStandardAsync();
            Console.WriteLine($"是否标准规格: {(isStandard ? "✅ 是" : "❌ 否")}");

            // 获取详细的标准截面信息
            var standardInfo = await frameType.GetStandardInfoAsync();
            if (standardInfo != null)
            {
                Console.WriteLine();
                Console.WriteLine("标准截面详细信息:");
                Console.WriteLine($"  代号: {standardInfo.SectionCode}");
                Console.WriteLine($"  名称: {standardInfo.SectionName}");
                Console.WriteLine($"  标准: {standardInfo.Standard}");
                Console.WriteLine($"  几何描述: {standardInfo.GetGeometryDescription()}");
                Console.WriteLine($"  重量描述: {standardInfo.GetWeightDescription()}");
                Console.WriteLine($"  惯性矩Ix: {standardInfo.Ix:F2} cm⁴");
                Console.WriteLine($"  惯性矩Iy: {standardInfo.Iy:F2} cm⁴");
                Console.WriteLine($"  截面模量Wx: {standardInfo.Wx:F2} cm³");
                Console.WriteLine($"  截面模量Wy: {standardInfo.Wy:F2} cm³");
                Console.WriteLine($"  回转半径ix: {standardInfo.ix:F2} cm");
                Console.WriteLine($"  回转半径iy: {standardInfo.iy:F2} cm");
                Console.WriteLine($"  形心坐标: ({standardInfo.Xc:F1}, {standardInfo.Yc:F1})mm");
            }

            // 获取推荐截面
            var recommendations = await frameType.GetRecommendationsAsync(2);
            if (recommendations.Count > 0)
            {
                Console.WriteLine();
                Console.WriteLine("相关推荐:");
                foreach (var rec in recommendations)
                {
                    Console.WriteLine($"  {rec.RecommendedSection.SectionCode} - 匹配度:{rec.MatchScore:F1}%");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 示例5执行失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 示例6: 数据验证
    /// </summary>
    public async Task Example6_DataValidationAsync()
    {
        Console.WriteLine("✅ 示例6: 数据验证");
        Console.WriteLine("-------------------------------------");

        try
        {
            // 验证数据库完整性
            var validation = await _manager.ValidateDatabaseIntegrityAsync();
            
            Console.WriteLine($"数据库完整性: {(validation.IsValid ? "✅ 通过" : "❌ 失败")}");
            
            if (validation.Messages.Count > 0)
            {
                Console.WriteLine();
                Console.WriteLine("验证信息:");
                foreach (var message in validation.Messages.Take(5))
                {
                    Console.WriteLine($"  {message}");
                }
            }
            
            if (validation.Issues.Count > 0)
            {
                Console.WriteLine();
                Console.WriteLine("发现的问题:");
                foreach (var issue in validation.Issues.Take(3))
                {
                    Console.WriteLine($"  ⚠️  {issue}");
                }
                
                if (validation.Issues.Count > 3)
                {
                    Console.WriteLine($"  ... 还有 {validation.Issues.Count - 3} 个问题");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 示例6执行失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 示例7: 数据库管理
    /// </summary>
    public async Task Example7_DatabaseManagementAsync()
    {
        Console.WriteLine("🛠️ 示例7: 数据库管理");
        Console.WriteLine("-------------------------------------");

        try
        {
            // 获取数据库统计信息
            var statistics = await _integration.GetDatabaseStatisticsAsync();
            Console.WriteLine($"当前数据库状态:");
            Console.WriteLine($"  版本: {statistics.DatabaseVersion}");
            Console.WriteLine($"  截面总数: {statistics.TotalSections}");
            Console.WriteLine($"  材料总数: {statistics.TotalMaterials}");

            // 创建备份
            Console.WriteLine();
            Console.WriteLine("创建数据库备份...");
            var backupResult = await _manager.BackupDatabaseAsync("example_backup");
            if (backupResult.IsSuccess)
            {
                Console.WriteLine($"✅ 备份成功: {backupResult.BackupPath}");
                Console.WriteLine($"   大小: {FormatBytes(backupResult.BackupSize)}");
            }
            else
            {
                Console.WriteLine($"❌ 备份失败: {backupResult.ErrorMessage}");
            }

            // 列出可用备份
            var backups = _manager.GetAvailableBackups();
            Console.WriteLine();
            Console.WriteLine($"可用备份数量: {backups.Count}");
            if (backups.Count > 0)
            {
                Console.WriteLine("最近的备份:");
                foreach (var backup in backups.Take(3))
                {
                    Console.WriteLine($"  {backup.FileName} - {backup.SizeFormatted} - {backup.CreatedDate:yyyy-MM-dd HH:mm}");
                }
            }

            // 优化数据库
            Console.WriteLine();
            Console.WriteLine("优化数据库...");
            var optimizeResult = await _manager.OptimizeDatabaseAsync();
            if (optimizeResult.IsSuccess)
            {
                Console.WriteLine($"✅ 优化完成，节省空间: {FormatBytes(optimizeResult.SpaceSaved)}");
            }
            else
            {
                Console.WriteLine($"❌ 优化失败: {optimizeResult.ErrorMessage}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 示例7执行失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 格式化字节数
    /// </summary>
    private string FormatBytes(long bytes)
    {
        if (bytes < 1024) return $"{bytes} B";
        if (bytes < 1024 * 1024) return $"{bytes / 1024.0:F1} KB";
        if (bytes < 1024 * 1024 * 1024) return $"{bytes / (1024.0 * 1024):F1} MB";
        return $"{bytes / (1024.0 * 1024 * 1024):F1} GB";
    }
}

/// <summary>
/// 示例程序入口
/// </summary>
public class DatabaseExamplesProgram
{
    public static async Task Main(string[] args)
    {
        try
        {
            var examples = new DatabaseUsageExamples();
            await examples.RunAllExamplesAsync();
            
            Console.WriteLine();
            Console.WriteLine("🎉 所有示例执行完毕！");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 程序执行出错: {ex.Message}");
        }

        Console.WriteLine();
        Console.WriteLine("按任意键退出...");
        Console.ReadKey();
    }
}