
-- cold_formed_sections_schema.sql
-- Schema for storing cold-formed steel section properties (GB50018 and others).
-- Units convention (recommended):
--   Geometry: mm
--   Area: mm^2
--   Second moments of area (I): mm^4
--   Section moduli (W): mm^3
--   Torsion constant (J): mm^4
--   Warping constant (Cw): mm^6
-- You can ingest data in other units (e.g., cm2/cm3/cm4) via the import script; it will convert.

PRAGMA foreign_keys = ON;

-- Source/standard info (where the numbers come from)
CREATE TABLE IF NOT EXISTS sources (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,            -- e.g., 'GB50018'
    edition TEXT,                  -- e.g., '2018', '2002(2008)'
    publisher TEXT,                -- optional
    note TEXT
);

-- Material table (optional; for yield strength etc.)
CREATE TABLE IF NOT EXISTS materials (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    grade TEXT NOT NULL,           -- e.g., 'Q345', 'Q235B'
    fy_MPa REAL,                   -- yield strength
    fu_MPa REAL,                   -- ultimate strength
    E_MPa REAL,                    -- <PERSON>'s modulus
    G_MPa REAL,                    -- shear modulus
    density_kg_m3 REAL            -- density
);

-- Section master table (one row per geometric size)
CREATE TABLE IF NOT EXISTS sections (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    section_name TEXT NOT NULL,    -- e.g., 'C160×60×20×2.0'
    type TEXT NOT NULL,            -- 'C','Z','U','RHS','CHS','LIP_CHANNEL','OTHER'
    standard TEXT,                 -- e.g., 'GB50018'
    source_id INTEGER,             -- FK to sources.id
    material_id INTEGER,           -- optional default material
    -- Geometry (nullable depending on type)
    h_mm REAL,                     -- web depth / height
    b_mm REAL,                     -- flange width
    c_mm REAL,                     -- lip length (for lipped sections)
    d_mm REAL,                     -- secondary depth / width if needed
    t_mm REAL,                     -- thickness
    r_in_mm REAL,                  -- inner radius (if applicable)
    r_out_mm REAL,                 -- outer radius (optional)
    dims_json TEXT,                -- extra dimensions in JSON if needed
    -- Metadata
    unique_key TEXT,               -- unique alias, if any
    UNIQUE(section_name, standard),
    FOREIGN KEY (source_id) REFERENCES sources(id) ON DELETE SET NULL,
    FOREIGN KEY (material_id) REFERENCES materials(id) ON DELETE SET NULL
);

-- Section properties (can keep multiple records per section for different calc assumptions)
CREATE TABLE IF NOT EXISTS section_properties (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    section_id INTEGER NOT NULL,   -- FK to sections.id
    calc_method TEXT,              -- e.g., 'GB50018 Annex B', 'Manufacturer', 'Software'
    -- Geometric properties (units see header)
    A_mm2 REAL,
    Ix_mm4 REAL, Iy_mm4 REAL,
    Wx_mm3 REAL, Wy_mm3 REAL,
    rx_mm REAL, ry_mm REAL,        -- radii of gyration
    J_mm4 REAL,                    -- torsion constant (Saint-Venant)
    Cw_mm6 REAL,                   -- warping constant
    x0_mm REAL, y0_mm REAL,        -- shear center offsets (optional)
    note TEXT,
    created_at TEXT DEFAULT (datetime('now')),
    FOREIGN KEY (section_id) REFERENCES sections(id) ON DELETE CASCADE
);

-- Helpful indexes for fast lookup
CREATE INDEX IF NOT EXISTS idx_sections_type ON sections(type);
CREATE INDEX IF NOT EXISTS idx_sections_h ON sections(h_mm);
CREATE INDEX IF NOT EXISTS idx_sections_t ON sections(t_mm);
CREATE INDEX IF NOT EXISTS idx_props_section ON section_properties(section_id);
CREATE INDEX IF NOT EXISTS idx_props_Wx ON section_properties(Wx_mm3);
CREATE INDEX IF NOT EXISTS idx_props_Wy ON section_properties(Wy_mm3);
