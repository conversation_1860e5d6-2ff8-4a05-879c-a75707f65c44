using System;

/// <summary>
/// 定位点系统测试示例
/// 演示修改后的定位点系统的使用方法
/// </summary>
public class LocatorSystemTestExample
{
    /// <summary>
    /// 测试定位点的基本开启和关闭功能
    /// </summary>
    public static void TestBasicLocatorOnOff()
    {
        Console.WriteLine("=== 测试定位点基本开启/关闭功能 ===");
        
        // 创建矩管实例
        var tube = new SteelTube(new DbPt(0, 0), 60, 120, 3);
        
        Console.WriteLine($"初始状态 - 定位点开启: {tube.LocatorEnabled}");
        Console.WriteLine($"初始状态 - ConPts数量: {tube.ConPts.Count}");
        
        // 开启定位点
        tube.LocatorEnabled = true;
        Console.WriteLine($"开启后 - 定位点开启: {tube.LocatorEnabled}");
        Console.WriteLine($"开启后 - ConPts数量: {tube.ConPts.Count}");
        Console.WriteLine($"开启后 - 最后一个点的PtType: {tube.ConPts[tube.ConPts.Count - 1].PtType}");
        
        // 关闭定位点
        tube.LocatorEnabled = false;
        Console.WriteLine($"关闭后 - 定位点开启: {tube.LocatorEnabled}");
        Console.WriteLine($"关闭后 - ConPts数量: {tube.ConPts.Count}");
        
        // 重新开启定位点
        tube.LocatorEnabled = true;
        Console.WriteLine($"重新开启后 - 定位点开启: {tube.LocatorEnabled}");
        Console.WriteLine($"重新开启后 - ConPts数量: {tube.ConPts.Count}");
        Console.WriteLine($"重新开启后 - 最后一个点的PtType: {tube.ConPts[tube.ConPts.Count - 1].PtType}");
        
        Console.WriteLine("✓ 基本开启/关闭功能测试通过\n");
    }
    
    /// <summary>
    /// 测试定位点类型切换功能
    /// </summary>
    public static void TestLocatorTypeSwitch()
    {
        Console.WriteLine("=== 测试定位点类型切换功能 ===");
        
        var tube = new SteelTube(new DbPt(0, 0), 60, 120, 3);
        tube.LocatorEnabled = true;
        
        // 测试不同的定位点类型
        var locatorTypes = new[]
        {
            LocatorPointType.BottomCenter,
            LocatorPointType.TopCenter,
            LocatorPointType.LeftCenter,
            LocatorPointType.RightCenter,
            LocatorPointType.GeometryCenter
        };
        
        foreach (var locatorType in locatorTypes)
        {
            tube.ActiveLocatorType = locatorType;
            var locatorPoint = tube.ConPts[tube.ConPts.Count - 1];
            
            Console.WriteLine($"定位点类型: {locatorType}");
            Console.WriteLine($"定位点位置: ({locatorPoint.X:F2}, {locatorPoint.Y:F2})");
            Console.WriteLine($"定位点标记: PtType={locatorPoint.PtType}");
            Console.WriteLine();
        }
        
        Console.WriteLine("✓ 定位点类型切换功能测试通过\n");
    }
    
    /// <summary>
    /// 测试定位点偏移功能
    /// </summary>
    public static void TestLocatorOffset()
    {
        Console.WriteLine("=== 测试定位点偏移功能 ===");
        
        var tube = new SteelTube(new DbPt(0, 0), 60, 120, 3);
        tube.LocatorEnabled = true;
        tube.ActiveLocatorType = LocatorPointType.BottomCenter;
        
        // 测试不同的偏移值
        var offsets = new[]
        {
            new { X = 0.0, Y = 0.0 },
            new { X = 10.0, Y = 20.0 },
            new { X = -5.0, Y = 15.0 },
            new { X = 0.0, Y = 30.0 }
        };
        
        foreach (var offset in offsets)
        {
            tube.LocatorOffsetX = offset.X;
            tube.LocatorOffsetY = offset.Y;
            
            var locatorPoint = tube.ConPts[tube.ConPts.Count - 1];
            
            Console.WriteLine($"偏移设置: X={offset.X}, Y={offset.Y}");
            Console.WriteLine($"定位点位置: ({locatorPoint.X:F2}, {locatorPoint.Y:F2})");
            Console.WriteLine();
        }
        
        Console.WriteLine("✓ 定位点偏移功能测试通过\n");
    }
    
    /// <summary>
    /// 测试重复开启关闭功能
    /// </summary>
    public static void TestRepeatedOnOff()
    {
        Console.WriteLine("=== 测试重复开启/关闭功能 ===");
        
        var tube = new SteelTube(new DbPt(0, 0), 60, 120, 3);
        
        // 重复开启关闭10次
        for (int i = 0; i < 10; i++)
        {
            // 开启
            tube.LocatorEnabled = true;
            bool hasLocatorAfterOn = tube.ConPts.Count > 0 && tube.ConPts[tube.ConPts.Count - 1].PtType == 99;
            
            // 关闭
            tube.LocatorEnabled = false;
            bool hasLocatorAfterOff = tube.ConPts.Count > 0 && tube.ConPts[tube.ConPts.Count - 1].PtType == 99;
            
            Console.WriteLine($"第{i + 1}次 - 开启后有定位点: {hasLocatorAfterOn}, 关闭后有定位点: {hasLocatorAfterOff}");
            
            if (!hasLocatorAfterOn || hasLocatorAfterOff)
            {
                Console.WriteLine("❌ 重复开启/关闭功能测试失败");
                return;
            }
        }
        
        Console.WriteLine("✓ 重复开启/关闭功能测试通过\n");
    }
    
    /// <summary>
    /// 测试矩管变换时定位点的跟随
    /// </summary>
    public static void TestLocatorTransform()
    {
        Console.WriteLine("=== 测试定位点变换跟随功能 ===");
        
        var tube = new SteelTube(new DbPt(0, 0), 60, 120, 3);
        tube.LocatorEnabled = true;
        tube.ActiveLocatorType = LocatorPointType.BottomCenter;
        
        // 记录初始位置
        var initialLocatorPos = tube.ConPts[tube.ConPts.Count - 1].EleCopy();
        Console.WriteLine($"初始定位点位置: ({initialLocatorPos.X:F2}, {initialLocatorPos.Y:F2})");
        
        // 测试移动
        tube.EleMove(10, 20);
        var afterMovePos = tube.ConPts[tube.ConPts.Count - 1];
        Console.WriteLine($"移动后定位点位置: ({afterMovePos.X:F2}, {afterMovePos.Y:F2})");
        
        // 测试旋转
        tube.EleMove_r(new DbPt(0, 0), Math.PI / 4); // 45度旋转
        var afterRotatePos = tube.ConPts[tube.ConPts.Count - 1];
        Console.WriteLine($"旋转后定位点位置: ({afterRotatePos.X:F2}, {afterRotatePos.Y:F2})");
        
        // 验证定位点仍然存在且标记正确
        bool hasLocatorAfterTransform = tube.ConPts.Count > 0 && tube.ConPts[tube.ConPts.Count - 1].PtType == 99;
        Console.WriteLine($"变换后定位点存在: {hasLocatorAfterTransform}");
        
        if (hasLocatorAfterTransform)
        {
            Console.WriteLine("✓ 定位点变换跟随功能测试通过\n");
        }
        else
        {
            Console.WriteLine("❌ 定位点变换跟随功能测试失败\n");
        }
    }
    
    /// <summary>
    /// 运行所有测试
    /// </summary>
    public static void RunAllTests()
    {
        Console.WriteLine("开始运行定位点系统测试...\n");
        
        try
        {
            TestBasicLocatorOnOff();
            TestLocatorTypeSwitch();
            TestLocatorOffset();
            TestRepeatedOnOff();
            TestLocatorTransform();
            
            Console.WriteLine("🎉 所有测试通过！定位点系统工作正常。");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 测试过程中发生错误: {ex.Message}");
            Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
        }
    }
} 