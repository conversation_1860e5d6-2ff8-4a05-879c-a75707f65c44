-- 🎯 国标截面数据库表结构设计
-- 数据库文件名: StandardSections.db
-- 版本: 1.0
-- 标准: GB/T 706-2008, GB/T 11263-2017 等

-- ======================================
-- 1. 截面类型主表
-- ======================================
CREATE TABLE SectionTypes (
    TypeId INTEGER PRIMARY KEY,
    TypeCode VARCHAR(10) NOT NULL UNIQUE,    -- 类型代码: AS(等边角钢), UAS(不等边角钢), CS(槽钢), LCS(轻型槽钢), etc.
    TypeName VARCHAR(50) NOT NULL,           -- 类型名称: 等边角钢, 不等边角钢, 普通槽钢, 轻型槽钢
    TypeNameEn VARCHAR(50),                  -- 英文名称: Equal Angle Steel
    Standard VARCHAR(20) NOT NULL,           -- 适用标准: GB/T 706-2008
    Description TEXT,                        -- 描述信息
    IsActive BOOLEAN DEFAULT 1,              -- 是否启用
    SortOrder INTEGER DEFAULT 0,             -- 显示顺序
    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
    UpdatedDate DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 插入基础数据
INSERT INTO SectionTypes (TypeId, TypeCode, TypeName, TypeNameEn, Standard, Description, SortOrder) VALUES
(1, 'AS', '等边角钢', 'Equal Angle Steel', 'GB/T 706-2008', '等边角钢截面', 1),
(2, 'UAS', '不等边角钢', 'Unequal Angle Steel', 'GB/T 706-2008', '不等边角钢截面', 2),
(3, 'CS', '普通槽钢', 'Channel Steel', 'GB/T 706-2008', '普通槽钢截面', 3),
(4, 'LCS', '轻型槽钢', 'Light Channel Steel', 'GB/T 11263-2017', '轻型槽钢截面', 4),
(5, 'HS', '工字钢', 'H-Section Steel', 'GB/T 706-2008', '工字钢截面', 5),
(6, 'LHS', '轻型工字钢', 'Light H-Section Steel', 'GB/T 11263-2017', '轻型工字钢截面', 6),
(7, 'RTS', '矩形钢管', 'Rectangular Tube Steel', 'GB/T 6728-2017', '矩形钢管截面', 7),
(8, 'STS', '方形钢管', 'Square Tube Steel', 'GB/T 6728-2017', '方形钢管截面', 8),
(9, 'CTS', '圆形钢管', 'Circular Tube Steel', 'GB/T 8162-2018', '圆形钢管截面', 9),
(10, 'CUSTOM', '自定义截面', 'Custom Section', 'User Defined', '用户自定义截面', 99);

-- ======================================
-- 2. 标准截面数据主表
-- ======================================
CREATE TABLE StandardSections (
    SectionId INTEGER PRIMARY KEY AUTOINCREMENT,
    TypeId INTEGER NOT NULL,                     -- 关联截面类型
    SectionCode VARCHAR(20) NOT NULL,            -- 截面代号: L50×5, L75×50×6, [12.6, etc.
    SectionName VARCHAR(50) NOT NULL,            -- 截面名称: 等边角钢L50×5
    
    -- 🔥 统一几何参数 (单位: mm)
    B REAL NOT NULL DEFAULT 0,                   -- 宽度/长边/外径
    H REAL NOT NULL DEFAULT 0,                   -- 高度/短边/壁厚(管材)
    T REAL NOT NULL DEFAULT 0,                   -- 厚度/壁厚
    T1 REAL DEFAULT 0,                          -- 翼缘厚度(工字钢)
    R REAL DEFAULT 0,                           -- 圆角半径
    R1 REAL DEFAULT 0,                          -- 内圆角半径
    D REAL DEFAULT 0,                           -- 扩展参数1
    U REAL DEFAULT 0,                           -- 扩展参数2
    F REAL DEFAULT 0,                           -- 扩展参数3
    
    -- 🔥 截面特性 (单位: cm²、cm⁴、cm³、cm、kg/m)
    Area REAL NOT NULL DEFAULT 0,               -- 截面面积 (cm²)
    WeightPerMeter REAL NOT NULL DEFAULT 0,     -- 理论重量 (kg/m)
    
    -- 惯性矩 (cm⁴)
    Ix REAL DEFAULT 0,                          -- X轴惯性矩
    Iy REAL DEFAULT 0,                          -- Y轴惯性矩
    Iz REAL DEFAULT 0,                          -- Z轴惯性矩 (扭转)
    
    -- 截面模量 (cm³)
    Wx REAL DEFAULT 0,                          -- X轴截面模量
    Wy REAL DEFAULT 0,                          -- Y轴截面模量
    
    -- 回转半径 (cm)
    ix REAL DEFAULT 0,                          -- X轴回转半径
    iy REAL DEFAULT 0,                          -- Y轴回转半径
    iz REAL DEFAULT 0,                          -- Z轴回转半径
    
    -- 形心坐标 (mm，相对于截面左下角)
    Xc REAL DEFAULT 0,                          -- 形心X坐标
    Yc REAL DEFAULT 0,                          -- 形心Y坐标
    
    -- 🔥 元数据
    IsCommon BOOLEAN DEFAULT 0,                  -- 是否常用规格
    IsPreferred BOOLEAN DEFAULT 0,               -- 是否推荐规格
    MinLength REAL DEFAULT 0,                   -- 最小长度 (mm)
    MaxLength REAL DEFAULT 0,                   -- 最大长度 (mm)
    Tolerance VARCHAR(10),                       -- 公差等级
    SurfaceTreatment VARCHAR(50),                -- 表面处理
    Remarks TEXT,                                -- 备注信息
    
    -- 系统字段
    IsActive BOOLEAN DEFAULT 1,
    SortOrder INTEGER DEFAULT 0,
    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
    UpdatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (TypeId) REFERENCES SectionTypes(TypeId),
    -- 唯一约束
    UNIQUE(TypeId, SectionCode)
);

-- 创建索引
CREATE INDEX idx_sections_type ON StandardSections(TypeId);
CREATE INDEX idx_sections_code ON StandardSections(SectionCode);
CREATE INDEX idx_sections_name ON StandardSections(SectionName);
CREATE INDEX idx_sections_common ON StandardSections(IsCommon);
CREATE INDEX idx_sections_size ON StandardSections(B, H, T);

-- ======================================
-- 3. 截面几何参数定义表
-- ======================================
CREATE TABLE SectionParameters (
    ParameterId INTEGER PRIMARY KEY AUTOINCREMENT,
    TypeId INTEGER NOT NULL,                    -- 关联截面类型
    ParameterCode VARCHAR(10) NOT NULL,         -- 参数代码: B, H, T, R, etc.
    ParameterName VARCHAR(50) NOT NULL,         -- 参数名称: 宽度, 高度, 厚度
    ParameterNameEn VARCHAR(50),               -- 英文名称: Width, Height, Thickness
    Unit VARCHAR(10) DEFAULT 'mm',             -- 单位
    Description TEXT,                           -- 参数描述
    IsRequired BOOLEAN DEFAULT 1,              -- 是否必需参数
    DefaultValue REAL DEFAULT 0,               -- 默认值
    MinValue REAL,                             -- 最小值
    MaxValue REAL,                             -- 最大值
    Precision INTEGER DEFAULT 1,               -- 精度 (小数位数)
    SortOrder INTEGER DEFAULT 0,
    
    FOREIGN KEY (TypeId) REFERENCES SectionTypes(TypeId),
    UNIQUE(TypeId, ParameterCode)
);

-- 插入参数定义数据
INSERT INTO SectionParameters (TypeId, ParameterCode, ParameterName, ParameterNameEn, Unit, Description, IsRequired, DefaultValue, SortOrder) VALUES
-- 等边角钢参数
(1, 'B', '边长', 'Side Length', 'mm', '等边角钢边长', 1, 50, 1),
(1, 'T', '厚度', 'Thickness', 'mm', '角钢厚度', 1, 5, 2),
(1, 'R', '内圆角半径', 'Inner Radius', 'mm', '内圆角半径', 0, 0, 3),
(1, 'R1', '端部圆角半径', 'End Radius', 'mm', '端部圆角半径，通常为t/3', 0, 0, 4),

-- 不等边角钢参数
(2, 'B', '长边', 'Long Side', 'mm', '不等边角钢长边', 1, 63, 1),
(2, 'H', '短边', 'Short Side', 'mm', '不等边角钢短边', 1, 40, 2),
(2, 'T', '厚度', 'Thickness', 'mm', '角钢厚度', 1, 5, 3),
(2, 'R', '内圆角半径', 'Inner Radius', 'mm', '内圆角半径', 0, 0, 4),
(2, 'R1', '端部圆角半径', 'End Radius', 'mm', '端部圆角半径', 0, 0, 5),

-- 普通槽钢参数
(3, 'H', '高度', 'Height', 'mm', '槽钢高度', 1, 100, 1),
(3, 'B', '翼缘宽度', 'Flange Width', 'mm', '翼缘宽度', 1, 48, 2),
(3, 'T', '腹板厚度', 'Web Thickness', 'mm', '腹板厚度', 1, 5.3, 3),
(3, 'T1', '翼缘厚度', 'Flange Thickness', 'mm', '翼缘厚度', 1, 7.5, 4),
(3, 'R', '内圆角半径', 'Inner Radius', 'mm', '内圆角半径', 0, 8.5, 5),

-- 轻型槽钢参数
(4, 'H', '高度', 'Height', 'mm', '槽钢高度', 1, 50, 1),
(4, 'B', '翼缘宽度', 'Flange Width', 'mm', '翼缘宽度', 1, 32, 2),
(4, 'T', '腹板厚度', 'Web Thickness', 'mm', '腹板厚度', 1, 3, 3),
(4, 'T1', '翼缘厚度', 'Flange Thickness', 'mm', '翼缘厚度', 1, 4.5, 4),
(4, 'R', '内圆角半径', 'Inner Radius', 'mm', '内圆角半径', 0, 3.5, 5);

-- ======================================
-- 4. 材料属性表
-- ======================================
CREATE TABLE Materials (
    MaterialId INTEGER PRIMARY KEY AUTOINCREMENT,
    MaterialCode VARCHAR(20) NOT NULL UNIQUE,   -- 材料代号: Q235, Q355, etc.
    MaterialName VARCHAR(50) NOT NULL,          -- 材料名称
    MaterialNameEn VARCHAR(50),                 -- 英文名称
    Standard VARCHAR(20),                       -- 适用标准: GB/T 700-2006
    
    -- 物理性能
    Density REAL DEFAULT 7850,                 -- 密度 (kg/m³)
    ElasticModulus REAL DEFAULT 206000,        -- 弹性模量 (MPa)
    ShearModulus REAL DEFAULT 79000,           -- 剪切模量 (MPa)
    PoissonRatio REAL DEFAULT 0.3,             -- 泊松比
    
    -- 力学性能
    YieldStrength REAL DEFAULT 235,            -- 屈服强度 (MPa)
    TensileStrength REAL DEFAULT 370,          -- 抗拉强度 (MPa)
    
    -- 热学性能
    ThermalExpansion REAL DEFAULT 12e-6,       -- 线膨胀系数 (1/°C)
    ThermalConductivity REAL DEFAULT 50,       -- 导热系数 (W/m·K)
    
    Description TEXT,
    IsActive BOOLEAN DEFAULT 1,
    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
    UpdatedDate DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 插入常用材料数据
INSERT INTO Materials (MaterialCode, MaterialName, MaterialNameEn, Standard, Density, YieldStrength, TensileStrength, Description) VALUES
('Q235', 'Q235碳素结构钢', 'Q235 Carbon Structural Steel', 'GB/T 700-2006', 7850, 235, 370, '最常用的碳素结构钢'),
('Q355', 'Q355低合金高强度结构钢', 'Q355 Low Alloy High Strength Steel', 'GB/T 1591-2018', 7850, 355, 470, '低合金高强度结构钢'),
('Q420', 'Q420低合金高强度结构钢', 'Q420 Low Alloy High Strength Steel', 'GB/T 1591-2018', 7850, 420, 520, '高强度结构钢'),
('20#', '20号碳素结构钢', '20# Carbon Steel', 'GB/T 700-2006', 7850, 245, 410, '20号优质碳素结构钢'),
('6061', '6061铝合金', '6061 Aluminum Alloy', 'GB/T 3190-2008', 2700, 240, 290, '6061-T6铝合金'),
('6063', '6063铝合金', '6063 Aluminum Alloy', 'GB/T 3190-2008', 2700, 170, 205, '6063-T5铝合金');

-- ======================================
-- 5. 截面-材料关联表
-- ======================================
CREATE TABLE SectionMaterials (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    SectionId INTEGER NOT NULL,
    MaterialId INTEGER NOT NULL,
    IsDefault BOOLEAN DEFAULT 0,               -- 是否默认材料
    
    FOREIGN KEY (SectionId) REFERENCES StandardSections(SectionId),
    FOREIGN KEY (MaterialId) REFERENCES Materials(MaterialId),
    UNIQUE(SectionId, MaterialId)
);

-- ======================================
-- 6. 用户自定义截面表
-- ======================================
CREATE TABLE UserSections (
    UserSectionId INTEGER PRIMARY KEY AUTOINCREMENT,
    UserId VARCHAR(50),                        -- 用户ID (可选)
    ProjectId VARCHAR(50),                     -- 项目ID (可选)
    
    SectionName VARCHAR(100) NOT NULL,        -- 截面名称
    BaseTypeId INTEGER,                        -- 基于的标准类型
    
    -- 几何参数 (与StandardSections相同结构)
    B REAL DEFAULT 0,
    H REAL DEFAULT 0,
    T REAL DEFAULT 0,
    T1 REAL DEFAULT 0,
    R REAL DEFAULT 0,
    R1 REAL DEFAULT 0,
    D REAL DEFAULT 0,
    U REAL DEFAULT 0,
    F REAL DEFAULT 0,
    
    -- 截面特性
    Area REAL DEFAULT 0,
    WeightPerMeter REAL DEFAULT 0,
    Ix REAL DEFAULT 0,
    Iy REAL DEFAULT 0,
    Wx REAL DEFAULT 0,
    Wy REAL DEFAULT 0,
    ix REAL DEFAULT 0,
    iy REAL DEFAULT 0,
    Xc REAL DEFAULT 0,
    Yc REAL DEFAULT 0,
    
    -- 自定义几何数据 (JSON格式存储复杂几何)
    GeometryData TEXT,                         -- JSON格式的几何定义
    PreviewImage BLOB,                         -- 预览图片
    
    Description TEXT,
    Tags VARCHAR(200),                         -- 标签，用逗号分隔
    IsTemplate BOOLEAN DEFAULT 0,             -- 是否为模板
    IsShared BOOLEAN DEFAULT 0,               -- 是否共享
    
    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
    UpdatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (BaseTypeId) REFERENCES SectionTypes(TypeId)
);

-- ======================================
-- 7. 数据库版本管理表
-- ======================================
CREATE TABLE DatabaseInfo (
    Key VARCHAR(50) PRIMARY KEY,
    Value TEXT,
    UpdatedDate DATETIME DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO DatabaseInfo (Key, Value) VALUES
('Version', '1.0'),
('CreatedDate', datetime('now')),
('Description', '国标截面数据库 - 包含角钢、槽钢、工字钢等标准截面'),
('LastUpdateDate', datetime('now')),
('DataSource', 'GB/T 706-2008, GB/T 11263-2017');

-- ======================================
-- 8. 视图定义
-- ======================================

-- 截面详细信息视图
CREATE VIEW v_SectionDetails AS
SELECT 
    s.SectionId,
    s.SectionCode,
    s.SectionName,
    st.TypeCode,
    st.TypeName,
    st.Standard,
    s.B, s.H, s.T, s.T1, s.R, s.R1,
    s.Area,
    s.WeightPerMeter,
    s.Ix, s.Iy, s.Wx, s.Wy,
    s.ix, s.iy,
    s.IsCommon,
    s.IsPreferred,
    s.Remarks
FROM StandardSections s
INNER JOIN SectionTypes st ON s.TypeId = st.TypeId
WHERE s.IsActive = 1 AND st.IsActive = 1;

-- 常用截面视图
CREATE VIEW v_CommonSections AS
SELECT * FROM v_SectionDetails 
WHERE IsCommon = 1 
ORDER BY TypeCode, SortOrder, B, H, T;

-- 材料属性视图
CREATE VIEW v_MaterialProperties AS
SELECT 
    m.MaterialCode,
    m.MaterialName,
    m.Standard,
    m.Density,
    m.ElasticModulus,
    m.YieldStrength,
    m.TensileStrength,
    m.ThermalExpansion
FROM Materials m
WHERE m.IsActive = 1;

-- ======================================
-- 9. 触发器定义
-- ======================================

-- 更新时间触发器
CREATE TRIGGER tr_sections_update 
AFTER UPDATE ON StandardSections
BEGIN
    UPDATE StandardSections SET UpdatedDate = datetime('now') WHERE SectionId = NEW.SectionId;
END;

CREATE TRIGGER tr_user_sections_update 
AFTER UPDATE ON UserSections
BEGIN
    UPDATE UserSections SET UpdatedDate = datetime('now') WHERE UserSectionId = NEW.UserSectionId;
END;