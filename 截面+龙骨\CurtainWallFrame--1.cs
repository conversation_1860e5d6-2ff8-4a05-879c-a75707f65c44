    /// <summary>
    /// 幕墙龙骨基类
    /// </summary>
    public class CurtainWallFrame : DbElement
    {
        #region 核心属性
        /// <summary>
        /// 截面类型UniquedId(弱引用关联）
        /// </summary>
        protected string _frameTypeUId = "xxxx";
        /// <summary>
        /// 截面类型UniquedId(弱引用关联）
        /// </summary>
        [Browsable(false), DisplayName("截面ID")]
        public string FrameTypeUId { get { return _frameTypeUId; } set { TransManager.Instance().Push(a => FrameTypeUId = a, _frameTypeUId); _frameTypeUId = value; if (AutoActivate) { ActCutCal2D3D(); } } }

        /// <summary>
        /// 龙骨类型
        /// </summary>
        internal CurtainWallFrameType FrameType = null;

        /// <summary>
        /// 标准几何轮廓点（按顺时针排列）
        /// </summary>
        internal List<DbPt> FrameOutPts = new List<DbPt>();

        /// <summary>
        /// 龙骨旋转角度
        /// </summary>
        internal double _frameAngle = 0.0;
        /// <summary>
        /// 龙骨旋转角度, 弧度制
        /// </summary>
        internal double FrameAngleRadian
        {
            get { return _frameAngle; }
            set
            {
                TransManager.Instance().Push(a => FrameAngleRadian = a, _frameAngle);
                _frameAngle = value;
                if (AutoActivate) ActCutCal2D3D();
            }
        }
        /// <summary>
        /// 龙骨旋转角度, 角度制，开放给用户
        /// </summary>
        [Category("几何属性"), DisplayName("旋转角度"), Description("龙骨绕插入点的旋转角度"),ReadOnly(false)]
        public double FrameAngle
        {
            get { return _frameAngle * 180 / Math.PI; }
            set
            {
                TransManager.Instance().Push(a => FrameAngle = a, _frameAngle);
                _frameAngle = value * 180 / Math.PI;
                if (AutoActivate) ActCutCal2D3D();
            }
        }

        /// <summary>
        /// 龙骨长度
        /// </summary>
        private double _frameLength = 0.0;
        /// <summary>
        /// 龙骨长度
        /// </summary>
        [Category("几何属性"), DisplayName("长度(mm)"), Description("龙骨的长度"), ReadOnly(false)]
        public double FrameLength 
        { 
            get { return _frameLength; } 
            set 
            {
                if (Math.Abs(_frameLength - value) < 0.001) return;
                TransManager.Instance().Push(a => FrameLength = a, _frameLength);
                _frameLength = value;
                if (AutoActivate) { ActCutCal2D3D(); }
            }
        }

        /// <summary>
        /// 材料属性
        /// </summary>
        internal DbMaterial _material;
        /// <suymma>
        /// 材料属性
        /// </suymma>
        [Category("材料"), DisplayName("材料类型"), Description("龙骨的材料类型"), ReadOnly(false)]
        [Editor(typeof(Btn_MatSelect), typeof(Btn_MatSelect))]
        public DbMaterial Material
        {
            get { return _material; }
            set
            {
                if (_material == value) return;
                TransManager.Instance().Push(a => Material = a, _material);
                _material = value;
                if (AutoActivate)
                {
                    ActCutCal2D3D();
                }
            }

        }
        #endregion

        #region 定位点功能
        /// <summary>
        /// 定位点位置
        /// </summary>
        protected DbPt _anchorPosition = new DbPt();

        /// <summary>
        /// 定位点半径
        /// </summary>
        //protected double _anchorRadius = 6.0;
        /// <summary>
        /// 定位点半径
        /// </summary>
        //[Category("定位点属性"), DisplayName("定位点半径"), Description("定位点显示图像的半径"), ReadOnly(false)]
        //public double AnchorRadius
        //{
        //    get { return _anchorRadius; }
        //    set
        //    {
        //        if (Math.Abs(_anchorRadius - value) < 0.001) return;
        //        TransManager.Instance().Push(a => AnchorRadius = a, _anchorRadius);
        //        _anchorRadius = value;
        //        ActCutCal2D3D();
        //    }
        //}

        /// <summary>
        /// 是否显示定位点
        /// </summary>
        //private bool _showAnchor = true;
        /// <summary>
        /// 是否显示定位点
        /// </summary>
        //[Category("显示属性"), DisplayName("显示定位点"), Description("是否显示定位点图形"), ReadOnly(false)]
        //public bool ShowAnchor
        //{
        //    get { return _showAnchor; }
        //    set
        //    {
        //        if (_showAnchor == value) return;
        //        TransManager.Instance().Push(a => ShowAnchor = a, _showAnchor);
        //        _showAnchor = value;
        //        if (AutoActivate) { ActCutCal2D3D(); }
        //    }
        //}

        /// <sumamry>
        /// X方向偏移量 （龙骨相对定位点的偏移）
        /// </sumamry>
        protected double _offsetX = 0.0;
        /// <sumamry>
        /// X方向偏移量 （龙骨相对定位点的偏移）
        /// </sumamry>
        [Category("定位控制"), DisplayName("左右移动(mm)"), Description("龙骨截面相对定位点的左右移动距离（定位点不动，截面左右移动）"), ReadOnly(false)]
        public virtual double OffsetX
        {
            get { return _offsetX; }
            set
            {
                if (Math.Abs(_offsetX - value) < 0.001) return;
                TransManager.Instance().Push(a => OffsetX = a, _offsetX);
                _offsetX = value;
                if (AutoActivate) { ActCutCal2D3D(); }
            }
        }

        /// <summary>
        /// Y方向偏移量（龙骨相对定位点的偏移）
        /// </summary>
        protected double _offsetY = 0.0;
        /// <summary>
        /// Y方向偏移量（龙骨相对定位点的偏移）
        /// </summary>
        [Category("定位控制"), DisplayName("上下移动(mm)"), Description("龙骨截面相对定位点的上下移动距离（定位点不动，截面上下移动）"), ReadOnly(false)]
        public virtual double OffsetY
        {
            get { return _offsetY; }
            set
            {
                if (Math.Abs(_offsetY - value) < 0.001) return;
                TransManager.Instance().Push(a => OffsetY = a, _offsetY);
                _offsetY = value;
                if (AutoActivate) { ActCutCal2D3D(); }
            }
        }

        /// <sumamry>
        /// 获取定位点位置
        /// </sumamry>
        public virtual DbPt GetAnchorPosition()
        {
            return ConPts.Count > 0 ? ConPts[0].EleCopy() : new DbPt();
        }

        /// <summary>
        /// 设置定位点位置（实际插入点位置）
        /// </summary>
        public virtual void SetAnchorPosition(DbPt anchorPos)
        {
            if (ConPts.Count > 0)
            {
                ConPts[0] = anchorPos.EleCopy();
            }
            else
            {
                ConPts.Add(anchorPos.EleCopy());

            }
            if (AutoActivate) { ActCutCal2D3D(); }
        }
        /// <summary>
        /// 获取截面信息描述
        /// </summary>
        /// <returns>截面信息字符串</returns>
        public virtual string GetSectionDescription()
        {
            if (FrameType != null)
            {
                return FrameType.GetSecName();
            }
            return "未定义截面";
        }

        /// <summary>
        /// 获取偏移状态描述
        /// </summary>
        /// <returns>偏移状态字符串</returns>
        public virtual string GetOffsetDescription()
        {
            if (Math.Abs(_offsetX) < 0.001 && Math.Abs(_offsetY) < 0.001)
            {
                return "无偏移";
            }
            return $"偏移: X={_offsetX:F1}mm, Y={_offsetY:F1}mm";
        }

        /// <summary>
        /// 重置偏移量
        /// </summary>
        public virtual void ResetOffset()
        {
            OffsetX = 0.0;
            OffsetY = 0.0;
        }

        /// <summary>
        /// 设置偏移量
        /// </summary>
        /// <param name="offsetX">X方向偏移</param>
        /// <param name="offsetY">Y方向偏移</param>
        public virtual void SetOffset(double offsetX, double offsetY)
        {
            OffsetX = offsetX;
            OffsetY = offsetY;
        }

        /// <summary>
        /// 计算截面相对于定位点的实际绘制位置
        /// </summary>
        /// <returns>实际绘制位置</returns>
        public virtual DbPt CalculateDrawPosition()
        {
            if (ConPts.Count == 0) return new DbPt();
            
            // 获取定位点位置
            DbPt anchorPos = ConPts[0];
            
            // 计算截面几何的边界
            double frameHeight = FrameType?.H ?? 120;
            double sectionBottomCenterOffset = frameHeight / 2.0;
            double anchorOffset = 30.0;
            
            // 实际绘制位置 = 定位点位置 + 截面中心偏移 + 用户偏移
            return new DbPt(
                anchorPos.X + _offsetX,
                anchorPos.Y + sectionBottomCenterOffset + anchorOffset + _offsetY
            );
        }

        #endregion

        #region 虚拟截面机制
        ///// <summary>
        ///// 虚拟截面类型（图元私有，不保存UniqueId）
        ///// </summary>
        //protected CurtainWallFrameType _virtualFrameType = null;

        ///// <summary>
        ///// 是否使用虚拟截面
        ///// </summary>
        //protected bool _ifUseVirtualType = false;

        ///// <summary>
        ///// 虚拟截面参数 B
        ///// </summary>
        //protected double? _virtualB = null;
        ///// <summary>
        ///// 虚拟截面参数 B
        ///// </summary>
        ////[Category("截面参数"), ReadOnly(false)]
        ////public double VirtualB
        ////{
        ////    get
        ////    {
        ////        if (_ifUseVirtualType && _virtualB.HasValue)
        ////            return _virtualB.Value;
        ////        return FrameType?.B ?? 120;
        ////    }
        ////}


        ///// <summary>
        ///// 虚拟截面参数 H
        ///// </summary>
        //protected double? _virtualH = null;

        ///// <summary>
        ///// 虚拟截面参数 U
        ///// </summary>
        //protected double? _virtualU = null;

        ///// <summary>
        ///// 虚拟截面参数 T
        ///// </summary>
        //protected double? _virtualT = null;

        ///// <summary>
        ///// 虚拟截面参数 D
        ///// </summary>
        //protected double? _virtualD = null;

        ///// <summary>
        ///// 虚拟截面参数 F
        ///// </summary>
        //protected double? _virtualF = null;

        ///// <summary>
        ///// 虚拟截面参数 U1
        ///// </summary>
        //protected double? _virtualU1 = null;

        ///// <summary>
        ///// 虚拟截面参数 T1
        ///// </summary>
        //protected double? _virtualT1 = null;

        ///// <summary>
        ///// 虚拟截面参数 D1
        ///// </summary>
        //protected double? _virtualD1 = null;

        ///// <summary>
        ///// 虚拟截面参数 F1
        ///// </summary>
        //protected double? _virtualF1 = null;

        ///// <summary>
        ///// 个性化状态（只读显示）
        ///// </summary>
        //[Category("截面信息"), DisplayName("个性化状态"), Description("当前图元的个性化状态"), ReadOnly(true)]
        //public string CustomizationStatus
        //{
        //    get
        //    {
        //        if (!_ifUseVirtualType)
        //            return "使用标准截面";

        //        var customizations = new List<string>();
        //        if (_virtualB.HasValue) customizations.Add($"B:{_virtualB}");
        //        if (_virtualH.HasValue) customizations.Add($"H:{_virtualH}");
        //        if (_virtualT.HasValue) customizations.Add($"T:{_virtualT}");
        //        if (_virtualD.HasValue) customizations.Add($"D:{_virtualD}");
        //        if (_virtualU.HasValue) customizations.Add($"U:{_virtualU}");
        //        if (_virtualF.HasValue) customizations.Add($"F:{_virtualF}");

        //        return $"个性化参数: {string.Join(", ", customizations)}";
        //    }
        //}
        #endregion

        /// <summary>
        /// 创建幕墙龙骨
        /// </summary>
        public CurtainWallFrame()
        {
            _if3D = false;
        }

        /// <summary>
        /// 构造函数（用于创建虚拟截面）
        /// </summary>
        /// <param name="insertPt">插入点</param>
        /// <param name="frameType">截面类型</param>
        /// <param name="frameAngle">旋转角度（弧度制，内部存储用）</param>
        //public CurtainWallFrame(DbPt insertPt, CurtainWallFrameType frameType, double frameAngle = 0.0)
        //{
        //    ConPts.Add(insertPt.EleCopy());
        //    _frameAngle = frameAngle; // 构造函数接收弧度制，直接存储
        //    SetFrameType(frameType);
        //}


        #region 核心方法
        /// <summary>
        /// 设置截面类型
        /// </summary>
        /// <param name="frameType">新的截面类型</param>
        /// <param name="ifActive">是否激活</param>
        public void SetEleType(CurtainWallFrameType type, bool ifActive = false)
        {
            TransManager.Instance().Push(a => SetEleType(a, ifActive), type);
            FrameType = type;
            _frameTypeUId = type.UniqueId;

            // 清除虚拟截面状态
            //ResetVirtualType();

            if (ifActive) { ActCutCal2D3D(); }
        }

        /// <summary>
        /// 设置龙骨类型
        /// </summary>
        public void SetFrameType(CurtainWallFrameType frameType)
        {
            TransManager.Instance().Push(SetFrameType, FrameType);
            FrameType = frameType;
            _frameTypeUId = frameType.UniqueId;
            ActCutCal2D3D();
        }

        /// <summary>
        /// 建立图元与类型的关联
        /// </summary>
        /// <returns>关联成功时返回null;关联失败时返回在执行LinkElementType操作前已关联的类型(用于协同时反补被创建者删除的类型)</returns>
        public override DbElementType LinkElementType()
        {
            // 保存原有的类型引用，用于关联失败时返回
            var originalFrameType = FrameType;

            foreach (DbElementType elementType in EBDB.Instance.GProject.ElementTypes)
            {
                if (elementType.UniqueId == _frameTypeUId)
                {
                    // 将找到的类型赋值给图元的 FrameType 即建立图元与类型的关联
                    FrameType = elementType as CurtainWallFrameType;
                    break;
                }
            }

            // 关联成功返回null，关联失败返回原类型(构件级协同时可能会发生这种情况，返回原来已关联的类型即可)
            return FrameType != null ? null : originalFrameType;
        }
        #endregion

        #region 虚拟截面核心方法
        /// <summary>
        /// 获取当前有效的截面类型
        /// </summary>
        //protected CurtainWallFrameType GetCurrentFrameType()
        //{
        //    return _ifUseVirtualType ? _virtualFrameType : FrameType;
        //}

        #endregion

        #region 虚拟方法（定位点与龙骨之间的控制关系），可以重写
        ///// <summary>
        ///// 计算基于定位点的绘制位置
        ///// </summary>
        ///// <returns>实际绘制位置</returns>
        //protected virtual DbPt CalculatePositionFromAnchor()
        //{
        //    // 基类提供默认实现：简单的偏移计算
        //    var basePos = _anchorPosition.EleCopy();
        //    basePos.X += _offsetX;
        //    basePos.Y += _offsetY;
        //    return basePos;
        //}

        ///// <summary>
        ///// 计算绘制位置的通用框架
        ///// </summary>
        ///// <returns>最终绘制位置</returns>
        //protected virtual DbPt CalculateDrawPosition()
        //{
        //    return _enableAnchor ? CalculatePositionFromAnchor() : (ConPts.Count > 0 ? ConPts[0] : new DbPt());
        //}

        #endregion


    }

