using System;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// 🎯 等边角钢国标数据管理类
/// 管理所有国标规定的等边角钢规格参数
/// </summary>
public static class AngleSteelStandards
{
    #region 🔥 国标角钢数据定义

    /// <summary>
    /// 国标等边角钢规格数据
    /// 根据GB/T 706-2008 《热轧型钢》标准
    /// </summary>
    private static readonly List<AngleSteelSpec> _standardSpecs = new List<AngleSteelSpec>
    {
        // 格式: 边长, 厚度, 中间圆弧半径r, 顶端圆弧半径r1, 截面积, 理论重量
        new AngleSteelSpec(20, 3, 3.5, 1.0, 1.12, 0.879),
        new AngleSteelSpec(25, 3, 3.5, 1.0, 1.43, 1.124),
        new AngleSteelSpec(25, 4, 3.5, 1.3, 1.86, 1.459),
        
        new AngleSteelSpec(30, 3, 5.0, 1.0, 1.74, 1.366),
        new AngleSteelSpec(30, 4, 5.0, 1.3, 2.27, 1.782),
        
        new AngleSteelSpec(40, 3, 6.0, 1.0, 2.35, 1.845),
        new AngleSteelSpec(40, 4, 6.0, 1.3, 3.08, 2.417),
        new AngleSteelSpec(40, 5, 6.0, 1.7, 3.79, 2.976),
        
        new AngleSteelSpec(45, 3, 7.0, 1.0, 2.66, 2.088),
        new AngleSteelSpec(45, 4, 7.0, 1.3, 3.49, 2.742),
        new AngleSteelSpec(45, 5, 7.0, 1.7, 4.30, 3.378),
        new AngleSteelSpec(45, 6, 7.0, 2.0, 5.09, 3.996),
        
        new AngleSteelSpec(50, 3, 7.0, 1.0, 2.96, 2.332),
        new AngleSteelSpec(50, 4, 7.0, 1.3, 3.89, 3.059),
        new AngleSteelSpec(50, 5, 7.0, 1.7, 4.80, 3.770),
        new AngleSteelSpec(50, 6, 7.0, 2.0, 5.69, 4.465),
        
        new AngleSteelSpec(56, 4, 8.0, 1.3, 4.38, 3.446),
        new AngleSteelSpec(56, 5, 8.0, 1.7, 5.41, 4.251),
        new AngleSteelSpec(56, 8, 8.0, 2.7, 8.45, 6.633),
        
        new AngleSteelSpec(63, 4, 8.0, 1.3, 4.96, 3.907),
        new AngleSteelSpec(63, 5, 8.0, 1.7, 6.13, 4.822),
        new AngleSteelSpec(63, 6, 8.0, 2.0, 7.28, 5.721),
        new AngleSteelSpec(63, 8, 8.0, 2.7, 9.55, 7.504),
        new AngleSteelSpec(63, 10, 8.0, 3.3, 11.73, 9.213),
        
        new AngleSteelSpec(70, 5, 9.0, 1.7, 6.86, 5.397),
        new AngleSteelSpec(70, 6, 9.0, 2.0, 8.15, 6.406),
        new AngleSteelSpec(70, 7, 9.0, 2.3, 9.42, 7.398),
        new AngleSteelSpec(70, 8, 9.0, 2.7, 10.66, 8.373),
        
        new AngleSteelSpec(75, 5, 9.0, 1.7, 7.39, 5.818),
        new AngleSteelSpec(75, 6, 9.0, 2.0, 8.78, 6.905),
        new AngleSteelSpec(75, 8, 9.0, 2.7, 11.50, 9.030),
        new AngleSteelSpec(75, 10, 9.0, 3.3, 14.13, 11.089),
        
        new AngleSteelSpec(80, 5, 9.0, 1.7, 7.91, 6.211),
        new AngleSteelSpec(80, 6, 9.0, 2.0, 9.42, 7.405),
        new AngleSteelSpec(80, 7, 9.0, 2.3, 10.90, 8.581),
        new AngleSteelSpec(80, 8, 9.0, 2.7, 12.36, 9.739),
        new AngleSteelSpec(80, 10, 9.0, 3.3, 15.20, 11.928),
        
        new AngleSteelSpec(90, 6, 10.0, 2.0, 10.68, 8.405),
        new AngleSteelSpec(90, 7, 10.0, 2.3, 12.38, 9.739),
        new AngleSteelSpec(90, 8, 10.0, 2.7, 14.06, 11.055),
        new AngleSteelSpec(90, 10, 10.0, 3.3, 17.34, 13.617),
        new AngleSteelSpec(90, 12, 10.0, 4.0, 20.52, 16.121),
        
        new AngleSteelSpec(100, 6, 10.0, 2.0, 11.94, 9.394),
        new AngleSteelSpec(100, 7, 10.0, 2.3, 13.84, 10.896),
        new AngleSteelSpec(100, 8, 10.0, 2.7, 15.72, 12.379),
        new AngleSteelSpec(100, 10, 10.0, 3.3, 19.38, 15.233),
        new AngleSteelSpec(100, 12, 10.0, 4.0, 22.94, 18.024),
        new AngleSteelSpec(100, 14, 10.0, 4.7, 26.40, 20.752),
        new AngleSteelSpec(100, 16, 10.0, 5.3, 29.76, 23.415),
        
        new AngleSteelSpec(110, 7, 11.0, 2.3, 15.30, 12.054),
        new AngleSteelSpec(110, 8, 11.0, 2.7, 17.38, 13.678),
        new AngleSteelSpec(110, 10, 11.0, 3.3, 21.46, 16.877),
        new AngleSteelSpec(110, 12, 11.0, 4.0, 25.44, 20.012),
        new AngleSteelSpec(110, 14, 11.0, 4.7, 29.32, 23.082),
        
        new AngleSteelSpec(125, 8, 12.0, 2.7, 19.80, 15.558),
        new AngleSteelSpec(125, 10, 12.0, 3.3, 24.50, 19.256),
        new AngleSteelSpec(125, 12, 12.0, 4.0, 29.10, 22.890),
        new AngleSteelSpec(125, 14, 12.0, 4.7, 33.60, 26.459),
        new AngleSteelSpec(125, 16, 12.0, 5.3, 37.98, 29.863),
        
        new AngleSteelSpec(140, 10, 13.0, 3.3, 27.54, 21.635),
        new AngleSteelSpec(140, 12, 13.0, 4.0, 32.78, 25.766),
        new AngleSteelSpec(140, 14, 13.0, 4.7, 37.90, 29.831),
        new AngleSteelSpec(140, 16, 13.0, 5.3, 42.90, 33.729),
        
        new AngleSteelSpec(160, 10, 15.0, 3.3, 31.62, 24.854),
        new AngleSteelSpec(160, 12, 15.0, 4.0, 37.68, 29.628),
        new AngleSteelSpec(160, 14, 15.0, 4.7, 43.62, 34.335),
        new AngleSteelSpec(160, 16, 15.0, 5.3, 49.44, 38.875),
        new AngleSteelSpec(160, 18, 15.0, 6.0, 55.14, 43.347),
        new AngleSteelSpec(160, 20, 15.0, 6.7, 60.72, 47.750),
        
        new AngleSteelSpec(180, 12, 17.0, 4.0, 42.58, 33.490),
        new AngleSteelSpec(180, 14, 17.0, 4.7, 49.38, 38.840),
        new AngleSteelSpec(180, 16, 17.0, 5.3, 56.06, 44.124),
        new AngleSteelSpec(180, 18, 17.0, 6.0, 62.62, 49.242),
        new AngleSteelSpec(180, 20, 17.0, 6.7, 69.06, 54.293),
        
        new AngleSteelSpec(200, 14, 18.0, 4.7, 55.14, 43.347),
        new AngleSteelSpec(200, 16, 18.0, 5.3, 62.68, 49.281),
        new AngleSteelSpec(200, 18, 18.0, 6.0, 70.10, 55.149),
        new AngleSteelSpec(200, 20, 18.0, 6.7, 77.38, 60.851),
        new AngleSteelSpec(200, 24, 18.0, 8.0, 91.58, 71.938)
    };

    #endregion

    #region 🔥 公共查询接口

    /// <summary>
    /// 获取所有标准角钢规格
    /// </summary>
    /// <returns>所有国标角钢规格列表</returns>
    public static List<AngleSteelSpec> GetAllStandardSpecs()
    {
        return _standardSpecs.ToList(); // 返回副本，避免外部修改
    }

    /// <summary>
    /// 根据边长获取可用的厚度列表
    /// </summary>
    /// <param name="sideLength">边长(mm)</param>
    /// <returns>该边长对应的所有可用厚度</returns>
    public static List<int> GetAvailableThicknesses(int sideLength)
    {
        return _standardSpecs
            .Where(spec => spec.SideLength == sideLength)
            .Select(spec => spec.Thickness)
            .OrderBy(t => t)
            .ToList();
    }

    /// <summary>
    /// 根据边长和厚度查找标准规格
    /// </summary>
    /// <param name="sideLength">边长(mm)</param>
    /// <param name="thickness">厚度(mm)</param>
    /// <returns>匹配的标准规格，如果不存在返回null</returns>
    public static AngleSteelSpec FindStandardSpec(int sideLength, int thickness)
    {
        return _standardSpecs.FirstOrDefault(spec => 
            spec.SideLength == sideLength && spec.Thickness == thickness);
    }

    /// <summary>
    /// 验证边长和厚度组合是否符合国标
    /// </summary>
    /// <param name="sideLength">边长(mm)</param>
    /// <param name="thickness">厚度(mm)</param>
    /// <returns>是否为标准规格</returns>
    public static bool IsStandardSpec(int sideLength, int thickness)
    {
        return FindStandardSpec(sideLength, thickness) != null;
    }

    /// <summary>
    /// 获取所有可用的边长列表
    /// </summary>
    /// <returns>所有标准边长，按升序排列</returns>
    public static List<int> GetAvailableSideLengths()
    {
        return _standardSpecs
            .Select(spec => spec.SideLength)
            .Distinct()
            .OrderBy(s => s)
            .ToList();
    }

    /// <summary>
    /// 根据边长和厚度获取推荐的最接近规格
    /// </summary>
    /// <param name="targetSideLength">目标边长</param>
    /// <param name="targetThickness">目标厚度</param>
    /// <returns>最接近的标准规格</returns>
    public static AngleSteelSpec GetNearestStandardSpec(int targetSideLength, int targetThickness)
    {
        if (_standardSpecs.Count == 0) return null;

        // 先找完全匹配
        var exactMatch = FindStandardSpec(targetSideLength, targetThickness);
        if (exactMatch != null) return exactMatch;

        // 如果没有完全匹配，找最接近的
        return _standardSpecs
            .OrderBy(spec => Math.Abs(spec.SideLength - targetSideLength) + Math.Abs(spec.Thickness - targetThickness))
            .First();
    }

    /// <summary>
    /// 生成角钢规格的标准名称
    /// </summary>
    /// <param name="sideLength">边长</param>
    /// <param name="thickness">厚度</param>
    /// <returns>标准名称，如"L50×5"</returns>
    public static string GetStandardName(int sideLength, int thickness)
    {
        return $"L{sideLength}×{thickness}";
    }

    /// <summary>
    /// 生成角钢规格的完整描述
    /// </summary>
    /// <param name="spec">角钢规格</param>
    /// <returns>完整描述，如"等边角钢 L50×5 (GB/T 706-2008)"</returns>
    public static string GetFullDescription(AngleSteelSpec spec)
    {
        return $"等边角钢 L{spec.SideLength}×{spec.Thickness} (GB/T 706-2008)";
    }

    #endregion

    #region 🔥 参数计算方法

    /// <summary>
    /// 计算顶端圆弧半径 r1 = d/3
    /// </summary>
    /// <param name="thickness">厚度 d</param>
    /// <returns>顶端圆弧半径</returns>
    public static double CalculateTopRadius(int thickness)
    {
        return thickness / 3.0;
    }

    /// <summary>
    /// 验证顶端圆弧半径是否正确
    /// </summary>
    /// <param name="thickness">厚度</param>
    /// <param name="topRadius">顶端圆弧半径</param>
    /// <param name="tolerance">容差，默认0.1mm</param>
    /// <returns>是否在容差范围内</returns>
    public static bool ValidateTopRadius(int thickness, double topRadius, double tolerance = 0.1)
    {
        double expectedRadius = CalculateTopRadius(thickness);
        return Math.Abs(topRadius - expectedRadius) <= tolerance;
    }

    #endregion
}

/// <summary>
/// 🎯 等边角钢标准规格数据结构
/// </summary>
public class AngleSteelSpec
{
    /// <summary>
    /// 边长 (mm)
    /// </summary>
    public int SideLength { get; set; }
    
    /// <summary>
    /// 厚度 d (mm)
    /// </summary>
    public int Thickness { get; set; }
    
    /// <summary>
    /// 中间圆弧半径 r (mm)
    /// </summary>
    public double MiddleRadius { get; set; }
    
    /// <summary>
    /// 顶端圆弧半径 r1 (mm)，理论值为 d/3
    /// </summary>
    public double TopRadius { get; set; }
    
    /// <summary>
    /// 截面积 (cm²)
    /// </summary>
    public double Area { get; set; }
    
    /// <summary>
    /// 理论重量 (kg/m)
    /// </summary>
    public double WeightPerMeter { get; set; }

    /// <summary>
    /// 构造函数
    /// </summary>
    public AngleSteelSpec(int sideLength, int thickness, double middleRadius, double topRadius, double area, double weight)
    {
        SideLength = sideLength;
        Thickness = thickness;
        MiddleRadius = middleRadius;
        TopRadius = topRadius;
        Area = area;
        WeightPerMeter = weight;
    }

    /// <summary>
    /// 获取标准名称
    /// </summary>
    public string GetStandardName()
    {
        return AngleSteelStandards.GetStandardName(SideLength, Thickness);
    }

    /// <summary>
    /// 获取完整描述
    /// </summary>
    public string GetFullDescription()
    {
        return AngleSteelStandards.GetFullDescription(this);
    }

    /// <summary>
    /// 验证顶端圆弧半径是否符合规范
    /// </summary>
    public bool ValidateTopRadius()
    {
        return AngleSteelStandards.ValidateTopRadius(Thickness, TopRadius);
    }

    /// <summary>
    /// 克隆规格
    /// </summary>
    public AngleSteelSpec Clone()
    {
        return new AngleSteelSpec(SideLength, Thickness, MiddleRadius, TopRadius, Area, WeightPerMeter);
    }

    public override string ToString()
    {
        return GetStandardName();
    }
}