using System;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// 🎯 国标截面数据库集成管理器
/// 将SQLite数据库与现有CurtainWallFrameType系统集成
/// </summary>
public class StandardSectionIntegration
{
    #region 🔥 单例模式和初始化

    private static StandardSectionIntegration _instance;
    private static readonly object _lock = new object();
    
    private StandardSectionDatabase _database;
    private bool _isInitialized = false;
    
    public static StandardSectionIntegration Instance
    {
        get
        {
            if (_instance == null)
            {
                lock (_lock)
                {
                    if (_instance == null)
                    {
                        _instance = new StandardSectionIntegration();
                    }
                }
            }
            return _instance;
        }
    }

    private StandardSectionIntegration()
    {
        _database = new StandardSectionDatabase();
    }

    /// <summary>
    /// 初始化国标数据库系统
    /// </summary>
    public async Task<bool> InitializeAsync()
    {
        try
        {
            if (!_database.IsDatabaseValid())
            {
                await _database.InitializeDatabaseAsync();
                await InitializeStandardDataAsync();
            }
            
            _isInitialized = true;
            return true;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"国标数据库初始化失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 初始化标准数据
    /// </summary>
    private async Task InitializeStandardDataAsync()
    {
        // 这里可以从SQL脚本文件加载数据，或者调用数据初始化程序
        // 简化起见，假设数据已经通过SQL脚本导入
        await Task.CompletedTask;
    }

    #endregion

    #region 🔥 CurtainWallFrameType集成接口

    /// <summary>
    /// 根据国标数据创建CurtainWallFrameType
    /// </summary>
    public async Task<CurtainWallFrameType> CreateFrameTypeFromStandardAsync(string typeCode, string sectionCode)
    {
        if (!_isInitialized)
            await InitializeAsync();

        var standardSection = await _database.FindStandardSectionAsync(typeCode, 0, 0, 0);
        if (standardSection == null)
            return null;

        return ConvertToFrameType(standardSection);
    }

    /// <summary>
    /// 查找匹配的标准截面
    /// </summary>
    public async Task<CurtainWallFrameType> FindMatchingStandardFrameTypeAsync(CurtainWallFrameType frameType)
    {
        if (!_isInitialized)
            await InitializeAsync();

        var typeCode = GetTypeCodeFromFrameType(frameType);
        var standardSection = await _database.FindStandardSectionAsync(typeCode, frameType.B, frameType.H, frameType.T);
        
        if (standardSection != null)
        {
            return ConvertToFrameType(standardSection);
        }
        
        // 如果找不到精确匹配，查找最近的
        var nearestSection = await _database.FindNearestStandardSectionAsync(typeCode, frameType.B, frameType.H, frameType.T);
        return nearestSection != null ? ConvertToFrameType(nearestSection) : null;
    }

    /// <summary>
    /// 获取指定类型的所有标准截面
    /// </summary>
    public async Task<List<CurtainWallFrameType>> GetStandardFrameTypesAsync(int curtainWallType)
    {
        if (!_isInitialized)
            await InitializeAsync();

        var typeCode = GetTypeCodeFromCurtainWallType(curtainWallType);
        var standardSections = await _database.GetStandardSectionsAsync(typeCode);
        
        return standardSections.Select(ConvertToFrameType).ToList();
    }

    /// <summary>
    /// 获取常用标准截面
    /// </summary>
    public async Task<List<CurtainWallFrameType>> GetCommonStandardFrameTypesAsync(int curtainWallType = 0)
    {
        if (!_isInitialized)
            await InitializeAsync();

        var typeCode = curtainWallType > 0 ? GetTypeCodeFromCurtainWallType(curtainWallType) : null;
        var commonSections = await _database.GetCommonSectionsAsync(typeCode);
        
        return commonSections.Select(ConvertToFrameType).ToList();
    }

    /// <summary>
    /// 验证截面是否为标准规格
    /// </summary>
    public async Task<bool> IsStandardSectionAsync(CurtainWallFrameType frameType)
    {
        if (!_isInitialized)
            await InitializeAsync();

        var typeCode = GetTypeCodeFromFrameType(frameType);
        var standardSection = await _database.FindStandardSectionAsync(typeCode, frameType.B, frameType.H, frameType.T);
        
        return standardSection != null;
    }

    /// <summary>
    /// 获取标准截面的完整信息
    /// </summary>
    public async Task<StandardSectionInfo> GetStandardSectionInfoAsync(CurtainWallFrameType frameType)
    {
        if (!_isInitialized)
            await InitializeAsync();

        var typeCode = GetTypeCodeFromFrameType(frameType);
        return await _database.FindStandardSectionAsync(typeCode, frameType.B, frameType.H, frameType.T);
    }

    #endregion

    #region 🔥 类型转换方法

    /// <summary>
    /// 将StandardSectionInfo转换为CurtainWallFrameType
    /// </summary>
    private CurtainWallFrameType ConvertToFrameType(StandardSectionInfo standardSection)
    {
        var frameType = new CurtainWallFrameType();
        
        // 设置基本类型
        frameType.Type = GetCurtainWallTypeFromTypeCode(standardSection.TypeCode);
        
        // 设置几何参数
        frameType.B = (int)standardSection.B;
        frameType.H = (int)standardSection.H;
        frameType.T = (int)standardSection.T;
        frameType.D = (int)standardSection.R1; // 端部圆角半径
        frameType.U = (int)standardSection.R;  // 内圆角半径
        frameType.F = (int)standardSection.T1; // 翼缘厚度（用于槽钢、工字钢）
        
        // 设置截面特性
        frameType.Area = standardSection.Area;
        frameType.WeightPerMeter = standardSection.WeightPerMeter;
        frameType.MomentOfInertiaX = standardSection.Ix;
        frameType.MomentOfInertiaY = standardSection.Iy;
        frameType.SectionModulusX = standardSection.Wx;
        frameType.SectionModulusY = standardSection.Wy;
        frameType.RadiusOfGyrationX = standardSection.ix;
        frameType.RadiusOfGyrationY = standardSection.iy;
        
        // 设置名称和标识
        frameType.Name = standardSection.SectionName;
        frameType.Tag = standardSection.SectionCode;
        frameType.UniqueId = $"STD_{standardSection.SectionId}";
        
        // 标记为标准截面
        frameType.IsStandardSection = true;
        frameType.StandardSectionId = standardSection.SectionId;
        
        return frameType;
    }

    /// <summary>
    /// 从CurtainWallFrameType获取对应的数据库类型代码
    /// </summary>
    private string GetTypeCodeFromFrameType(CurtainWallFrameType frameType)
    {
        return GetTypeCodeFromCurtainWallType(frameType.Type);
    }

    /// <summary>
    /// 从幕墙龙骨类型获取数据库类型代码
    /// </summary>
    private string GetTypeCodeFromCurtainWallType(int curtainWallType)
    {
        switch (curtainWallType)
        {
            case 3: return "AS";   // 等边角钢
            case 4: return "UAS";  // 不等边角钢  
            case 5: return "CS";   // 普通槽钢
            case 6: return "LCS";  // 轻型槽钢
            case 7: return "HS";   // 工字钢
            case 8: return "LHS";  // 轻型工字钢
            case 9: return "RTS";  // 矩形钢管  
            case 10: return "STS"; // 方形钢管
            case 11: return "CTS"; // 圆形钢管
            default: return "CUSTOM";
        }
    }

    /// <summary>
    /// 从数据库类型代码获取幕墙龙骨类型
    /// </summary>
    private int GetCurtainWallTypeFromTypeCode(string typeCode)
    {
        switch (typeCode?.ToUpper())
        {
            case "AS": return 3;   // 等边角钢
            case "UAS": return 4;  // 不等边角钢
            case "CS": return 5;   // 普通槽钢
            case "LCS": return 6;  // 轻型槽钢
            case "HS": return 7;   // 工字钢
            case "LHS": return 8;  // 轻型工字钢
            case "RTS": return 9;  // 矩形钢管
            case "STS": return 10; // 方形钢管
            case "CTS": return 11; // 圆形钢管
            default: return 50;    // 自定义
        }
    }

    #endregion

    #region 🔥 CurtainWallFrameType扩展方法

    /// <summary>
    /// 为CurtainWallFrameType添加标准截面支持
    /// </summary>
    public static void ExtendFrameTypeWithStandardSupport(CurtainWallFrameType frameType)
    {
        // 添加标准截面相关属性
        if (!frameType.GetType().GetProperties().Any(p => p.Name == "IsStandardSection"))
        {
            // 在实际实现中，这些属性应该直接添加到CurtainWallFrameType类中
            // 这里只是演示扩展的概念
        }
    }

    #endregion

    #region 🔥 数据库管理方法

    /// <summary>
    /// 获取数据库统计信息
    /// </summary>
    public async Task<DatabaseStatistics> GetDatabaseStatisticsAsync()
    {
        if (!_isInitialized)
            await InitializeAsync();

        return await _database.GetStatisticsAsync();
    }

    /// <summary>
    /// 获取所有截面类型
    /// </summary>
    public async Task<List<SectionTypeInfo>> GetAllSectionTypesAsync()
    {
        if (!_isInitialized)
            await InitializeAsync();

        return await _database.GetSectionTypesAsync();
    }

    /// <summary>
    /// 重新加载数据库
    /// </summary>
    public async Task ReloadDatabaseAsync()
    {
        _isInitialized = false;
        await InitializeAsync();
    }

    /// <summary>
    /// 备份数据库
    /// </summary>
    public async Task<bool> BackupDatabaseAsync(string backupPath)
    {
        try
        {
            var sourcePath = StandardSectionDatabase.DefaultDatabasePath;
            if (System.IO.File.Exists(sourcePath))
            {
                System.IO.File.Copy(sourcePath, backupPath, true);
                return true;
            }
            return false;
        }
        catch
        {
            return false;
        }
    }

    #endregion

    #region 🔥 查询和搜索接口

    /// <summary>
    /// 按条件搜索标准截面
    /// </summary>
    public async Task<List<CurtainWallFrameType>> SearchStandardFrameTypesAsync(SectionQueryCriteria criteria)
    {
        if (!_isInitialized)
            await InitializeAsync();

        var sections = new List<StandardSectionInfo>();
        
        if (!string.IsNullOrEmpty(criteria.TypeCode))
        {
            if (criteria.MinB.HasValue || criteria.MaxB.HasValue || 
                criteria.MinH.HasValue || criteria.MaxH.HasValue ||
                criteria.MinT.HasValue || criteria.MaxT.HasValue)
            {
                // 按范围查询
                sections = await _database.GetSectionsByRangeAsync(
                    criteria.TypeCode,
                    criteria.MinB ?? 0, criteria.MaxB ?? double.MaxValue,
                    criteria.MinH ?? 0, criteria.MaxH ?? double.MaxValue,
                    criteria.MinT ?? 0, criteria.MaxT ?? double.MaxValue
                );
            }
            else
            {
                // 获取指定类型的所有截面
                sections = await _database.GetStandardSectionsAsync(criteria.TypeCode);
            }
        }
        
        // 应用其他过滤条件
        if (criteria.IsCommon.HasValue)
        {
            sections = sections.Where(s => s.IsCommon == criteria.IsCommon.Value).ToList();
        }
        
        if (criteria.IsPreferred.HasValue)
        {
            sections = sections.Where(s => s.IsPreferred == criteria.IsPreferred.Value).ToList();
        }
        
        if (criteria.MinArea.HasValue)
        {
            sections = sections.Where(s => s.Area >= criteria.MinArea.Value).ToList();
        }
        
        if (criteria.MaxArea.HasValue)
        {
            sections = sections.Where(s => s.Area <= criteria.MaxArea.Value).ToList();
        }
        
        if (criteria.MinWeight.HasValue)
        {
            sections = sections.Where(s => s.WeightPerMeter >= criteria.MinWeight.Value).ToList();
        }
        
        if (criteria.MaxWeight.HasValue)
        {
            sections = sections.Where(s => s.WeightPerMeter <= criteria.MaxWeight.Value).ToList();
        }
        
        // 模糊匹配
        if (!string.IsNullOrEmpty(criteria.SectionCodePattern))
        {
            sections = sections.Where(s => s.SectionCode.Contains(criteria.SectionCodePattern)).ToList();
        }
        
        if (!string.IsNullOrEmpty(criteria.SectionNamePattern))
        {
            sections = sections.Where(s => s.SectionName.Contains(criteria.SectionNamePattern)).ToList();
        }
        
        return sections.Select(ConvertToFrameType).ToList();
    }

    /// <summary>
    /// 获取推荐的标准截面
    /// </summary>
    public async Task<List<SectionRecommendationInfo>> GetRecommendedSectionsAsync(
        CurtainWallFrameType targetFrameType, 
        int maxRecommendations = 5)
    {
        if (!_isInitialized)
            await InitializeAsync();

        var recommendations = new List<SectionRecommendationInfo>();
        var typeCode = GetTypeCodeFromFrameType(targetFrameType);
        
        // 获取同类型的所有标准截面
        var standardSections = await _database.GetStandardSectionsAsync(typeCode);
        
        foreach (var section in standardSections)
        {
            var score = CalculateMatchScore(targetFrameType, section);
            if (score > 60) // 只推荐匹配度大于60%的截面
            {
                var recommendation = new SectionRecommendationInfo
                {
                    RecommendedSection = section,
                    MatchScore = score,
                    RecommendationReason = GenerateRecommendationReason(targetFrameType, section, score)
                };
                
                GenerateAdvantagesAndConsiderations(targetFrameType, section, recommendation);
                recommendations.Add(recommendation);
            }
        }
        
        return recommendations
            .OrderByDescending(r => r.MatchScore)
            .Take(maxRecommendations)
            .ToList();
    }

    /// <summary>
    /// 计算匹配度分数
    /// </summary>
    private double CalculateMatchScore(CurtainWallFrameType target, StandardSectionInfo standard)
    {
        double score = 100;
        
        // 几何参数匹配度
        score -= Math.Abs(target.B - standard.B) / Math.Max(target.B, standard.B) * 30;
        score -= Math.Abs(target.H - standard.H) / Math.Max(target.H, standard.H) * 30;
        score -= Math.Abs(target.T - standard.T) / Math.Max(target.T, standard.T) * 20;
        
        // 截面特性匹配度
        if (target.Area > 0 && standard.Area > 0)
        {
            score -= Math.Abs(target.Area - standard.Area) / Math.Max(target.Area, standard.Area) * 10;
        }
        
        // 常用规格加分
        if (standard.IsCommon) score += 5;
        if (standard.IsPreferred) score += 10;
        
        return Math.Max(0, score);
    }

    /// <summary>
    /// 生成推荐理由
    /// </summary>
    private string GenerateRecommendationReason(CurtainWallFrameType target, StandardSectionInfo standard, double score)
    {
        if (score >= 95)
            return "几何参数高度匹配，是理想的标准规格选择";
        else if (score >= 85)
            return "参数匹配度很高，性能接近目标要求";
        else if (score >= 75)
            return "参数基本匹配，可以满足大部分应用需求";
        else
            return "参数存在一定差异，但仍是可行的标准规格选择";
    }

    /// <summary>
    /// 生成优点和注意事项
    /// </summary>
    private void GenerateAdvantagesAndConsiderations(CurtainWallFrameType target, StandardSectionInfo standard, SectionRecommendationInfo recommendation)
    {
        // 优点
        if (standard.IsCommon)
            recommendation.Advantages.Add("常用规格，供货充足");
        if (standard.IsPreferred)
            recommendation.Advantages.Add("推荐规格，性价比高");
        if (standard.Area >= target.Area)
            recommendation.Advantages.Add("截面面积满足强度要求");
        if (standard.WeightPerMeter <= target.WeightPerMeter)
            recommendation.Advantages.Add("重量较轻，有利于节约成本");
            
        // 注意事项
        if (Math.Abs(standard.B - target.B) > target.B * 0.1)
            recommendation.Considerations.Add($"宽度差异较大（目标:{target.B}mm，推荐:{standard.B}mm）");
        if (Math.Abs(standard.H - target.H) > target.H * 0.1)
            recommendation.Considerations.Add($"高度差异较大（目标:{target.H}mm，推荐:{standard.H}mm）");
        if (standard.Area < target.Area * 0.9)
            recommendation.Considerations.Add("截面面积偏小，需验证强度是否满足要求");
        if (standard.WeightPerMeter > target.WeightPerMeter * 1.2)
            recommendation.Considerations.Add("重量增加较多，需考虑对结构的影响");
    }

    #endregion

    #region 🔥 资源释放

    public void Dispose()
    {
        _database?.Dispose();
    }

    #endregion
}

/// <summary>
/// 🎯 CurtainWallFrameType的标准截面扩展
/// </summary>
public static class CurtainWallFrameTypeStandardExtensions
{
    /// <summary>
    /// 异步查找匹配的标准截面
    /// </summary>
    public static async Task<CurtainWallFrameType> FindMatchingStandardAsync(this CurtainWallFrameType frameType)
    {
        return await StandardSectionIntegration.Instance.FindMatchingStandardFrameTypeAsync(frameType);
    }

    /// <summary>
    /// 异步验证是否为标准规格
    /// </summary>
    public static async Task<bool> IsStandardAsync(this CurtainWallFrameType frameType)
    {
        return await StandardSectionIntegration.Instance.IsStandardSectionAsync(frameType);
    }

    /// <summary>
    /// 异步获取标准截面信息
    /// </summary>
    public static async Task<StandardSectionInfo> GetStandardInfoAsync(this CurtainWallFrameType frameType)
    {
        return await StandardSectionIntegration.Instance.GetStandardSectionInfoAsync(frameType);
    }

    /// <summary>
    /// 异步获取推荐的标准截面
    /// </summary>
    public static async Task<List<SectionRecommendationInfo>> GetRecommendationsAsync(this CurtainWallFrameType frameType, int maxCount = 5)
    {
        return await StandardSectionIntegration.Instance.GetRecommendedSectionsAsync(frameType, maxCount);
    }
}