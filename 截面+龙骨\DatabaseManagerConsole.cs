using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

/// <summary>
/// 🎯 数据库管理控制台工具
/// 提供命令行界面来管理国标截面数据库
/// </summary>
public class DatabaseManagerConsole
{
    private readonly DatabaseManager _manager;
    private readonly StandardSectionIntegration _integration;
    
    public DatabaseManagerConsole()
    {
        _manager = new DatabaseManager();
        _integration = StandardSectionIntegration.Instance;
    }

    /// <summary>
    /// 运行控制台程序
    /// </summary>
    public async Task RunAsync()
    {
        Console.WriteLine("🎯 国标截面数据库管理工具");
        Console.WriteLine("=====================================");
        Console.WriteLine();
        
        while (true)
        {
            ShowMainMenu();
            var choice = Console.ReadLine()?.Trim();
            
            switch (choice?.ToLower())
            {
                case "1":
                case "init":
                    await InitializeDatabaseAsync();
                    break;
                    
                case "2":
                case "validate":
                    await ValidateDatabaseAsync();
                    break;
                    
                case "3":
                case "backup":
                    await BackupDatabaseAsync();
                    break;
                    
                case "4":
                case "restore":
                    await RestoreDatabaseAsync();
                    break;
                    
                case "5":
                case "optimize":
                    await OptimizeDatabaseAsync();
                    break;
                    
                case "6":
                case "stats":
                    await ShowStatisticsAsync();
                    break;
                    
                case "7":
                case "test":
                    await TestIntegrationAsync();
                    break;
                    
                case "8":
                case "cleanup":
                    await CleanupBackupsAsync();
                    break;
                    
                case "9":
                case "export":
                    await ExportDataAsync();
                    break;
                    
                case "0":
                case "exit":
                case "quit":
                    Console.WriteLine("再见！");
                    return;
                    
                default:
                    Console.WriteLine("❌ 无效选择，请重试");
                    break;
            }
            
            Console.WriteLine();
            Console.WriteLine("按任意键继续...");
            Console.ReadKey();
            Console.Clear();
        }
    }

    /// <summary>
    /// 显示主菜单
    /// </summary>
    private void ShowMainMenu()
    {
        Console.WriteLine("请选择操作:");
        Console.WriteLine("1. 初始化数据库 (init)");
        Console.WriteLine("2. 验证数据库 (validate)");
        Console.WriteLine("3. 备份数据库 (backup)");
        Console.WriteLine("4. 恢复数据库 (restore)");
        Console.WriteLine("5. 优化数据库 (optimize)");
        Console.WriteLine("6. 显示统计信息 (stats)");
        Console.WriteLine("7. 测试集成 (test)");
        Console.WriteLine("8. 清理备份 (cleanup)");
        Console.WriteLine("9. 导出数据 (export)");
        Console.WriteLine("0. 退出 (exit)");
        Console.WriteLine();
        Console.Write("请输入选择: ");
    }

    /// <summary>
    /// 初始化数据库
    /// </summary>
    private async Task InitializeDatabaseAsync()
    {
        Console.WriteLine();
        Console.WriteLine("🔥 初始化数据库");
        Console.WriteLine("=====================================");
        
        var result = await _manager.InitializeFullDatabaseAsync();
        
        foreach (var message in result.Messages)
        {
            Console.WriteLine(message);
        }
        
        if (result.IsSuccess)
        {
            Console.WriteLine();
            Console.WriteLine($"✅ 初始化成功！");
            Console.WriteLine($"📊 导入截面数: {result.ImportedSections}");
            Console.WriteLine($"⏱️ 耗时: {result.Duration.TotalSeconds:F1} 秒");
        }
        else
        {
            Console.WriteLine();
            Console.WriteLine($"❌ 初始化失败: {result.ErrorMessage}");
        }
    }

    /// <summary>
    /// 验证数据库
    /// </summary>
    private async Task ValidateDatabaseAsync()
    {
        Console.WriteLine();
        Console.WriteLine("🔍 验证数据库完整性");
        Console.WriteLine("=====================================");
        
        var result = await _manager.ValidateDatabaseIntegrityAsync();
        
        foreach (var message in result.Messages)
        {
            Console.WriteLine(message);
        }
        
        if (result.Issues.Count > 0)
        {
            Console.WriteLine();
            Console.WriteLine("发现的问题:");
            foreach (var issue in result.Issues)
            {
                Console.WriteLine($"⚠️  {issue}");
            }
        }
        
        Console.WriteLine();
        if (result.IsValid)
        {
            Console.WriteLine("✅ 数据库验证通过");
        }
        else
        {
            Console.WriteLine($"❌ 数据库验证失败，发现 {result.Issues.Count} 个问题");
        }
    }

    /// <summary>
    /// 备份数据库
    /// </summary>
    private async Task BackupDatabaseAsync()
    {
        Console.WriteLine();
        Console.WriteLine("💾 备份数据库");
        Console.WriteLine("=====================================");
        
        Console.Write("输入备份名称 (留空使用默认名称): ");
        var backupName = Console.ReadLine()?.Trim();
        
        var result = await _manager.BackupDatabaseAsync(backupName);
        
        if (result.IsSuccess)
        {
            Console.WriteLine($"✅ {result.Message}");
            Console.WriteLine($"📁 大小: {FormatBytes(result.BackupSize)}");
        }
        else
        {
            Console.WriteLine($"❌ 备份失败: {result.ErrorMessage}");
        }
    }

    /// <summary>
    /// 恢复数据库
    /// </summary>
    private async Task RestoreDatabaseAsync()
    {
        Console.WriteLine();
        Console.WriteLine("🔄 恢复数据库");
        Console.WriteLine("=====================================");
        
        // 显示可用备份
        var backups = _manager.GetAvailableBackups();
        if (backups.Count == 0)
        {
            Console.WriteLine("❌ 没有找到备份文件");
            return;
        }
        
        Console.WriteLine("可用备份:");
        for (int i = 0; i < backups.Count; i++)
        {
            var backup = backups[i];
            Console.WriteLine($"{i + 1}. {backup.FileName} - {backup.SizeFormatted} - {backup.CreatedDate:yyyy-MM-dd HH:mm:ss}");
        }
        
        Console.WriteLine();
        Console.Write("选择备份 (输入序号): ");
        var choice = Console.ReadLine()?.Trim();
        
        if (int.TryParse(choice, out int index) && index > 0 && index <= backups.Count)
        {
            var selectedBackup = backups[index - 1];
            
            Console.WriteLine($"⚠️  确认要恢复到备份 '{selectedBackup.FileName}' 吗？");
            Console.WriteLine("这将覆盖当前数据库！");
            Console.Write("输入 'yes' 确认: ");
            
            var confirm = Console.ReadLine()?.Trim().ToLower();
            if (confirm == "yes")
            {
                var result = await _manager.RestoreDatabaseAsync(selectedBackup.FilePath);
                
                foreach (var message in result.Messages)
                {
                    Console.WriteLine(message);
                }
                
                if (result.IsSuccess)
                {
                    Console.WriteLine("✅ 恢复完成");
                }
                else
                {
                    Console.WriteLine($"❌ 恢复失败: {result.ErrorMessage}");
                }
            }
            else
            {
                Console.WriteLine("❌ 已取消恢复操作");
            }
        }
        else
        {
            Console.WriteLine("❌ 无效选择");
        }
    }

    /// <summary>
    /// 优化数据库
    /// </summary>
    private async Task OptimizeDatabaseAsync()
    {
        Console.WriteLine();
        Console.WriteLine("⚡ 优化数据库");
        Console.WriteLine("=====================================");
        
        var result = await _manager.OptimizeDatabaseAsync();
        
        foreach (var message in result.Messages)
        {
            Console.WriteLine(message);
        }
        
        if (result.IsSuccess)
        {
            Console.WriteLine($"✅ 优化完成，节省空间: {FormatBytes(result.SpaceSaved)}");
        }
        else
        {
            Console.WriteLine($"❌ 优化失败: {result.ErrorMessage}");
        }
    }

    /// <summary>
    /// 显示统计信息
    /// </summary>
    private async Task ShowStatisticsAsync()
    {
        Console.WriteLine();
        Console.WriteLine("📊 数据库统计信息");
        Console.WriteLine("=====================================");
        
        try
        {
            var statistics = await _integration.GetDatabaseStatisticsAsync();
            
            Console.WriteLine($"数据库版本: {statistics.DatabaseVersion}");
            Console.WriteLine($"总截面数: {statistics.TotalSections}");
            Console.WriteLine($"材料数: {statistics.TotalMaterials}");
            Console.WriteLine();
            
            Console.WriteLine("各类型截面分布:");
            foreach (var typeCount in statistics.SectionTypeCounts)
            {
                var percentage = statistics.TotalSections > 0 ? 
                    (typeCount.Count * 100.0 / statistics.TotalSections) : 0;
                Console.WriteLine($"  {typeCount.TypeName}: {typeCount.Count} 个 ({percentage:F1}%)");
            }
            
            // 显示数据库文件信息
            var dbPath = StandardSectionDatabase.DefaultDatabasePath;
            if (File.Exists(dbPath))
            {
                var fileInfo = new FileInfo(dbPath);
                Console.WriteLine();
                Console.WriteLine("数据库文件信息:");
                Console.WriteLine($"  路径: {dbPath}");
                Console.WriteLine($"  大小: {FormatBytes(fileInfo.Length)}");
                Console.WriteLine($"  创建时间: {fileInfo.CreationTime:yyyy-MM-dd HH:mm:ss}");
                Console.WriteLine($"  修改时间: {fileInfo.LastWriteTime:yyyy-MM-dd HH:mm:ss}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 获取统计信息失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 测试集成
    /// </summary>
    private async Task TestIntegrationAsync()
    {
        Console.WriteLine();
        Console.WriteLine("🧪 测试系统集成");
        Console.WriteLine("=====================================");
        
        try
        {
            // 测试1: 获取常用截面
            Console.WriteLine("测试1: 获取常用等边角钢...");
            var commonAngles = await _integration.GetCommonStandardFrameTypesAsync(3); // 等边角钢
            Console.WriteLine($"✅ 找到 {commonAngles.Count} 个常用等边角钢");
            
            if (commonAngles.Count > 0)
            {
                var sample = commonAngles.First();
                Console.WriteLine($"   示例: {sample.Name} - B:{sample.B}mm, T:{sample.T}mm");
            }
            
            // 测试2: 查找特定规格
            Console.WriteLine();
            Console.WriteLine("测试2: 查找L50×5等边角钢...");
            var testFrameType = new CurtainWallFrameType { Type = 3, B = 50, H = 50, T = 5 };
            var matchedSection = await _integration.FindMatchingStandardFrameTypeAsync(testFrameType);
            
            if (matchedSection != null)
            {
                Console.WriteLine($"✅ 找到匹配截面: {matchedSection.Name}");
                Console.WriteLine($"   面积: {matchedSection.Area:F2} cm²");
                Console.WriteLine($"   重量: {matchedSection.WeightPerMeter:F2} kg/m");
            }
            else
            {
                Console.WriteLine("❌ 未找到匹配截面");
            }
            
            // 测试3: 验证标准规格
            Console.WriteLine();
            Console.WriteLine("测试3: 验证L75×6是否为标准规格...");
            var testFrameType2 = new CurtainWallFrameType { Type = 3, B = 75, H = 75, T = 6 };
            var isStandard = await _integration.IsStandardSectionAsync(testFrameType2);
            Console.WriteLine($"{(isStandard ? "✅" : "❌")} L75×6 {(isStandard ? "是" : "不是")}标准规格");
            
            // 测试4: 获取截面类型
            Console.WriteLine();
            Console.WriteLine("测试4: 获取所有截面类型...");
            var sectionTypes = await _integration.GetAllSectionTypesAsync();
            Console.WriteLine($"✅ 找到 {sectionTypes.Count} 种截面类型:");
            foreach (var type in sectionTypes)
            {
                Console.WriteLine($"   {type.TypeName} ({type.TypeCode}) - {type.Standard}");
            }
            
            Console.WriteLine();
            Console.WriteLine("✅ 集成测试完成，所有功能正常！");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 集成测试失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 清理备份文件
    /// </summary>
    private async Task CleanupBackupsAsync()
    {
        Console.WriteLine();
        Console.WriteLine("🧹 清理备份文件");
        Console.WriteLine("=====================================");
        
        Console.Write("保留多少天内的备份 (默认30天): ");
        var input = Console.ReadLine()?.Trim();
        
        int keepDays = 30;
        if (!string.IsNullOrEmpty(input) && int.TryParse(input, out int days) && days > 0)
        {
            keepDays = days;
        }
        
        var result = _manager.CleanupOldBackups(keepDays);
        
        foreach (var message in result.Messages)
        {
            Console.WriteLine(message);
        }
        
        if (result.IsSuccess)
        {
            Console.WriteLine($"✅ 清理完成，删除了 {result.DeletedFiles} 个文件，释放空间 {FormatBytes(result.SpaceFreed)}");
        }
        else
        {
            Console.WriteLine($"❌ 清理失败: {result.ErrorMessage}");
        }
    }

    /// <summary>
    /// 导出数据
    /// </summary>
    private async Task ExportDataAsync()
    {
        Console.WriteLine();
        Console.WriteLine("📤 导出数据");
        Console.WriteLine("=====================================");
        
        Console.WriteLine("选择导出格式:");
        Console.WriteLine("1. CSV格式");
        Console.WriteLine("2. JSON格式");
        Console.WriteLine("3. Excel格式");
        Console.Write("请选择: ");
        
        var choice = Console.ReadLine()?.Trim();
        
        switch (choice)
        {
            case "1":
                await ExportToCsvAsync();
                break;
            case "2":
                await ExportToJsonAsync();
                break;
            case "3":
                await ExportToExcelAsync();
                break;
            default:
                Console.WriteLine("❌ 无效选择");
                break;
        }
    }

    /// <summary>
    /// 导出为CSV
    /// </summary>
    private async Task ExportToCsvAsync()
    {
        Console.WriteLine("📄 导出为CSV格式...");
        
        try
        {
            var outputPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), 
                $"StandardSections_{DateTime.Now:yyyyMMdd_HHmmss}.csv");
            
            var statistics = await _integration.GetDatabaseStatisticsAsync();
            
            using (var writer = new StreamWriter(outputPath, false, System.Text.Encoding.UTF8))
            {
                // 写入CSV头
                writer.WriteLine("截面类型,截面代号,截面名称,B,H,T,面积,重量,Ix,Iy,Wx,Wy,常用,推荐");
                
                // 导出各类型截面数据
                var sectionTypes = await _integration.GetAllSectionTypesAsync();
                
                foreach (var sectionType in sectionTypes)
                {
                    var frameTypes = await _integration.GetStandardFrameTypesAsync(GetCurtainWallTypeFromCode(sectionType.TypeCode));
                    
                    foreach (var frameType in frameTypes)
                    {
                        var standardInfo = await frameType.GetStandardInfoAsync();
                        if (standardInfo != null)
                        {
                            writer.WriteLine($"{sectionType.TypeName},{standardInfo.SectionCode},{standardInfo.SectionName}," +
                                           $"{standardInfo.B},{standardInfo.H},{standardInfo.T},{standardInfo.Area}," +
                                           $"{standardInfo.WeightPerMeter},{standardInfo.Ix},{standardInfo.Iy}," +
                                           $"{standardInfo.Wx},{standardInfo.Wy},{standardInfo.IsCommon},{standardInfo.IsPreferred}");
                        }
                    }
                }
            }
            
            Console.WriteLine($"✅ 导出完成: {outputPath}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 导出失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 导出为JSON
    /// </summary>
    private async Task ExportToJsonAsync()
    {
        Console.WriteLine("📄 导出为JSON格式...");
        Console.WriteLine("⚠️  JSON导出功能需要System.Text.Json或Newtonsoft.Json库支持");
        Console.WriteLine("请在实际项目中实现此功能");
    }

    /// <summary>
    /// 导出为Excel
    /// </summary>
    private async Task ExportToExcelAsync()
    {
        Console.WriteLine("📊 导出为Excel格式...");
        Console.WriteLine("⚠️  Excel导出功能需要EPPlus或其他Excel库支持");
        Console.WriteLine("请在实际项目中实现此功能");
    }

    /// <summary>
    /// 从类型代码获取幕墙龙骨类型
    /// </summary>
    private int GetCurtainWallTypeFromCode(string typeCode)
    {
        switch (typeCode?.ToUpper())
        {
            case "AS": return 3;   // 等边角钢
            case "UAS": return 4;  // 不等边角钢
            case "CS": return 5;   // 普通槽钢
            case "LCS": return 6;  // 轻型槽钢
            default: return 0;
        }
    }

    /// <summary>
    /// 格式化字节数
    /// </summary>
    private string FormatBytes(long bytes)
    {
        if (bytes < 1024) return $"{bytes} B";
        if (bytes < 1024 * 1024) return $"{bytes / 1024.0:F1} KB";
        if (bytes < 1024 * 1024 * 1024) return $"{bytes / (1024.0 * 1024):F1} MB";
        return $"{bytes / (1024.0 * 1024 * 1024):F1} GB";
    }
}

/// <summary>
/// 程序入口点
/// </summary>
public class Program
{
    public static async Task Main(string[] args)
    {
        try
        {
            var console = new DatabaseManagerConsole();
            await console.RunAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"程序运行出错: {ex.Message}");
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}