using System;
using System.Collections.Generic;

/// <summary>
/// 矩管方案D使用示例
/// 展示如何使用独立图元架构的矩管和定位点系统
/// </summary>
public class SteelTubePlanDUsageExample
{
    /// <summary>
    /// 基本使用示例
    /// </summary>
    public static void BasicUsageExample()
    {
        Console.WriteLine("=== 矩管方案D基本使用示例 ===");

        // 1. 创建矩管图元
        var insertPoint = new DbPt(100, 100);
        var tube = new SteelTubePlanD(
            insertPt: insertPoint,
            width: 60,
            height: 120,
            thickness: 3,
            hoAngle: 0
        );

        Console.WriteLine($"创建矩管: {tube.Width}×{tube.Height}×{tube.Thickness}");
        Console.WriteLine($"矩管ID: {tube.UniqueId}");

        // 2. 创建定位点图元
        var locatorPosition = new DbPt(100, 10); // 矩管下方30mm处
        var locator = new TubeLocatorPoint(
            position: locatorPosition,
            locatorType: TubeLocatorPoint.TubeLocatorType.BottomCenter,
            boundTubeId: tube.UniqueId,
            offsetX: 0,
            offsetY: 30
        );

        Console.WriteLine($"创建定位点: {locator.LocatorType}");
        Console.WriteLine($"定位点ID: {locator.UniqueId}");

        // 3. 建立绑定关系
        bool bindingSuccess = TubeLocatorBindingManager.CreateBinding(
            tubeId: tube.UniqueId,
            locatorId: locator.UniqueId,
            locatorType: locator.LocatorType
        );

        Console.WriteLine($"绑定关系创建: {(bindingSuccess ? "成功" : "失败")}");

        // 4. 添加到视图（模拟）
        Console.WriteLine("添加图元到视图:");
        Console.WriteLine($"  - 矩管图元: {tube.GetType().Name}");
        Console.WriteLine($"  - 定位点图元: {locator.GetType().Name}");
        
        // 实际使用时的代码：
        // EbDb.ActivVi2D.GView.AddElement(tube);
        // EbDb.ActivVi2D.GView.AddElement(locator);

        Console.WriteLine("基本使用示例完成\n");
    }

    /// <summary>
    /// 多定位点示例
    /// </summary>
    public static void MultipleLocatorsExample()
    {
        Console.WriteLine("=== 多定位点使用示例 ===");

        // 创建矩管
        var tube = new SteelTubePlanD(new DbPt(200, 200), 80, 160, 4);
        Console.WriteLine($"创建矩管: {tube.Width}×{tube.Height}×{tube.Thickness}");

        // 创建多个定位点
        var locatorTypes = new[]
        {
            TubeLocatorPoint.TubeLocatorType.TopCenter,
            TubeLocatorPoint.TubeLocatorType.BottomCenter,
            TubeLocatorPoint.TubeLocatorType.LeftCenter,
            TubeLocatorPoint.TubeLocatorType.RightCenter,
            TubeLocatorPoint.TubeLocatorType.GeometryCenter
        };

        var locators = new List<TubeLocatorPoint>();

        foreach (var locatorType in locatorTypes)
        {
            // 计算定位点位置
            var referencePoint = tube.GetReferencePoint(locatorType);
            var locatorPosition = CalculateLocatorPosition(referencePoint, locatorType);

            var locator = new TubeLocatorPoint(
                position: locatorPosition,
                locatorType: locatorType,
                boundTubeId: tube.UniqueId
            );

            locators.Add(locator);

            // 建立绑定关系
            TubeLocatorBindingManager.CreateBinding(
                tubeId: tube.UniqueId,
                locatorId: locator.UniqueId,
                locatorType: locatorType
            );

            Console.WriteLine($"创建定位点: {locatorType}");
        }

        // 查询绑定关系
        var boundLocatorIds = TubeLocatorBindingManager.GetTubeLocators(tube.UniqueId);
        Console.WriteLine($"矩管绑定的定位点数量: {boundLocatorIds.Count}");

        Console.WriteLine("多定位点示例完成\n");
    }

    /// <summary>
    /// 智能拉伸示例
    /// </summary>
    public static void SmartStretchExample()
    {
        Console.WriteLine("=== 智能拉伸使用示例 ===");

        // 创建矩管和定位点
        var tube = new SteelTubePlanD(new DbPt(300, 300), 60, 120, 3);
        var locator = new TubeLocatorPoint(
            position: new DbPt(300, 180),
            locatorType: TubeLocatorPoint.TubeLocatorType.BottomCenter,
            boundTubeId: tube.UniqueId
        );

        TubeLocatorBindingManager.CreateBinding(
            tubeId: tube.UniqueId,
            locatorId: locator.UniqueId,
            locatorType: locator.LocatorType
        );

        Console.WriteLine($"初始矩管尺寸: {tube.Width}×{tube.Height}");

        // 模拟垂直拉伸
        var stretchVector = new DbPt(0, 20); // 向下拉伸20mm
        TubeLocatorBindingManager.PerformSmartStretch(
            locatorId: locator.UniqueId,
            stretchVector: stretchVector
        );

        Console.WriteLine($"垂直拉伸后矩管尺寸: {tube.Width}×{tube.Height}");

        // 创建水平定位点进行水平拉伸
        var rightLocator = new TubeLocatorPoint(
            position: new DbPt(330, 300),
            locatorType: TubeLocatorPoint.TubeLocatorType.RightCenter,
            boundTubeId: tube.UniqueId
        );

        TubeLocatorBindingManager.CreateBinding(
            tubeId: tube.UniqueId,
            locatorId: rightLocator.UniqueId,
            locatorType: rightLocator.LocatorType
        );

        // 模拟水平拉伸
        var horizontalStretch = new DbPt(15, 0); // 向右拉伸15mm
        TubeLocatorBindingManager.PerformSmartStretch(
            locatorId: rightLocator.UniqueId,
            stretchVector: horizontalStretch
        );

        Console.WriteLine($"水平拉伸后矩管尺寸: {tube.Width}×{tube.Height}");
        Console.WriteLine("智能拉伸示例完成\n");
    }

    /// <summary>
    /// 位置同步示例
    /// </summary>
    public static void PositionSyncExample()
    {
        Console.WriteLine("=== 位置同步使用示例 ===");

        // 创建矩管和定位点
        var tube = new SteelTubePlanD(new DbPt(400, 400), 60, 120, 3);
        var locator = new TubeLocatorPoint(
            position: new DbPt(400, 340),
            locatorType: TubeLocatorPoint.TubeLocatorType.TopCenter,
            boundTubeId: tube.UniqueId
        );

        TubeLocatorBindingManager.CreateBinding(
            tubeId: tube.UniqueId,
            locatorId: locator.UniqueId,
            locatorType: locator.LocatorType
        );

        Console.WriteLine($"初始矩管中心: ({tube._centerPt.X:F1}, {tube._centerPt.Y:F1})");
        Console.WriteLine($"初始定位点位置: ({locator.Position.X:F1}, {locator.Position.Y:F1})");

        // 移动矩管，定位点应该自动跟随
        tube.EleMove(50, 30);
        
        // 同步定位点位置
        TubeLocatorBindingManager.SyncLocatorPosition(
            tubeId: tube.UniqueId,
            locatorId: locator.UniqueId,
            locatorType: locator.LocatorType
        );

        Console.WriteLine($"移动后矩管中心: ({tube._centerPt.X:F1}, {tube._centerPt.Y:F1})");
        Console.WriteLine($"同步后定位点位置: ({locator.Position.X:F1}, {locator.Position.Y:F1})");

        // 移动定位点，矩管应该自动跟随
        var newLocatorPosition = new DbPt(500, 400);
        TubeLocatorBindingManager.SyncTubePosition(
            locatorId: locator.UniqueId,
            newPosition: newLocatorPosition
        );

        Console.WriteLine($"定位点移动到: ({newLocatorPosition.X:F1}, {newLocatorPosition.Y:F1})");
        Console.WriteLine($"矩管中心同步到: ({tube._centerPt.X:F1}, {tube._centerPt.Y:F1})");
        Console.WriteLine("位置同步示例完成\n");
    }

    /// <summary>
    /// 绑定关系管理示例
    /// </summary>
    public static void BindingManagementExample()
    {
        Console.WriteLine("=== 绑定关系管理示例 ===");

        // 创建多个矩管和定位点
        var tube1 = new SteelTubePlanD(new DbPt(500, 500), 60, 120, 3);
        var tube2 = new SteelTubePlanD(new DbPt(600, 500), 80, 100, 4);

        var locator1 = new TubeLocatorPoint(new DbPt(500, 440), TubeLocatorPoint.TubeLocatorType.TopCenter);
        var locator2 = new TubeLocatorPoint(new DbPt(500, 380), TubeLocatorPoint.TubeLocatorType.BottomCenter);
        var locator3 = new TubeLocatorPoint(new DbPt(600, 450), TubeLocatorPoint.TubeLocatorType.TopCenter);

        // 建立绑定关系
        TubeLocatorBindingManager.CreateBinding(tube1.UniqueId, locator1.UniqueId, locator1.LocatorType);
        TubeLocatorBindingManager.CreateBinding(tube1.UniqueId, locator2.UniqueId, locator2.LocatorType);
        TubeLocatorBindingManager.CreateBinding(tube2.UniqueId, locator3.UniqueId, locator3.LocatorType);

        // 查询绑定关系
        var tube1Locators = TubeLocatorBindingManager.GetTubeLocators(tube1.UniqueId);
        var tube2Locators = TubeLocatorBindingManager.GetTubeLocators(tube2.UniqueId);

        Console.WriteLine($"矩管1绑定的定位点数量: {tube1Locators.Count}");
        Console.WriteLine($"矩管2绑定的定位点数量: {tube2Locators.Count}");

        // 检查绑定关系
        bool isBinding = TubeLocatorBindingManager.IsBindingExists(tube1.UniqueId, locator1.UniqueId);
        Console.WriteLine($"矩管1与定位点1的绑定关系: {(isBinding ? "存在" : "不存在")}");

        // 获取定位点绑定的矩管
        string boundTube = TubeLocatorBindingManager.GetLocatorTube(locator1.UniqueId);
        Console.WriteLine($"定位点1绑定的矩管: {(string.IsNullOrEmpty(boundTube) ? "无" : "存在")}");

        // 移除特定绑定关系
        TubeLocatorBindingManager.RemoveBinding(tube1.UniqueId, locator2.UniqueId);
        tube1Locators = TubeLocatorBindingManager.GetTubeLocators(tube1.UniqueId);
        Console.WriteLine($"移除绑定后矩管1的定位点数量: {tube1Locators.Count}");

        // 清理矩管的所有绑定关系
        TubeLocatorBindingManager.ClearTubeBindings(tube1.UniqueId);
        tube1Locators = TubeLocatorBindingManager.GetTubeLocators(tube1.UniqueId);
        Console.WriteLine($"清理后矩管1的定位点数量: {tube1Locators.Count}");

        // 获取统计信息
        string statistics = TubeLocatorBindingManager.GetBindingStatistics();
        Console.WriteLine($"绑定关系统计: {statistics}");

        Console.WriteLine("绑定关系管理示例完成\n");
    }

    /// <summary>
    /// 错误处理示例
    /// </summary>
    public static void ErrorHandlingExample()
    {
        Console.WriteLine("=== 错误处理示例 ===");

        try
        {
            // 尝试创建无效的绑定关系
            bool result1 = TubeLocatorBindingManager.CreateBinding("", "validId", TubeLocatorPoint.TubeLocatorType.TopCenter);
            Console.WriteLine($"空矩管ID绑定结果: {result1}");

            bool result2 = TubeLocatorBindingManager.CreateBinding("validId", "", TubeLocatorPoint.TubeLocatorType.TopCenter);
            Console.WriteLine($"空定位点ID绑定结果: {result2}");

            // 尝试获取不存在的绑定关系
            var nonExistentLocators = TubeLocatorBindingManager.GetTubeLocators("nonExistentTubeId");
            Console.WriteLine($"不存在的矩管ID查询结果: {nonExistentLocators.Count}个定位点");

            string nonExistentTube = TubeLocatorBindingManager.GetLocatorTube("nonExistentLocatorId");
            Console.WriteLine($"不存在的定位点ID查询结果: {(string.IsNullOrEmpty(nonExistentTube) ? "无绑定" : "有绑定")}");

            // 尝试移除不存在的绑定关系
            bool removeResult = TubeLocatorBindingManager.RemoveBinding("nonExistentTube", "nonExistentLocator");
            Console.WriteLine($"移除不存在绑定关系结果: {removeResult}");

        }
        catch (Exception ex)
        {
            Console.WriteLine($"捕获异常: {ex.Message}");
        }

        Console.WriteLine("错误处理示例完成\n");
    }

    /// <summary>
    /// 计算定位点位置
    /// </summary>
    private static DbPt CalculateLocatorPosition(DbPt referencePoint, TubeLocatorPoint.TubeLocatorType locatorType)
    {
        // 根据定位点类型添加默认偏移
        switch (locatorType)
        {
            case TubeLocatorPoint.TubeLocatorType.TopCenter:
                return new DbPt(referencePoint.X, referencePoint.Y + 30);
            case TubeLocatorPoint.TubeLocatorType.BottomCenter:
                return new DbPt(referencePoint.X, referencePoint.Y - 30);
            case TubeLocatorPoint.TubeLocatorType.LeftCenter:
                return new DbPt(referencePoint.X - 30, referencePoint.Y);
            case TubeLocatorPoint.TubeLocatorType.RightCenter:
                return new DbPt(referencePoint.X + 30, referencePoint.Y);
            case TubeLocatorPoint.TubeLocatorType.GeometryCenter:
                return referencePoint.EleCopy();
            default:
                return referencePoint.EleCopy();
        }
    }

    /// <summary>
    /// 运行所有示例
    /// </summary>
    public static void RunAllExamples()
    {
        Console.WriteLine("开始运行矩管方案D使用示例...\n");

        BasicUsageExample();
        MultipleLocatorsExample();
        SmartStretchExample();
        PositionSyncExample();
        BindingManagementExample();
        ErrorHandlingExample();

        Console.WriteLine("所有示例运行完成！");
        Console.WriteLine("\n总结:");
        Console.WriteLine("- 矩管方案D采用独立图元架构，符合CAD平台设计原则");
        Console.WriteLine("- 定位点作为独立图元，可以单独选择、编辑和删除");
        Console.WriteLine("- 绑定管理器提供完整的关系管理和智能拉伸功能");
        Console.WriteLine("- 系统具有良好的错误处理和线程安全特性");
    }
}

/// <summary>
/// 性能测试示例
/// </summary>
public class PerformanceTestExample
{
    /// <summary>
    /// 大量图元性能测试
    /// </summary>
    public static void LargeScaleTest()
    {
        Console.WriteLine("=== 大量图元性能测试 ===");

        var startTime = DateTime.Now;

        // 创建大量矩管和定位点
        const int tubeCount = 100;
        const int locatorsPerTube = 4;

        var tubes = new List<SteelTubePlanD>();
        var locators = new List<TubeLocatorPoint>();

        for (int i = 0; i < tubeCount; i++)
        {
            // 创建矩管
            var tube = new SteelTubePlanD(
                new DbPt(i * 100, i * 50),
                60 + i % 40,
                120 + i % 80,
                3 + i % 3
            );
            tubes.Add(tube);

            // 为每个矩管创建多个定位点
            var locatorTypes = new[]
            {
                TubeLocatorPoint.TubeLocatorType.TopCenter,
                TubeLocatorPoint.TubeLocatorType.BottomCenter,
                TubeLocatorPoint.TubeLocatorType.LeftCenter,
                TubeLocatorPoint.TubeLocatorType.RightCenter
            };

            for (int j = 0; j < locatorsPerTube; j++)
            {
                var locatorType = locatorTypes[j];
                var referencePoint = tube.GetReferencePoint(locatorType);
                
                var locator = new TubeLocatorPoint(
                    position: referencePoint,
                    locatorType: locatorType,
                    boundTubeId: tube.UniqueId
                );
                locators.Add(locator);

                // 建立绑定关系
                TubeLocatorBindingManager.CreateBinding(
                    tubeId: tube.UniqueId,
                    locatorId: locator.UniqueId,
                    locatorType: locatorType
                );
            }
        }

        var creationTime = DateTime.Now;
        Console.WriteLine($"创建{tubeCount}个矩管和{tubeCount * locatorsPerTube}个定位点耗时: {(creationTime - startTime).TotalMilliseconds:F1}ms");

        // 测试查询性能
        var queryStartTime = DateTime.Now;
        
        for (int i = 0; i < tubeCount; i++)
        {
            var boundLocators = TubeLocatorBindingManager.GetTubeLocators(tubes[i].UniqueId);
            // 模拟一些查询操作
        }

        var queryEndTime = DateTime.Now;
        Console.WriteLine($"查询{tubeCount}次绑定关系耗时: {(queryEndTime - queryStartTime).TotalMilliseconds:F1}ms");

        // 测试同步性能
        var syncStartTime = DateTime.Now;
        
        for (int i = 0; i < Math.Min(10, tubeCount); i++)
        {
            // 移动矩管并同步定位点
            tubes[i].EleMove(10, 10);
            
            var boundLocators = TubeLocatorBindingManager.GetTubeLocators(tubes[i].UniqueId);
            foreach (string locatorId in boundLocators)
            {
                // 模拟同步操作
            }
        }

        var syncEndTime = DateTime.Now;
        Console.WriteLine($"同步10个矩管的定位点耗时: {(syncEndTime - syncStartTime).TotalMilliseconds:F1}ms");

        // 清理测试数据
        TubeLocatorBindingManager.ClearAllBindings();

        var totalTime = DateTime.Now;
        Console.WriteLine($"总耗时: {(totalTime - startTime).TotalMilliseconds:F1}ms");
        Console.WriteLine("大量图元性能测试完成\n");
    }
} 