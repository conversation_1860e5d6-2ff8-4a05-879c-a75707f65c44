using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using GuiDB;
using EBCore;

/// <summary>
/// 幕墙龙骨基类 - 参考结构柱系统的Column类架构
/// 核心理念：弱引用关联截面类型，支持虚拟截面机制，所有龙骨都有定位点功能（可开启/关闭）
/// </summary>
[Serializable]
[DbElement("幕墙龙骨", MajorType.CurtainWall, "默认", true)]
public class CurtainWallFrame : DbElement
{
    #region 🔥 核心属性：完全参考结构柱系统

    /// <summary>
    /// 🎯 截面类型UniqueId（弱引用关联）
    /// 参考结构柱的 _typeUId
    /// </summary>
    protected internal string _frameTypeUId = "xxxx";

    /// <summary>
    /// 🎯 截面类型UniqueId属性（用于属性栏显示）
    /// </summary>
    [Browsable(false), DisplayName("截面ID")]
    public string FrameTypeUId
    {
        get { return _frameTypeUId; }
        set
        {
            TransManager.Instance().Push(a => FrameTypeUId = a, _frameTypeUId);
            _frameTypeUId = value;
            if (AutoActivate) { ActCutCal2D3D(); }
        }
    }

    /// <summary>
    /// 🎯 截面类型运行时引用（不保存到文件）
    /// 参考结构柱的 ColType
    /// </summary>
    internal CurtainWallFrameType FrameType = null;

    /// <summary>
    /// 🎯 龙骨几何属性（参考结构柱）
    /// </summary>
    private double _frameAngle = 0.0;
    [Category("几何属性"), DisplayName("旋转角度"), Description("龙骨绕插入点的旋转角度（弧度）"), ReadOnly(false)]
    public double FrameAngle
    {
        get { return _frameAngle; }
        set
        {
            TransManager.Instance().Push(a => FrameAngle = a, _frameAngle);
            _frameAngle = value;
            if (AutoActivate) { ActCutCal2D3D(); }
        }
    }

    /// <summary>
    /// 🎯 龙骨长度
    /// </summary>
    private double _frameLength = 3000.0;
    [Category("几何属性"), DisplayName("长度(mm)"), Description("龙骨的长度"), ReadOnly(false)]
    public double FrameLength
    {
        get { return _frameLength; }
        set
        {
            TransManager.Instance().Push(a => FrameLength = a, _frameLength);
            _frameLength = value;
            if (AutoActivate) { ActCutCal2D3D(); }
        }
    }

    /// <summary>
    /// 🎯 材料类型（用于属性栏显示）
    /// </summary>
    [Category("材料属性"), DisplayName("材料类型"), Description("龙骨的材料类型"), ReadOnly(false)]
    public CurtainWallMaterial MaterialType
    {
        get { return FrameType?.Material ?? CurtainWallMaterial.Aluminum; }
        set
        {
            if (FrameType != null)
            {
                FrameType.Material = value;
                if (AutoActivate) { ActCutCal2D3D(); }
            }
        }
    }

    #endregion

    #region 🔥 定位点功能：所有龙骨都有定位点功能（可开启/关闭）

    /// <summary>
    /// 🎯 是否启用定位点功能
    /// </summary>
    protected bool _enableAnchor = false;
    [Category("定位点控制"), DisplayName("启用定位点"), Description("是否启用定位点控制功能"), ReadOnly(false)]
    public virtual bool EnableAnchor
    {
        get { return _enableAnchor; }
        set
        {
            if (_enableAnchor == value) return;
            TransManager.Instance().Push(a => EnableAnchor = a, _enableAnchor);
            _enableAnchor = value;
            if (AutoActivate) { ActCutCal2D3D(); }
        }
    }

    /// <summary>
    /// 🎯 定位点位置
    /// </summary>
    protected DbPt _anchorPosition = new DbPt();

    /// <summary>
    /// 🎯 X方向偏移量（龙骨相对定位点的偏移，定位点不动）
    /// </summary>
    protected double _offsetX = 0.0;
    [Category("定位控制"), DisplayName("X偏移"), Description("龙骨相对定位点的X方向偏移距离（定位点不动）"), ReadOnly(false)]
    public virtual double OffsetX
    {
        get { return _offsetX; }
        set
        {
            if (Math.Abs(_offsetX - value) < 0.001) return;
            TransManager.Instance().Push(a => OffsetX = a, _offsetX);
            _offsetX = value;
            if (AutoActivate && _enableAnchor) { ActCutCal2D3D(); }
        }
    }

    /// <summary>
    /// 🎯 Y方向偏移量（龙骨相对定位点的偏移，定位点不动）
    /// </summary>
    protected double _offsetY = 0.0;
    [Category("定位控制"), DisplayName("Y偏移"), Description("龙骨相对定位点的Y方向偏移距离（定位点不动）"), ReadOnly(false)]
    public virtual double OffsetY
    {
        get { return _offsetY; }
        set
        {
            if (Math.Abs(_offsetY - value) < 0.001) return;
            TransManager.Instance().Push(a => OffsetY = a, _offsetY);
            _offsetY = value;
            if (AutoActivate && _enableAnchor) { ActCutCal2D3D(); }
        }
    }

    #endregion

    #region 🔥 虚拟截面机制：支持单个龙骨参数修改

    /// <summary>
    /// 🎯 虚拟截面类型（图元私有，不保存UniqueId）
    /// </summary>
    private CurtainWallFrameType _virtualFrameType = null;

    /// <summary>
    /// 🎯 是否使用虚拟截面
    /// </summary>
    private bool _useVirtualType = false;

    /// <summary>
    /// 🎯 虚拟截面参数：宽度覆盖
    /// </summary>
    private int? _virtualB = null;

    /// <summary>
    /// 🎯 虚拟截面参数：高度覆盖
    /// </summary>
    private int? _virtualH = null;

    /// <summary>
    /// 🎯 虚拟截面参数：壁厚覆盖
    /// </summary>
    private int? _virtualT = null;

    /// <summary>
    /// 🎯 虚拟截面参数：圆角半径覆盖
    /// </summary>
    private int? _virtualD = null;

    /// <summary>
    /// 🎯 宽度属性（智能处理虚拟截面）
    /// </summary>
    [Category("截面参数"), DisplayName("宽度(mm)"), Description("龙骨截面宽度"), ReadOnly(false)]
    public int Width
    {
        get
        {
            if (_useVirtualType && _virtualB.HasValue)
                return _virtualB.Value;
            return FrameType?.B ?? 60;
        }
        set
        {
            if (FrameType == null) return;

            // 🔥 关键：参数修改时自动创建虚拟截面
            EnsureVirtualFrameType();
            _virtualB = value;
            _virtualFrameType.B = value;

            if (AutoActivate) { ActCutCal2D3D(); }

            // 🔥 检查是否可以回归到共享截面
            CheckAndRevertToSharedType();
        }
    }

    /// <summary>
    /// 🎯 高度属性（智能处理虚拟截面）
    /// </summary>
    [Category("截面参数"), DisplayName("高度(mm)"), Description("龙骨截面高度"), ReadOnly(false)]
    public int Height
    {
        get
        {
            if (_useVirtualType && _virtualH.HasValue)
                return _virtualH.Value;
            return FrameType?.H ?? 120;
        }
        set
        {
            if (FrameType == null) return;

            EnsureVirtualFrameType();
            _virtualH = value;
            _virtualFrameType.H = value;

            if (AutoActivate) { ActCutCal2D3D(); }
            CheckAndRevertToSharedType();
        }
    }

    /// <summary>
    /// 🎯 壁厚属性（智能处理虚拟截面）
    /// </summary>
    [Category("截面参数"), DisplayName("壁厚(mm)"), Description("龙骨截面壁厚"), ReadOnly(false)]
    public int Thickness
    {
        get
        {
            if (_useVirtualType && _virtualT.HasValue)
                return _virtualT.Value;
            return FrameType?.T ?? 3;
        }
        set
        {
            if (FrameType == null) return;

            EnsureVirtualFrameType();
            _virtualT = value;
            _virtualFrameType.T = value;

            if (AutoActivate) { ActCutCal2D3D(); }
            CheckAndRevertToSharedType();
        }
    }

    /// <summary>
    /// 🎯 圆角半径属性（智能处理虚拟截面）
    /// </summary>
    [Category("截面参数"), DisplayName("圆角半径(mm)"), Description("龙骨截面圆角半径"), ReadOnly(false)]
    public int CornerRadius
    {
        get
        {
            if (_useVirtualType && _virtualD.HasValue)
                return _virtualD.Value;
            return FrameType?.D ?? 0;
        }
        set
        {
            if (FrameType == null) return;

            EnsureVirtualFrameType();
            _virtualD = value;
            _virtualFrameType.D = value;

            if (AutoActivate) { ActCutCal2D3D(); }
            CheckAndRevertToSharedType();
        }
    }

    /// <summary>
    /// 🎯 截面类型名称（只读显示）
    /// </summary>
    [Category("截面信息"), DisplayName("截面类型"), Description("当前使用的截面类型"), ReadOnly(true)]
    public string SectionTypeName
    {
        get
        {
            if (_useVirtualType)
                return $"{FrameType?.GetSecName()} (个性化)";
            return FrameType?.GetSecName() ?? "未设置";
        }
    }

    /// <summary>
    /// 🎯 个性化状态（只读显示）
    /// </summary>
    [Category("截面信息"), DisplayName("个性化状态"), Description("当前图元的个性化状态"), ReadOnly(true)]
    public string CustomizationStatus
    {
        get
        {
            if (!_useVirtualType)
                return "使用标准截面";

            var customizations = new List<string>();
            if (_virtualB.HasValue) customizations.Add($"宽度:{_virtualB}");
            if (_virtualH.HasValue) customizations.Add($"高度:{_virtualH}");
            if (_virtualT.HasValue) customizations.Add($"壁厚:{_virtualT}");
            if (_virtualD.HasValue) customizations.Add($"圆角:{_virtualD}");

            return $"个性化参数: {string.Join(", ", customizations)}";
        }
    }

    #endregion

    #region 🔥 抽象方法：子类必须实现

    /// <summary>
    /// 🎯 从定位点计算龙骨实际位置（子类实现具体逻辑）
    /// </summary>
    protected abstract DbPt CalculatePositionFromAnchor();

    /// <summary>
    /// 🎯 通用的位置计算逻辑
    /// </summary>
    protected virtual DbPt CalculateDrawPosition()
    {
        return _enableAnchor ? CalculatePositionFromAnchor() : _insertPt;
    }

    #endregion

    #region 🔥 构造函数：参考结构柱

    public CurtainWallFrame()
    {
        // 默认构造函数
    }

    /// <summary>
    /// 🎯 构造函数（参考结构柱的构造方式）
    /// </summary>
    public CurtainWallFrame(DbPt insertPt, CurtainWallFrameType frameType, double frameLength = 3000.0, double frameAngle = 0.0)
    {
        _insertPt = insertPt;
        _frameLength = frameLength;
        _frameAngle = frameAngle;
        
        SetFrameType(frameType);
    }

    #endregion

    #region 🔥 核心方法：参考结构柱的关联机制

    /// <summary>
    /// 🎯 设置截面类型（参考结构柱的SetColumnType）
    /// </summary>
    public void SetFrameType(CurtainWallFrameType frameType, bool ifActive = false)
    {
        FrameType = frameType;
        _frameTypeUId = frameType?.UniqueId ?? "xxxx";
        
        // 🔥 清除虚拟截面状态
        ResetVirtualType();

        if (ifActive)
        {
            ActCutCal2D3D();
        }
    }

    /// <summary>
    /// 🎯 设置定位点位置
    /// </summary>
    public virtual void SetAnchorPosition(DbPt anchorPos)
    {
        _anchorPosition = anchorPos.EleCopy();
        if (AutoActivate && _enableAnchor) { ActCutCal2D3D(); }
    }

    /// <summary>
    /// 🎯 建立图元与类型的关联（参考结构柱的LinkElementType）
    /// </summary>
    public override DbElementType LinkElementType()
    {
        // 🔥 从项目的截面类型集合中查找
        FrameType = EBDB.Instance.GProject.CurtainWallFrameTypes?.Find(ft => ft.UniqueId == _frameTypeUId);
        return FrameType;
    }

    #endregion

    #region 🔥 虚拟截面核心方法

    /// <summary>
    /// 🎯 确保虚拟截面存在
    /// </summary>
    private void EnsureVirtualFrameType()
    {
        if (!_useVirtualType && FrameType != null)
        {
            // 🔥 创建虚拟截面：克隆当前截面但不保存UniqueId
            _virtualFrameType = FrameType.Clone();
            _useVirtualType = true;
        }
    }

    /// <summary>
    /// 🎯 检查并回归到共享截面
    /// </summary>
    private void CheckAndRevertToSharedType()
    {
        if (!_useVirtualType || FrameType == null || _virtualFrameType == null)
            return;

        // 🔥 检查虚拟截面是否与原始截面相同
        bool canRevert = true;
        if (_virtualB.HasValue && _virtualB.Value != FrameType.B) canRevert = false;
        if (_virtualH.HasValue && _virtualH.Value != FrameType.H) canRevert = false;
        if (_virtualT.HasValue && _virtualT.Value != FrameType.T) canRevert = false;
        if (_virtualD.HasValue && _virtualD.Value != FrameType.D) canRevert = false;

        if (canRevert)
        {
            // 🔥 回归到共享截面
            ResetVirtualType();
        }
    }

    /// <summary>
    /// 🎯 重置虚拟截面状态
    /// </summary>
    private void ResetVirtualType()
    {
        _useVirtualType = false;
        _virtualFrameType = null;
        _virtualB = null;
        _virtualH = null;
        _virtualT = null;
        _virtualD = null;
    }

    /// <summary>
    /// 🎯 获取当前有效的截面类型
    /// </summary>
    private CurtainWallFrameType GetCurrentFrameType()
    {
        return _useVirtualType ? _virtualFrameType : FrameType;
    }

    /// <summary>
    /// 🎯 重置为截面默认值
    /// </summary>
    public void ResetToTypeDefaults()
    {
        if (FrameType == null) return;

        ResetVirtualType();
        if (AutoActivate) { ActCutCal2D3D(); }
    }

    /// <summary>
    /// 🎯 保存为新的截面类型
    /// </summary>
    public CurtainWallFrameType SaveAsFrameType(string newName)
    {
        if (!_useVirtualType || _virtualFrameType == null)
            return null;

        // 🔥 创建新的全局截面类型
        var newFrameType = _virtualFrameType.EleCopy(true); // 生成新的UniqueId
        newFrameType.Name = newName;

        // 🔥 添加到项目截面库
        EBDB.Instance.GProject.CurtainWallFrameTypes.Add(newFrameType);

        return newFrameType;
    }

    #endregion

    #region 🔥 虚拟方法：子类可以重写

    /// <summary>
    /// 🎯 计算基于定位点的绘制位置（虚拟方法，子类可重写）
    /// </summary>
    /// <returns>实际绘制位置</returns>
    protected virtual DbPt CalculatePositionFromAnchor()
    {
        // 基类提供默认实现：简单的偏移计算
        var basePos = _anchorPosition.EleCopy();
        basePos.X += _offsetX;
        basePos.Y += _offsetY;
        return basePos;
    }

    /// <summary>
    /// 🎯 计算绘制位置的通用框架
    /// </summary>
    /// <returns>最终绘制位置</returns>
    protected virtual DbPt CalculateDrawPosition()
    {
        return _enableAnchor ? CalculatePositionFromAnchor() : _insertPt;
    }

    #endregion

    #region 🔥 数据持久化（参考结构柱）

    /// <summary>
    /// 🎯 保存数据
    /// </summary>
    public override void DataSave(BinaryWriter binaryWriter)
    {
        base.DataSave(binaryWriter);

        // 🔥 保存截面关联
        binaryWriter.Write(_frameTypeUId);

        // 🔥 保存几何属性
        binaryWriter.Write(_frameAngle);
        binaryWriter.Write(_frameLength);

        // 🔥 保存定位点功能数据
        binaryWriter.Write(_enableAnchor);
        binaryWriter.Write(_anchorPosition.X);
        binaryWriter.Write(_anchorPosition.Y);
        binaryWriter.Write(_offsetX);
        binaryWriter.Write(_offsetY);

        // 🔥 保存虚拟截面状态
        binaryWriter.Write(_useVirtualType);
        if (_useVirtualType)
        {
            binaryWriter.Write(_virtualB ?? -1);
            binaryWriter.Write(_virtualH ?? -1);
            binaryWriter.Write(_virtualT ?? -1);
            binaryWriter.Write(_virtualD ?? -1);
        }
    }

    /// <summary>
    /// 🎯 加载数据
    /// </summary>
    public override void DataLoad(BinaryReader binaryReader)
    {
        base.DataLoad(binaryReader);

        // 🔥 加载截面关联
        _frameTypeUId = binaryReader.ReadString();

        // 🔥 加载几何属性
        _frameAngle = binaryReader.ReadDouble();
        _frameLength = binaryReader.ReadDouble();

        // 🔥 加载定位点功能数据
        _enableAnchor = binaryReader.ReadBoolean();
        _anchorPosition = new DbPt(binaryReader.ReadDouble(), binaryReader.ReadDouble());
        _offsetX = binaryReader.ReadDouble();
        _offsetY = binaryReader.ReadDouble();

        // 🔥 加载虚拟截面状态
        _useVirtualType = binaryReader.ReadBoolean();
        if (_useVirtualType)
        {
            int b = binaryReader.ReadInt32();
            int h = binaryReader.ReadInt32();
            int t = binaryReader.ReadInt32();
            int d = binaryReader.ReadInt32();

            _virtualB = b >= 0 ? (int?)b : null;
            _virtualH = h >= 0 ? (int?)h : null;
            _virtualT = t >= 0 ? (int?)t : null;
            _virtualD = d >= 0 ? (int?)d : null;
        }
    }

    /// <summary>
    /// 🎯 深度复制（抽象方法，子类实现）
    /// </summary>
    public abstract override DbElement EleCopy(bool changeUid = false);

    #endregion

    #region 🔥 调试和信息方法

    /// <summary>
    /// 🎯 获取图元提示信息
    /// </summary>
    public override void EleTips(out string str1, out string str2)
    {
        str1 = $"幕墙龙骨: {GetCurrentFrameType()?.GetSecName() ?? "未知"}";
        str2 = $"尺寸: {Width}×{Height}×{Thickness}mm, 长度: {_frameLength:F0}mm";
    }

    #endregion
} 