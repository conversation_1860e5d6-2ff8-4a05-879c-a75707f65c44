using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Windows.Media.Media3D;

/// <summary>
/// 钢矩管 - 方案D：独立图元架构版本
/// 特点：专为独立定位点图元设计，完全分离的架构
/// 优势：符合平台设计原则，架构清晰，易于维护和扩展
/// 架构：纯矩管图元，通过绑定管理器与独立定位点图元协同工作
/// </summary>
[Serializable] //序列化标识，所有图元必须有这个标签
[DbElement("钢矩管D", MajorType.CurtainWall)]
public class SteelTubePlanD : DbElement
{
    #region 几何参数
    /// <summary>
    /// 矩管宽度
    /// </summary>
    private double _width = 60;
    /// <summary>
    /// 矩管宽度
    /// </summary>
    [Category("几何参数"), DisplayName("宽度"), Description("矩管宽度"), ReadOnly(false)]
    public double Width
    {
        get { return _width; }
        set
        {
            if (_width == value) return;
            TransManager.Instance().Push(a => Width = a, _width);
            _width = value;
            
            // 参数修改时几何中心不变，只需要重新计算控制点
            _needRecalcCenter = false;
            _isGeometryOperation = false;
            
            // 通知绑定的定位点更新位置
            NotifyBoundLocators();
            
            ActCutCal2D3D();
        }
    }

    /// <summary>
    /// 矩管高度
    /// </summary>
    private double _height = 120;
    /// <summary>
    /// 矩管高度
    /// </summary>
    [Category("几何参数"), DisplayName("高度"), Description("矩管高度"), ReadOnly(false)]
    public double Height
    {
        get { return _height; }
        set
        {
            if (_height == value) return;
            TransManager.Instance().Push(a => Height = a, _height);
            _height = value;
            
            // 参数修改时几何中心不变，只需要重新计算控制点
            _needRecalcCenter = false;
            _isGeometryOperation = false;
            
            // 通知绑定的定位点更新位置
            NotifyBoundLocators();
            
            ActCutCal2D3D();
        }
    }

    /// <summary>
    /// 壁厚
    /// </summary>
    private double _thickness = 3;
    /// <summary>
    /// 壁厚
    /// </summary>
    [Category("几何参数"), DisplayName("壁厚"), Description("矩管壁厚"), ReadOnly(false)]
    public double Thickness
    {
        get { return _thickness; }
        set
        {
            if (_thickness == value) return;
            TransManager.Instance().Push(a => Thickness = a, _thickness);
            _thickness = value;
            
            // 壁厚改变时，圆角半径会自动重新计算，但几何中心不变
            _needRecalcCenter = false;
            _isGeometryOperation = false;
            
            ActCutCal2D3D();
        }
    }

    /// <summary>
    /// 弯角半径 - 根据壁厚自动计算
    /// </summary>
    [Category("几何参数"), DisplayName("弯角半径"), Description("外弯角半径（根据壁厚自动计算）"), ReadOnly(true)]
    public double Radius
    {
        get { return CalculateRadius(); }
    }

    /// <summary>
    /// 根据壁厚自动计算圆角半径
    /// </summary>
    private double CalculateRadius()
    {
        if (_thickness <= 3)
            return 2 * _thickness;
        else if (_thickness <= 6)
            return 2.5 * _thickness;
        else if (_thickness <= 10)
            return 3 * _thickness;
        else
            return 3 * _thickness;
    }

    /// <summary> 
    /// 截面旋转角度
    /// </summary>
    public double _hoAngle;
    /// <summary>
    /// 截面旋转角度
    /// </summary>
    [Category("几何参数"), DisplayName("截面旋转"), Description("截面旋转角度"), ReadOnly(false)]
    public double HoAngle
    {
        get => _hoAngle;
        set
        {
            if (Math.Abs(_hoAngle - value) < 0.001) return;
            TransManager.Instance().Push(a => HoAngle = a, _hoAngle);
            _hoAngle = value;
            
            // 角度修改时几何中心不变，只需要重新计算控制点
            _needRecalcCenter = false;
            _isGeometryOperation = false;
            
            // 通知绑定的定位点更新位置
            NotifyBoundLocators();

            if (!AutoActivate) return;
            ActCutCal2D3D();
        }
    }

    /// <summary> 
    /// 是否显示填充
    /// </summary>
    public bool _showFill = true;
    /// <summary>
    /// 是否显示填充
    /// </summary>
    [Category("显示属性"), DisplayName("显示填充"), Description("钢矩管截面是否显示填充"), ReadOnly(false)]
    public bool ShowFill
    {
        get { return _showFill; }
        set
        {
            if (_showFill == value) return;
            TransManager.Instance().Push(a => ShowFill = a, _showFill);
            _showFill = value;
            ActCutCal2D3D();
        }
    }

    /// <summary>
    /// 是否显示中心线
    /// </summary>
    public bool _showCenterLines = false;
    /// <summary>
    /// 是否显示中心线
    /// </summary>
    [Category("显示属性"), DisplayName("显示中心线"), Description("是否显示中心十字线"), ReadOnly(false)]
    public bool ShowCenterLines
    {
        get { return _showCenterLines; }
        set
        {
            if (_showCenterLines == value) return;
            TransManager.Instance().Push(a => ShowCenterLines = a, _showCenterLines);
            _showCenterLines = value;
            ActCutCal2D3D();
        }
    }
    #endregion

    #region 偏移参数
    /// <summary>
    /// 左偏移
    /// </summary>
    public double _leftOffset = 30;
    /// <summary>
    /// 左偏移
    /// </summary>
    [Category("位置控制"), DisplayName("左偏移"), Description("插入点到几何中心左侧的距离"), ReadOnly(false)]
    public double LeftOffset
    {
        get => _leftOffset;
        set
        {
            if (Math.Abs(_leftOffset - value) < 0.001) return;
            TransManager.Instance().Push(a => LeftOffset = a, _leftOffset);
            _leftOffset = value;
            _needRecalcCenter = true;
            _isGeometryOperation = false;
            
            // 通知绑定的定位点更新位置
            NotifyBoundLocators();
            
            ActCutCal2D3D();
        }
    }

    /// <summary>
    /// 右偏移
    /// </summary>
    public double _rightOffset = 30;
    /// <summary>
    /// 右偏移
    /// </summary>
    [Category("位置控制"), DisplayName("右偏移"), Description("插入点到几何中心右侧的距离"), ReadOnly(false)]
    public double RightOffset
    {
        get => _rightOffset;
        set
        {
            if (Math.Abs(_rightOffset - value) < 0.001) return;
            TransManager.Instance().Push(a => RightOffset = a, _rightOffset);
            _rightOffset = value;
            _needRecalcCenter = true;
            _isGeometryOperation = false;
            
            // 通知绑定的定位点更新位置
            NotifyBoundLocators();
            
            ActCutCal2D3D();
        }
    }

    /// <summary>
    /// 上偏移
    /// </summary>
    public double _topOffset = 0;
    /// <summary>
    /// 上偏移
    /// </summary>
    [Category("位置控制"), DisplayName("上偏移"), Description("插入点到几何中心上侧的距离"), ReadOnly(false)]
    public double TopOffset
    {
        get => _topOffset;
        set
        {
            if (Math.Abs(_topOffset - value) < 0.001) return;
            TransManager.Instance().Push(a => TopOffset = a, _topOffset);
            _topOffset = value;
            _needRecalcCenter = true;
            _isGeometryOperation = false;
            
            // 通知绑定的定位点更新位置
            NotifyBoundLocators();
            
            ActCutCal2D3D();
        }
    }

    /// <summary>
    /// 下偏移
    /// </summary>
    public double _bottomOffset = 90;
    /// <summary>
    /// 下偏移
    /// </summary>
    [Category("位置控制"), DisplayName("下偏移"), Description("插入点到几何中心下侧的距离"), ReadOnly(false)]
    public double BottomOffset
    {
        get => _bottomOffset;
        set
        {
            if (Math.Abs(_bottomOffset - value) < 0.001) return;
            TransManager.Instance().Push(a => BottomOffset = a, _bottomOffset);
            _bottomOffset = value;
            _needRecalcCenter = true;
            _isGeometryOperation = false;
            
            // 通知绑定的定位点更新位置
            NotifyBoundLocators();
            
            ActCutCal2D3D();
        }
    }
    #endregion

    #region 核心属性
    /// <summary>
    /// 插入点（用户指定的基准点）
    /// </summary>
    public DbPt _insertPt = new DbPt();

    /// <summary>
    /// 几何中心点（根据插入点和偏移参数计算）
    /// </summary>
    public DbPt _centerPt = new DbPt();

    /// <summary>
    /// 移动标识符
    /// </summary>
    private bool _ifMove = false;

    /// <summary>
    /// 拉伸标识符
    /// </summary>
    private bool _ifStretch = false;

    /// <summary>
    /// 是否需要重新计算几何中心
    /// </summary>
    private bool _needRecalcCenter = false;

    /// <summary>
    /// 是否正在进行几何操作
    /// </summary>
    private bool _isGeometryOperation = false;

    /// <summary>
    /// 变换矩阵
    /// </summary>
    private Matrix3D _transformMatrix = Matrix3D.Identity;

    /// <summary>
    /// 是否应用了变换矩阵
    /// </summary>
    private bool _hasTransform = false;
    #endregion

    #region 绑定系统支持
    /// <summary>
    /// 通知绑定的定位点更新位置
    /// </summary>
    private void NotifyBoundLocators()
    {
        // 通过绑定管理器通知所有绑定的定位点更新位置
        var boundLocators = TubeLocatorBindingManager.GetTubeLocators(this.UniqueId);
        foreach (string locatorId in boundLocators)
        {
            // 这里可以添加具体的通知逻辑
            // 实际实现时需要调用绑定管理器的同步方法
        }
    }

    /// <summary>
    /// 获取指定类型的参考点位置
    /// </summary>
    /// <param name="locatorType">定位点类型</param>
    /// <returns>参考点位置</returns>
    public DbPt GetReferencePoint(TubeLocatorPoint.TubeLocatorType locatorType)
    {
        double halfWidth = _width / 2.0;
        double halfHeight = _height / 2.0;
        DbPt center = _centerPt;

        DbPt referencePoint;

        switch (locatorType)
        {
            case TubeLocatorPoint.TubeLocatorType.TopCenter:
                referencePoint = new DbPt(center.X, center.Y + halfHeight);
                break;
            case TubeLocatorPoint.TubeLocatorType.BottomCenter:
                referencePoint = new DbPt(center.X, center.Y - halfHeight);
                break;
            case TubeLocatorPoint.TubeLocatorType.LeftCenter:
                referencePoint = new DbPt(center.X - halfWidth, center.Y);
                break;
            case TubeLocatorPoint.TubeLocatorType.RightCenter:
                referencePoint = new DbPt(center.X + halfWidth, center.Y);
                break;
            case TubeLocatorPoint.TubeLocatorType.GeometryCenter:
                referencePoint = center.EleCopy();
                break;
            default:
                referencePoint = center.EleCopy();
                break;
        }

        // 应用旋转变换
        if (Math.Abs(_hoAngle) > 0.001)
        {
            double angleRad = _hoAngle * Math.PI / 180.0;
            referencePoint.RotateSelf(center, angleRad);
        }

        // 应用变换矩阵
        if (_hasTransform)
        {
            ApplyTransformToPoint(referencePoint, center);
        }

        return referencePoint;
    }
    #endregion

    #region 构造函数
    /// <summary>
    /// 无参构造函数
    /// </summary>
    public SteelTubePlanD()
    {
        _if3D = false;
        LayerSet("幕墙龙骨");
    }

    /// <summary>
    /// 标准构造函数
    /// </summary>
    /// <param name="insertPt">插入点</param>
    /// <param name="width">宽度</param>
    /// <param name="height">高度</param>
    /// <param name="thickness">壁厚</param>
    /// <param name="hoAngle">截面旋转角度</param>
    /// <param name="leftOffset">左偏移</param>
    /// <param name="rightOffset">右偏移</param>
    /// <param name="topOffset">上偏移</param>
    /// <param name="bottomOffset">下偏移</param>
    public SteelTubePlanD(DbPt insertPt, double width = 60.0, double height = 120.0, double thickness = 3.0,
                          double hoAngle = 0.0, double leftOffset = 30, double rightOffset = 30,
                          double topOffset = 0, double bottomOffset = 90)
    {
        _if3D = false;
        LayerSet("幕墙龙骨");
        
        _width = width;
        _height = height;
        _thickness = thickness;
        _hoAngle = hoAngle;
        _insertPt = insertPt;
        _leftOffset = leftOffset;
        _rightOffset = rightOffset;
        _topOffset = topOffset;
        _bottomOffset = bottomOffset;
        _ifHatch = true;

        // 初始化时需要计算几何中心
        _needRecalcCenter = true;
        _isGeometryOperation = false;

        // 计算几何中心点
        CalcuPtcenter();
    }

    /// <summary>
    /// 简化构造函数
    /// </summary>
    /// <param name="insertPt">插入点</param>
    /// <param name="width">宽度</param>
    /// <param name="height">高度</param>
    /// <param name="thickness">壁厚</param>
    /// <param name="hoAngle">截面旋转角度</param>
    public SteelTubePlanD(DbPt insertPt, double width, double height, double thickness = 3.0, double hoAngle = 0.0)
        : this(insertPt, width, height, thickness, hoAngle, 30, 30, 0, 90)
    {
    }
    #endregion

    #region 核心计算方法
    /// <summary>
    /// 计算几何中心点
    /// </summary>
    public void CalcuPtcenter()
    {
        // 使用象限判断算法计算几何中心
        if (_leftOffset >= _rightOffset && _bottomOffset >= _topOffset)
        {
            _centerPt = _insertPt.Move(-(_width / 2 - _rightOffset), -(_height / 2 - _topOffset));
        }
        else if (_leftOffset <= _rightOffset && _topOffset <= _bottomOffset)
        {
            _centerPt = _insertPt.Move(_width / 2 - _leftOffset, -(_height / 2 - _topOffset));
        }
        else if (_leftOffset <= _rightOffset && _topOffset >= _bottomOffset)
        {
            _centerPt = _insertPt.Move((_width / 2 - _leftOffset), (_height / 2 - _bottomOffset));
        }
        else if (_topOffset >= _bottomOffset && _leftOffset >= _rightOffset)
        {
            _centerPt = _insertPt.Move(-(_width / 2 - _rightOffset), _height / 2 - _bottomOffset);
        }
        else
        {
            _centerPt = _insertPt;
        }
    }

    /// <summary>
    /// 计算所有控制点
    /// </summary>
    public void CalcuPts()
    {
        ConPts.Clear();

        // 几何中心作为第一个控制点
        DbPt currentCenter = _centerPt.EleCopy();
        ConPts.Add(currentCenter);

        double halfWidth = _width / 2.0;
        double halfHeight = _height / 2.0;

        // 添加边中点（用于拉伸控制）
        DbPt topMid = new DbPt(currentCenter.X, currentCenter.Y + halfHeight);
        topMid.PtType = 2;
        ConPts.Add(topMid);

        DbPt rightMid = new DbPt(currentCenter.X + halfWidth, currentCenter.Y);
        rightMid.PtType = 2;
        ConPts.Add(rightMid);

        DbPt bottomMid = new DbPt(currentCenter.X, currentCenter.Y - halfHeight);
        bottomMid.PtType = 2;
        ConPts.Add(bottomMid);

        DbPt leftMid = new DbPt(currentCenter.X - halfWidth, currentCenter.Y);
        leftMid.PtType = 2;
        ConPts.Add(leftMid);

        // 添加圆弧控制点
        if (CalculateRadius() > 0.001)
        {
            DbPt[] corners = new DbPt[]
            {
                new DbPt(currentCenter.X - halfWidth, currentCenter.Y - halfHeight),
                new DbPt(currentCenter.X + halfWidth, currentCenter.Y - halfHeight),
                new DbPt(currentCenter.X + halfWidth, currentCenter.Y + halfHeight),
                new DbPt(currentCenter.X - halfWidth, currentCenter.Y + halfHeight),
            };

            AddArcControlPoints(corners);
        }

        // 应用旋转变换
        if (Math.Abs(_hoAngle) > 0.001)
        {
            double angleRad = _hoAngle * Math.PI / 180.0;
            for (int i = 1; i < ConPts.Count; i++)
            {
                ConPts[i].RotateSelf(ConPts[0], angleRad);
            }
        }

        // 应用变换矩阵
        if (_hasTransform)
        {
            ApplyTransformMatrix();
        }
    }

    /// <summary>
    /// 添加圆弧控制点
    /// </summary>
    private void AddArcControlPoints(DbPt[] corners)
    {
        double radius = CalculateRadius();
        if (radius < 0.001) return;

        for (int i = 0; i < 4; i++)
        {
            DbPt currentCorner = corners[i];
            DbPt prevCorner = corners[(i + 3) % 4];
            DbPt nextCorner = corners[(i + 1) % 4];

            DbLine l1 = new DbLine(currentCorner, prevCorner);
            DbLine l2 = new DbLine(currentCorner, nextCorner);

            DbPt dirPre = l1.PtEnd - l1.PtSt;
            DbPt dirNext = l2.PtEnd - l2.PtSt;
            dirPre.Normalize2D();
            dirNext.Normalize2D();

            DbPt arcStart = new DbPt(currentCorner.X + dirPre.X * radius, currentCorner.Y + dirPre.Y * radius);
            arcStart.PtType = 0;

            DbPt arcEnd = new DbPt(currentCorner.X + dirNext.X * radius, currentCorner.Y + dirNext.Y * radius);
            arcEnd.PtType = 0;

            DbPt arcCenter = new DbPt(currentCorner.X + (dirPre.X + dirNext.X) * radius,
                                     currentCorner.Y + (dirPre.Y + dirNext.Y) * radius);

            DbPt arcMid = CalculateOutwardArcMid(arcStart, arcEnd, arcCenter, currentCorner);
            arcMid.PtType = 1;

            ConPts.Add(arcStart);
            ConPts.Add(arcMid);
            ConPts.Add(arcEnd);
        }
    }

    /// <summary>
    /// 计算向外凸出的圆弧中点
    /// </summary>
    private DbPt CalculateOutwardArcMid(DbPt arcStart, DbPt arcEnd, DbPt arcCenter, DbPt cornerPt)
    {
        DbPt midPoint = new DbPt((arcStart.X + arcEnd.X) / 2.0, (arcStart.Y + arcEnd.Y) / 2.0);
        DbPt centerToMid = new DbPt(midPoint.X - arcCenter.X, midPoint.Y - arcCenter.Y);
        DbPt cornerToCenter = new DbPt(arcCenter.X - cornerPt.X, arcCenter.Y - cornerPt.Y);

        centerToMid.Normalize2D();
        cornerToCenter.Normalize2D();

        double radius = GMath.Distance(arcCenter, arcStart);
        double dotProduct = centerToMid.X * cornerToCenter.X + centerToMid.Y * cornerToCenter.Y;

        if (dotProduct > 0)
        {
            centerToMid.X = -centerToMid.X;
            centerToMid.Y = -centerToMid.Y;
        }

        return new DbPt(arcCenter.X + centerToMid.X * radius, arcCenter.Y + centerToMid.Y * radius);
    }

    /// <summary>
    /// 应用变换矩阵
    /// </summary>
    private void ApplyTransformMatrix()
    {
        if (!_hasTransform || ConPts.Count <= 1) return;

        DbPt center = ConPts[0];
        for (int i = 1; i < ConPts.Count; i++)
        {
            ApplyTransformToPoint(ConPts[i], center);
        }
    }

    /// <summary>
    /// 对单个点应用变换矩阵
    /// </summary>
    public void ApplyTransformToPoint(DbPt point, DbPt center)
    {
        double relativeX = point.X - center.X;
        double relativeY = point.Y - center.Y;

        double transformedX = _transformMatrix.M11 * relativeX + _transformMatrix.M12 * relativeY;
        double transformedY = _transformMatrix.M21 * relativeX + _transformMatrix.M22 * relativeY;

        point.X = center.X + transformedX;
        point.Y = center.Y + transformedY;
    }

    /// <summary>
    /// 获取外轮廓点集
    /// </summary>
    private List<DbPt> GetOuterPoints()
    {
        List<DbPt> points = new List<DbPt>();

        if (CalculateRadius() < 0.001)
        {
            double halfWidth = _width / 2.0;
            double halfHeight = _height / 2.0;
            DbPt currentCenter = _centerPt.EleCopy();

            DbPt[] corners = new DbPt[]
            {
                new DbPt(currentCenter.X - halfWidth, currentCenter.Y - halfHeight),
                new DbPt(currentCenter.X + halfWidth, currentCenter.Y - halfHeight),
                new DbPt(currentCenter.X + halfWidth, currentCenter.Y + halfHeight),
                new DbPt(currentCenter.X - halfWidth, currentCenter.Y + halfHeight),
            };

            if (Math.Abs(_hoAngle) > 0.001)
            {
                double angleRad = _hoAngle * Math.PI / 180.0;
                for (int i = 0; i < corners.Length; i++)
                {
                    corners[i].RotateSelf(currentCenter, angleRad);
                }
            }

            if (_hasTransform)
            {
                for (int i = 0; i < corners.Length; i++)
                {
                    ApplyTransformToPoint(corners[i], currentCenter);
                }
            }

            points.AddRange(corners);
            EnsureCounterClockwise(points);
            return points;
        }

        // 从控制点中提取外轮廓点
        for (int i = 5; i < ConPts.Count; i++)
        {
            if (ConPts[i].PtType != 2)
            {
                points.Add(ConPts[i]);
            }
        }

        EnsureCounterClockwise(points);
        return points;
    }

    /// <summary>
    /// 确保点集是逆时针方向
    /// </summary>
    private void EnsureCounterClockwise(List<DbPt> points)
    {
        if (points.Count < 3) return;

        double signedArea = 0;
        for (int i = 0; i < points.Count; i++)
        {
            int j = (i + 1) % points.Count;
            signedArea += (points[j].X - points[i].X) * (points[j].Y + points[i].Y);
        }

        if (signedArea > 0)
        {
            points.Reverse();
        }
    }

    /// <summary>
    /// 将点集转换为线段
    /// </summary>
    private void ConvertPointsToLines(List<DbPt> points)
    {
        if (points == null || points.Count < 3) return;

        for (int i = 0; i < points.Count; i++)
        {
            DbPt currentPt = points[i];
            DbPt nextPt = points[(i + 1) % points.Count];

            if (nextPt.PtType == 1)
            {
                DbPt endPt = points[(i + 2) % points.Count];
                DbLine arcLine = new DbLine(currentPt.EleCopy(), endPt.EleCopy(), nextPt.EleCopy());
                Lines.Add(arcLine);
                i++;
            }
            else
            {
                DbLine line = new DbLine(currentPt.EleCopy(), nextPt.EleCopy());
                Lines.Add(line);
            }
        }
    }
    #endregion

    #region 图元操作方法
    /// <summary>
    /// 图元激活计算
    /// </summary>
    public override void Activate()
    {
        Hatchs.Clear();
        Lines.Clear();

        // 智能控制点管理
        if (_needRecalcCenter)
        {
            CalcuPtcenter();
            CalcuPts();
            _needRecalcCenter = false;
        }
        else if (_isGeometryOperation)
        {
            if (ConPts.Count > 0)
            {
                _centerPt = ConPts[0].EleCopy();
                ExtractTransformFromControlPoints();
            }
        }
        else
        {
            CalcuPts();
        }

        // 生成几何图形
        List<DbPt> outerPoints = GetOuterPoints();
        List<DbPt> innerPoints = GMath.GetOffsetArcPts(outerPoints, _thickness, true);

        ConvertPointsToLines(outerPoints);
        ConvertPointsToLines(innerPoints);

        // 添加填充
        if (_ifHatch)
        {
            DbHatch hatch = new DbHatch(Lines, 1, 1);
            hatch.LayerId = PreLayerManage.GetLayerId("填充层");
            Hatchs.Add(hatch);
        }

        // 设置线段属性
        foreach (DbLine line in Lines)
        {
            line.SetStatus(1, 1, 1);
            line.LayerId = PreLayerManage.GetLayerId("幕墙龙骨");
        }

        LayerChange(layerManage.GetLayer(_layerId));

        if (_styleIndex >= 0) { foreach (DbLine line in Lines) { line.StyleIndex = _styleIndex; } }
        if (_colorIndex >= 0) { foreach (DbLine line in Lines) { line.ColorIndex = _colorIndex; } }
        if (_widthIndex >= 0) { foreach (DbLine line in Lines) { line.WidthIndex = _widthIndex; } }

        EleArea = new DbEleArea(this);
        CalSolid2D();

        // 重置操作状态
        _ifMove = false;
        _ifStretch = false;
        _isGeometryOperation = false;
    }

    /// <summary>
    /// 返回图元提示信息
    /// </summary>
    public override void EleTips(out string str1, out string str2)
    {
        str1 = "矩管方案D";
        str2 = $"{_width:F0}×{_height:F0}×{_thickness:F0}";
        str2 += " [独立架构]";
        
        // 显示绑定的定位点数量
        var boundLocators = TubeLocatorBindingManager.GetTubeLocators(this.UniqueId);
        if (boundLocators.Count > 0)
        {
            str2 += $" [定位点:{boundLocators.Count}个]";
        }
    }

    /// <summary>
    /// 重写移动
    /// </summary>
    public override void EleMove(double X, double Y)
    {
        foreach (DbPt pt in ConPts)
        {
            pt.MoveSelf(X, Y);
        }

        if (ConPts.Count > 0)
        {
            _centerPt = ConPts[0].EleCopy();
        }

        // 通知绑定的定位点更新位置
        NotifyBoundLocators();

        _ifMove = true;
        _isGeometryOperation = true;
        Activate();
    }

    /// <summary>
    /// 图元拉伸控制
    /// </summary>
    public override void EleMove_s(double X, double Y)
    {
        if (ConPts == null || ConPts.Count == 0) return;

        if (ConPts[0].Status == 1)
        {
            EleMove(X, Y);
            return;
        }

        // 边中点拉伸控制
        if (ConPts.Count > 4)
        {
            if (ConPts[1].Status == 1) // 上边中点
            {
                double localDeltaY = CalculateLocalDeltaWithTransform(X, Y, true);
                ConPts[1].MoveSelf(X, Y);
                
                DbPt dragVector = new DbPt(X, Y);
                DbPt awayFromCenter = new DbPt(ConPts[1].X - ConPts[0].X, ConPts[1].Y - ConPts[0].Y);
                double dotProduct = dragVector.X * awayFromCenter.X + dragVector.Y * awayFromCenter.Y;
                
                double heightChange = Math.Abs(localDeltaY) * (dotProduct > 0 ? 1 : -1);
                _height = Math.Max(10, _height + heightChange);
                
                RecalculateControlPointsDirectly(ConPts[0]);
            }
            else if (ConPts[2].Status == 1) // 右边中点
            {
                double localDeltaX = CalculateLocalDeltaWithTransform(X, Y, false);
                ConPts[2].MoveSelf(X, Y);
                
                DbPt dragVector = new DbPt(X, Y);
                DbPt awayFromCenter = new DbPt(ConPts[2].X - ConPts[0].X, ConPts[2].Y - ConPts[0].Y);
                double dotProduct = dragVector.X * awayFromCenter.X + dragVector.Y * awayFromCenter.Y;
                
                double widthChange = Math.Abs(localDeltaX) * (dotProduct > 0 ? 2 : -2);
                _width = Math.Max(10, _width + widthChange);
                
                RecalculateControlPointsDirectly(ConPts[0]);
            }
            else if (ConPts[3].Status == 1) // 下边中点
            {
                double localDeltaY = CalculateLocalDeltaWithTransform(X, Y, true);
                ConPts[3].MoveSelf(X, Y);
                
                DbPt dragVector = new DbPt(X, Y);
                DbPt awayFromCenter = new DbPt(ConPts[3].X - ConPts[0].X, ConPts[3].Y - ConPts[0].Y);
                double dotProduct = dragVector.X * awayFromCenter.X + dragVector.Y * awayFromCenter.Y;
                
                double heightChange = Math.Abs(localDeltaY) * (dotProduct > 0 ? 1 : -1);
                _height = Math.Max(10, _height + heightChange);
                
                RecalculateControlPointsDirectly(ConPts[0]);
            }
            else if (ConPts[4].Status == 1) // 左边中点
            {
                double localDeltaX = CalculateLocalDeltaWithTransform(X, Y, false);
                ConPts[4].MoveSelf(X, Y);
                
                DbPt dragVector = new DbPt(X, Y);
                DbPt awayFromCenter = new DbPt(ConPts[4].X - ConPts[0].X, ConPts[4].Y - ConPts[0].Y);
                double dotProduct = dragVector.X * awayFromCenter.X + dragVector.Y * awayFromCenter.Y;
                
                double widthChange = Math.Abs(localDeltaX) * (dotProduct > 0 ? 2 : -2);
                _width = Math.Max(10, _width + widthChange);
                
                RecalculateControlPointsDirectly(ConPts[0]);
            }
        }

        // 通知绑定的定位点更新位置
        NotifyBoundLocators();

        _ifStretch = true;
        _isGeometryOperation = true;
        Activate();
    }

    /// <summary>
    /// 计算考虑变换的本地坐标增量
    /// </summary>
    private double CalculateLocalDeltaWithTransform(double globalX, double globalY, bool isVertical)
    {
        if (!_hasTransform)
        {
            return CalculateLocalDelta(globalX, globalY, isVertical);
        }

        DbPt globalDelta = new DbPt(globalX, globalY);
        DbPt standardDirection;
        
        if (isVertical)
        {
            standardDirection = new DbPt(-Math.Sin(_hoAngle), Math.Cos(_hoAngle));
        }
        else
        {
            standardDirection = new DbPt(Math.Cos(_hoAngle), Math.Sin(_hoAngle));
        }

        DbPt actualDirection = new DbPt(
            _transformMatrix.M11 * standardDirection.X + _transformMatrix.M12 * standardDirection.Y,
            _transformMatrix.M21 * standardDirection.X + _transformMatrix.M22 * standardDirection.Y
        );

        double dotProduct = globalDelta.X * actualDirection.X + globalDelta.Y * actualDirection.Y;
        double actualDirectionLength = Math.Sqrt(actualDirection.X * actualDirection.X + actualDirection.Y * actualDirection.Y);

        return actualDirectionLength > 0.001 ? dotProduct / actualDirectionLength : 0;
    }

    /// <summary>
    /// 计算全局拖拽向量在本地坐标系中的投影
    /// </summary>
    private double CalculateLocalDelta(double globalX, double globalY, bool isVertical)
    {
        if (Math.Abs(_hoAngle) < 0.001)
        {
            return isVertical ? globalY : globalX;
        }

        double angleRad = _hoAngle * Math.PI / 180.0;
        double cosAngle = Math.Cos(angleRad);
        double sinAngle = Math.Sin(angleRad);

        if (isVertical)
        {
            return globalX * (-sinAngle) + globalY * cosAngle;
        }
        else
        {
            return globalX * cosAngle + globalY * sinAngle;
        }
    }

    /// <summary>
    /// 直接重新计算所有控制点位置
    /// </summary>
    private void RecalculateControlPointsDirectly(DbPt centerPt)
    {
        ConPts[0] = centerPt.EleCopy();
        _centerPt = centerPt.EleCopy();

        double halfWidth = _width / 2.0;
        double halfHeight = _height / 2.0;

        DbPt topMid = new DbPt(centerPt.X, centerPt.Y + halfHeight);
        DbPt rightMid = new DbPt(centerPt.X + halfWidth, centerPt.Y);
        DbPt bottomMid = new DbPt(centerPt.X, centerPt.Y - halfHeight);
        DbPt leftMid = new DbPt(centerPt.X - halfWidth, centerPt.Y);

        if (Math.Abs(_hoAngle) > 0.001)
        {
            double angleRad = _hoAngle * Math.PI / 180.0;
            topMid.RotateSelf(centerPt, angleRad);
            rightMid.RotateSelf(centerPt, angleRad);
            bottomMid.RotateSelf(centerPt, angleRad);
            leftMid.RotateSelf(centerPt, angleRad);
        }

        if (_hasTransform)
        {
            ApplyTransformToPoint(topMid, centerPt);
            ApplyTransformToPoint(rightMid, centerPt);
            ApplyTransformToPoint(bottomMid, centerPt);
            ApplyTransformToPoint(leftMid, centerPt);
        }

        ConPts[1] = topMid;
        ConPts[1].PtType = 2;
        ConPts[2] = rightMid;
        ConPts[2].PtType = 2;
        ConPts[3] = bottomMid;
        ConPts[3].PtType = 2;
        ConPts[4] = leftMid;
        ConPts[4].PtType = 2;

        if (CalculateRadius() > 0.001)
        {
            DbPt[] corners = new DbPt[]
            {
                new DbPt(centerPt.X - halfWidth, centerPt.Y - halfHeight),
                new DbPt(centerPt.X + halfWidth, centerPt.Y - halfHeight),
                new DbPt(centerPt.X + halfWidth, centerPt.Y + halfHeight),
                new DbPt(centerPt.X - halfWidth, centerPt.Y + halfHeight),
            };

            if (Math.Abs(_hoAngle) > 0.001)
            {
                double angleRad = _hoAngle * Math.PI / 180.0;
                for (int i = 0; i < corners.Length; i++)
                {
                    corners[i].RotateSelf(centerPt, angleRad);
                }
            }

            if (_hasTransform)
            {
                for (int i = 0; i < corners.Length; i++)
                {
                    ApplyTransformToPoint(corners[i], centerPt);
                }
            }

            while (ConPts.Count > 5)
            {
                ConPts.RemoveAt(ConPts.Count - 1);
            }

            AddArcControlPoints(corners);
        }
    }

    /// <summary>
    /// 重写旋转
    /// </summary>
    public override void EleMove_r(DbPt rotCenter, double angle)
    {
        foreach (DbPt pt in ConPts)
        {
            pt.RotateSelf(rotCenter, angle);
        }

        if (ConPts.Count > 0)
        {
            _centerPt = ConPts[0].EleCopy();
        }

        _hoAngle += angle * 180 / Math.PI;
        while (_hoAngle > 180) _hoAngle -= 360;
        while (_hoAngle < -180) _hoAngle += 360;

        // 通知绑定的定位点更新位置
        NotifyBoundLocators();

        _ifMove = true;
        _isGeometryOperation = true;
        Activate();
        _ifMove = false;
    }

    /// <summary>
    /// 重写镜像
    /// </summary>
    public override void EleMove_m(DbLine mirrorLine)
    {
        foreach (DbPt pt in ConPts)
        {
            pt.MirrorSelf(mirrorLine);
        }

        if (ConPts.Count > 0)
        {
            _centerPt = ConPts[0].EleCopy();
        }

        UpdateTransformMatrixForMirror(mirrorLine);

        // 通知绑定的定位点更新位置
        NotifyBoundLocators();

        _isGeometryOperation = true;
        _ifMove = true;
        Activate();
        _ifMove = false;
    }

    /// <summary>
    /// 更新变换矩阵以包含镜像
    /// </summary>
    private void UpdateTransformMatrixForMirror(DbLine mirrorLine)
    {
        DbPt lineDir = mirrorLine.PtEnd - mirrorLine.PtSt;
        lineDir.Normalize2D();

        DbPt normal = new DbPt(-lineDir.Y, lineDir.X);
        double nx = normal.X;
        double ny = normal.Y;

        Matrix3D mirrorMatrix = new Matrix3D(
            1 - 2 * nx * nx, -2 * nx * ny, 0, 0,
            -2 * nx * ny, 1 - 2 * ny * ny, 0, 0,
            0, 0, 1, 0,
            0, 0, 0, 1
        );

        _transformMatrix = Matrix3D.Multiply(_transformMatrix, mirrorMatrix);
        _hasTransform = true;
    }

    /// <summary>
    /// 从当前控制点提取变换矩阵
    /// </summary>
    private void ExtractTransformFromControlPoints()
    {
        if (ConPts.Count < 5) return;

        DbPt center = ConPts[0];
        DbPt rightPoint = ConPts[2];
        DbPt topPoint = ConPts[1];

        DbPt xAxis = new DbPt(rightPoint.X - center.X, rightPoint.Y - center.Y);
        DbPt yAxis = new DbPt(topPoint.X - center.X, topPoint.Y - center.Y);

        double xLength = Math.Sqrt(xAxis.X * xAxis.X + xAxis.Y * xAxis.Y);
        double yLength = Math.Sqrt(yAxis.X * yAxis.X + yAxis.Y * yAxis.Y);

        if (xLength > 0.001 && yLength > 0.001)
        {
            xAxis.X /= xLength;
            xAxis.Y /= xLength;
            yAxis.X /= yLength;
            yAxis.Y /= yLength;

            double currentAngle = _hoAngle * Math.PI / 180.0;
            double cos = Math.Cos(-currentAngle);
            double sin = Math.Sin(-currentAngle);

            double xAxisX_noRot = xAxis.X * cos - xAxis.Y * sin;
            double xAxisY_noRot = xAxis.X * sin + xAxis.Y * cos;
            double yAxisX_noRot = yAxis.X * cos - yAxis.Y * sin;
            double yAxisY_noRot = yAxis.X * sin + yAxis.Y * cos;

            _transformMatrix = new Matrix3D(
                xAxisX_noRot, yAxisX_noRot, 0, 0,
                xAxisY_noRot, yAxisY_noRot, 0, 0,
                0, 0, 1, 0,
                0, 0, 0, 1
            );

            _hasTransform = Math.Abs(_transformMatrix.M11 - 1) > 0.001 ||
                           Math.Abs(_transformMatrix.M12) > 0.001 ||
                           Math.Abs(_transformMatrix.M21) > 0.001 ||
                           Math.Abs(_transformMatrix.M22 - 1) > 0.001;
        }
    }
    #endregion

    #region 数据持久化
    /// <summary>
    /// 保存数据
    /// </summary>
    public override void DataSave(BinaryWriter binaryWriter)
    {
        binaryWriter.Write(this.GetType().ToString());
        binaryWriter.Write(1); // 版本号

        PubSave(binaryWriter);

        binaryWriter.Write(_width);
        binaryWriter.Write(_height);
        binaryWriter.Write(_thickness);
        binaryWriter.Write(_hoAngle);
        binaryWriter.Write(_ifHatch);
        binaryWriter.Write(_leftOffset);
        binaryWriter.Write(_rightOffset);
        binaryWriter.Write(_topOffset);
        binaryWriter.Write(_bottomOffset);

        binaryWriter.Write(_hasTransform);
        if (_hasTransform)
        {
            binaryWriter.Write(_transformMatrix.M11);
            binaryWriter.Write(_transformMatrix.M12);
            binaryWriter.Write(_transformMatrix.M21);
            binaryWriter.Write(_transformMatrix.M22);
        }
    }

    /// <summary>
    /// 装载数据
    /// </summary>
    public override void DataLoad(BinaryReader binaryReader)
    {
        int verNum = binaryReader.ReadInt32();
        PubLoad(binaryReader);

        _width = binaryReader.ReadDouble();
        _height = binaryReader.ReadDouble();
        _thickness = binaryReader.ReadDouble();
        _hoAngle = binaryReader.ReadDouble();
        _ifHatch = binaryReader.ReadBoolean();
        _leftOffset = binaryReader.ReadDouble();
        _rightOffset = binaryReader.ReadDouble();
        _topOffset = binaryReader.ReadDouble();
        _bottomOffset = binaryReader.ReadDouble();

        _hasTransform = binaryReader.ReadBoolean();
        if (_hasTransform)
        {
            double m11 = binaryReader.ReadDouble();
            double m12 = binaryReader.ReadDouble();
            double m21 = binaryReader.ReadDouble();
            double m22 = binaryReader.ReadDouble();
            _transformMatrix = new Matrix3D(
                m11, m12, 0, 0,
                m21, m22, 0, 0,
                0, 0, 1, 0,
                0, 0, 0, 1
            );
        }
        else
        {
            _transformMatrix = Matrix3D.Identity;
        }

        _needRecalcCenter = true;
        _isGeometryOperation = false;
    }

    /// <summary>
    /// 深度复制
    /// </summary>
    public override DbElement EleCopy(bool changeUid = false)
    {
        SteelTubePlanD ele = new SteelTubePlanD();

        PubCopy(ele);

        ele._width = _width;
        ele._height = _height;
        ele._thickness = _thickness;
        ele._hoAngle = _hoAngle;
        ele._ifHatch = _ifHatch;
        ele._leftOffset = _leftOffset;
        ele._rightOffset = _rightOffset;
        ele._topOffset = _topOffset;
        ele._bottomOffset = _bottomOffset;

        ele._hasTransform = _hasTransform;
        ele._transformMatrix = _transformMatrix;

        ele._isGeometryOperation = false;

        if (changeUid) { ele.UniqueId = Guid.NewGuid().ToString(); }

        return ele;
    }
    #endregion
} 