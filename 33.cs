    /// <summary>
    /// 建筑柱图元类 - 用于表示建筑设计中的各种柱子构件
    /// 
    /// 功能概述：
    /// - 支持矩形柱、圆形柱、异形柱三种基本截面类型
    /// - 支持垂直柱和斜柱两种空间形式
    /// - 支持面层装饰显示和材料属性设置
    /// - 完整的楼层定位和标高控制系统
    /// - 智能的属性面板动态显示控制
    /// - 专业的3D建模和截面视图生成
    /// 
    /// 技术特点：
    /// - 基于DbElement基类，完全集成到图元系统中
    /// - 实现多个专业接口，支持高级功能扩展
    /// - 使用事务管理系统，支持撤销/重做操作
    /// - 智能的几何计算和控制点管理
    /// - 完整的数据持久化和版本兼容性
    /// 
    /// 支持的柱子类型：
    /// 1. 矩形柱：通过横向宽度和纵向宽度定义
    /// 2. 圆形柱：通过半径或直径定义
    /// 3. 异形柱：通过自定义截面类型定义
    /// 
    /// 支持的空间形式：
    /// 1. 垂直柱：标准的垂直柱子
    /// 2. 斜柱：支持倾斜角度设置的斜柱
    /// 
    /// 高级功能：
    /// - 面层装饰：在适当比例下显示面层厚度
    /// - 材料管理：核心材质和面层材质分别设置
    /// - 楼层关联：自动关联楼层并计算标高
    /// - 截面编辑：支持复杂截面的创建和编辑
    /// - 统计数据：自动计算长度、面积、体积等
    /// 
    /// 应用场景：
    /// - 建筑结构设计中的柱子建模
    /// - 建筑平面图和剖面图的绘制
    /// - 结构计算和工程量统计
    /// - 3D建筑模型的创建和渲染
    /// </summary>
    [Serializable]                           // 可序列化标识，支持文件保存和加载
    [DbElement("建筑柱", MajorType.Arc)]      // 图元类型标识，在软件中显示为"建筑柱"，归类为建筑类型
    public class ArcColumn : DbElement, IInstanceProperty, IDbMaterial, IDbArcSection
    {
        // 接口说明：
        // DbElement: 基础图元类，提供基本的几何操作、显示功能、事务管理等
        // IInstanceProperty: 实例属性接口，支持属性面板的动态显示控制和条件可见性
        // IDbMaterial: 材料接口，支持材料管理、材质设置、材料库集成等
        // IDbArcSection: 截面接口，支持截面类型编辑、截面管理、复杂截面定义等

        #region 属性参数定义 - 建筑柱的所有属性参数和控制逻辑
        /// <summary>
        /// 截面编辑功能属性
        /// 
        /// 功能说明：
        /// - 提供截面类型的编辑和管理功能
        /// - 支持复杂截面的创建和修改
        /// - 集成截面管理系统，支持截面库操作
        /// - 使用自定义编辑器，提供专业的截面编辑界面
        /// 
        /// 技术实现：
        /// - 使用TypeEditEvent委托类型，支持事件驱动的编辑
        /// - 通过TypeEditBtn编辑器提供按钮式的用户界面
        /// - 调用专门的截面编辑命令进行复杂操作
        /// 
        /// 用户交互：
        /// - 点击属性面板中的"截面编辑"按钮
        /// - 打开截面编辑对话框或页面
        /// - 支持截面的可视化编辑和参数设置
        /// </summary>
        [Category("类型"), DisplayName("截面编辑"), Description("编辑柱子的截面类型和参数"), ReadOnly(false)]
        [Editor(typeof(TypeEditBtn), typeof(TypeEditBtn)), PropertyOrder(0)]
        public TypeEditEvent TypeEdit
        {
            set
            {
                // 设置器暂时为空，主要功能在获取器中实现
            }
            get
            {
                // 返回一个委托，当用户点击编辑按钮时执行
                return () =>
                {
                    // 执行截面编辑命令
                    // 这将打开截面管理界面，允许用户编辑复杂截面
                    EBDB.Instance.CmdRun("ArchDe_EB.SecTypeEditCmd");
                };
            }
        }

        /// <summary>
        /// 视图
        /// </summary>
        [Category("几何约束"), DisplayName("楼层"), Description("所在楼层"), PropertyOrder(0), ReadOnly(false),
         Editor(typeof(ViewEditorNew), typeof(ViewEditorNew))]
        public View View
        {
            get => _view;
            set
            {
                if (value == _view) return;
                _view.Delete(this);
                value.AddElement(this);
                //UpdateArcColumn();
                ActCutCal2D3D();
                EBDB.Instance.UIReDrawAll();
            }
        }

        ///// <summary>
        ///// 定位楼层只读
        ///// </summary>
        ////[Category("几何约束"), ReadOnly(true), DisplayName("定位楼层"), Description("所在楼层名称"), PropertyOrder(0)]
        ////public string LevelName
        ////{
        ////    get => _view.ViewName;
        ////}

        /// <summary>
        /// 柱子的顶部标高，含顶部偏移
        /// </summary>
        public double _topLevel;
        /// <summary>
        /// 柱子的顶部标高，含顶部偏移
        /// </summary>
        [Category("几何约束"), DisplayName("顶部标高"), Description("顶部标高不可修改"), PropertyOrder(1)]
        public double TopLevel
        {
            get => _topLevel;
            set
            {
                if (_topLevel == value) { return; }
                TransManager.Instance().Push(a => TopLevel = a, _topLevel);
                _topLevel = value;
                _topOffset = value - _view.TopLevel;
                if (!AutoActivate) return;
                ActCutCal2D3D();
            }
        }

        /// <summary>
        /// 柱子的底部标高，含柱子的底部偏移
        /// </summary>
        public string _bottomLevel;
        /// <summary>
        /// 柱子的底部标高，含柱子的底部偏移
        /// </summary>
        [Category("几何约束"), DisplayName("底部标高"), Description("底部标高不可修改"), PropertyOrder(2)]
        public string BottomLevel
        {
            get => _bottomLevel;
            set
            {
                if (_bottomLevel == value) { return; }
                TransManager.Instance().Push(a => BottomLevel = a, _bottomLevel);
                _bottomLevel = value;
                double va = double.Parse(value);
                _bottomOffset = va - _view.BotLevel;
                if (!AutoActivate) return;
                ActCutCal2D3D();
            }
        }

        /// <summary>
        /// 柱子顶部偏移
        /// </summary>
        public double _topOffset;
        /// <summary>
        /// 顶部偏移
        /// </summary>
        [Category("几何约束"), DisplayName("顶部偏移"), Description("顶部偏移"), PropertyOrder(3)]
        public double TopOffset
        {
            get => _topOffset;
            set
            {
                if (_topOffset == value) { return; }
                TransManager.Instance().Push(a => TopOffset = a, _topOffset);
                _topOffset = value;
                _topLevel = _view.TopLevel - _topOffset;
                if (!AutoActivate) return;
                ActCutCal2D3D();
            }
        }

        /// <summary>
        /// 底部偏移
        /// </summary>
        public double _bottomOffset;
        /// <summary>
        ///         /// <summary>
        /// 底部偏移
        /// </summary>
        /// </summary>
        [Category("几何约束"), DisplayName("底部偏移"), Description("底部偏移"), PropertyOrder(4)]
        public double BottomOffset
        {
            get => _bottomOffset;
            set
            {
                if (_bottomOffset == value) { return; }
                TransManager.Instance().Push(a => BottomOffset = a, _bottomOffset);
                _bottomOffset = value;
                if (!AutoActivate) return;
                ActCutCal2D3D();
            }
        }

        /// <summary>
        /// 截面旋转角度
        /// </summary>
        public double _hoAngle;
        /// <summary>
        /// 截面旋转角度
        /// </summary>
        [Category("几何约束"), DisplayName("截面旋转"), Description("截面旋转角度"), PropertyOrder(5)]
        public double HoAngle
        {
            get => _hoAngle;
            set
            {
                if (_hoAngle == value) { return; }
                TransManager.Instance().Push(a => HoAngle = a, _hoAngle);
                _hoAngle = value;
                if (!AutoActivate) return;
                ActCutCal2D3D();
            }
        }

        /// <summary>
        /// 矩形柱的横向宽度
        /// </summary>
        public double _transverseWith;
        /// <summary>
        /// 矩形柱的横向宽度
        /// </summary>
        [Category("几何约束"), DisplayName("横向宽度"), Description("横向宽度"), PropertyOrder(6)]
        public double TransverseWith
        {
            get => _transverseWith;
            set
            {
                if (_transverseWith == value) { return; }
                TransManager.Instance().Push(a => TransverseWith = a, _transverseWith);
                _transverseWith = value;
                if (!AutoActivate) return;
                ActCutCal2D3D();
            }
        }

        /// <summary>
        /// 矩形柱的纵向宽度
        /// </summary>
        public double _verticalwidth;
        /// <summary>
        /// 矩形柱的纵向宽度
        /// </summary>
        [Category("几何约束"), DisplayName("纵向宽度"), Description("纵向宽度"), PropertyOrder(7)]
        public double Verticalwidth
        {
            get => _verticalwidth;
            set
            {
                if (_verticalwidth == value) { return; }
                TransManager.Instance().Push(a => Verticalwidth = a, _verticalwidth);
                _verticalwidth = value;
                if (!AutoActivate) return;
                ActCutCal2D3D();
            }
        }

        /// <summary>
        /// 圆柱半径
        /// </summary>
        public double _radius;
        /// <summary>
        /// 圆柱半径
        /// </summary>
        [Category("几何约束"), DisplayName("半径"), Description("半径"), PropertyOrder(8)]
        public double Radius
        {
            get => _radius;
            set
            {
                if (_radius == value) { return; }
                TransManager.Instance().Push(a => Radius = a, _radius);
                _radius = value;
                _doubleradius = value * 2;
                if (!AutoActivate) return;
                ActCutCal2D3D();
            }
        }

        /// <summary>
        /// 圆柱直径
        /// </summary>
        public double _doubleradius;
        /// <summary>
        /// 圆柱直径
        /// </summary>
        [Category("几何约束"), DisplayName("直径"), Description("直径"), PropertyOrder(9)]
        public double Doubleradius
        {
            get => _doubleradius;
            set
            {
                if (_doubleradius == value) { return; }
                TransManager.Instance().Push(a => Doubleradius = a, _doubleradius);
                _doubleradius = value;
                _radius = value / 2;
                if (!AutoActivate) return;
                ActCutCal2D3D();
            }
        }

        /// <summary>
        /// 柱体样式,是否为斜柱(第二阶段实现)
        /// </summary>
        public bool _ifVer = false;
        /// <summary>
        /// 柱体样式,是否为斜柱(第二阶段实现)
        /// </summary>
        [Category("几何约束"), DisplayName("是否斜柱"), Description("是否为斜柱"), PropertyOrder(10), Browsable(true)]
        public bool IfVer
        {
            get => _ifVer;
            set
            {
                if (_ifVer == value) { return; }
                TransManager.Instance().Push(a => IfVer = a, _ifVer);
                _ifVer = value;
                if (!AutoActivate) return;
                ActAndCal2D();
                CalSolid();
            }
        }

        /// <summary>
        /// 柱的倾斜角度
        /// </summary>
        public double _veAngle = 60;
        /// <summary>
        /// 柱的倾斜角度
        /// </summary>
        [Category("几何约束"), DisplayName("倾斜角度"), Description("柱在垂直空间上的倾斜角度"), PropertyOrder(11), Browsable(true)]
        public double VeAngle
        {
            get => _veAngle;
            set
            {
                if (_veAngle == value) { return; }
                if (value > 90)
                {
                    value = 90;
                }
                if (value < 0)
                {
                    value = 0;
                }
                TransManager.Instance().Push(a => VeAngle = a, _veAngle);
                _veAngle = value;
                if (!AutoActivate) return;
                ActCutCal2D3D();
            }
        }

        /// <summary>
        /// 是否显示面层
        /// </summary>
        private bool _isshowsurface = false;
        /// <summary>
        /// 是否显示面层
        /// </summary>
        [Category("面层设置"), DisplayName("显示面层"), Description("在1:100及以上比例显示面层"), PropertyOrder(12)]
        public bool Isshowsurface
        {
            get
            {
                return _isshowsurface;
            }
            set
            {
                if (_isshowsurface == value) return;
                TransManager.Instance().Push(a => Isshowsurface = a, _isshowsurface);
                _isshowsurface = value;
                if (!AutoActivate) return;
                ActCutCal2D3D();
            }
        }

        /// <summary>
        /// 面层厚度，默认为20mm
        /// </summary>
        public double _surfthick = 20;
        /// <summary>
        /// 面层厚度，默认为20mm
        /// </summary>
        [Category("面层设置"), DisplayName("面层厚度"), Description("放置定位点平面上左偏移(以中心点为基准)"), PropertyOrder(13)]
        public double Surfthick
        {
            get => _surfthick;
            set
            {
                if (_surfthick == value) { return; }
                TransManager.Instance().Push(a => Surfthick = a, _surfthick);
                _surfthick = value;
                if (!AutoActivate) return;
                ActCutCal2D3D();
            }
        }

        /// <summary>
        /// 面层高度，默认为柱高度，后续会与房间吊顶高度结合
        /// </summary>
        public double _surfheight = 2000;
        /// <summary>
        /// 面层高度，默认为柱高度，后续会与房间吊顶高度结合
        /// </summary>
        [Category("面层设置"), DisplayName("面层高度"), Description("放置定位点平面上左偏移(以中心点为基准)"), PropertyOrder(14)]
        public double Surfheight
        {
            get => _surfheight;
            set
            {
                if (_surfheight == value) { return; }
                TransManager.Instance().Push(a => Surfheight = a, _surfheight);
                _surfheight = value;
                if (!AutoActivate) return;
                ActCutCal2D3D();
            }
        }

        /// <summary>
        /// 柱子核心部分材质
        /// </summary>
        private DbMaterial _coreMateria = new DbMaterial("钢筋混凝土");
        /// <summary>
        /// 柱子核心部分材质
        /// </summary>
        [Category("材料构造"), DisplayName("核心材质"), Description("柱子核心部分材质"), ReadOnly(false), PropertyOrder(15)]
        [Editor(typeof(Btn_MatSelect), typeof(Btn_MatSelect))] //添加此行代码可在属性栏中调出材质选择窗口
        public DbMaterial CoreMateria
        {
            get { return _coreMateria; }
            set
            {
                if (_coreMateria == value) return;
                TransManager.Instance().Push(a => CoreMateria = a, _coreMateria);
                _coreMateria = value;
                if (!AutoActivate) return;
                ActCutCal2D3D();
            }
        }

        /// <summary>
        /// 柱子面层材质
        /// </summary>
        private DbMaterial _surfaceMateria = new DbMaterial("混凝土");
        /// <summary>
        /// 柱子面层材质
        /// </summary>
        [Category("材料构造"), DisplayName("面层材质"), Description("柱子面层材质"), ReadOnly(false), PropertyOrder(16)]
        [Editor(typeof(Btn_MatSelect), typeof(Btn_MatSelect))] //添加此行代码可在属性栏中调出材质选择窗口
        public DbMaterial SurfaceMateria
        {
            get { return _surfaceMateria; }
            set
            {
                if (_surfaceMateria == value) return;
                TransManager.Instance().Push(a => SurfaceMateria = a, _surfaceMateria);
                _surfaceMateria = value;
                if (!AutoActivate) return;
                ActCutCal2D3D();

            }
        }


        /// 矩形柱偏心参数
        /// <summary>
        /// 放置定位点平面上左偏移(以中心点为基准)
        /// </summary>
        public double _leftRec;
        /// <summary>
        /// 放置定位点平面上左偏移(以中心点为基准)
        /// </summary>
        [Category("几何约束"), DisplayName("左偏移"), Description("放置定位点平面上左偏移(以中心点为基准)"), Browsable(false)]
        public double LeftRec
        {
            get => _leftRec;
            set
            {
                if (_leftRec == value) { return; }
                TransManager.Instance().Push(a => LeftRec = a, _leftRec);
                _leftRec = value;
                if (!AutoActivate) return;
                ActAndCal2D();
            }
        }

        /// <summary>
        /// 放置定位点平面上右偏移(以中心点为基准)
        /// </summary>
        public double _rightRec;
        /// <summary>
        /// 放置定位点平面上右偏移(以中心点为基准)
        /// </summary>
        [Category("几何约束"), DisplayName("右偏移"), Description("放置定位点平面上右偏移(以中心点为基准)"), Browsable(false)]
        public double RightRec
        {
            get => _rightRec;
            set
            {
                if (_rightRec == value) { return; }
                TransManager.Instance().Push(a => RightRec = a, _rightRec);
                _rightRec = value;
                if (!AutoActivate) return;
                ActAndCal2D();
            }
        }

        /// <summary>
        /// 放置定位点平面上向上偏移(以中心点为基准)
        /// </summary>
        public double _topRec;
        /// <summary>
        /// 放置定位点平面上向上偏移(以中心点为基准)
        /// </summary>
        [Category("几何约束"), DisplayName("上偏移"), Description("放置定位点平面上向上偏移(以中心点为基准)"), Browsable(false)]

        public double TopRec
        {
            get => _topRec;
            set
            {
                if (_topRec == value) { return; }
                TransManager.Instance().Push(a => TopRec = a, _topRec);
                _topRec = value;
                if (!AutoActivate) return;
                ActAndCal2D();
            }
        }

        /// <summary>
        /// 放置定位点平面向下偏移(以中心点为基准)
        /// </summary>
        public double _bottomRec;
        /// <summary>
        /// 放置定位点平面向下偏移(以中心点为基准)
        /// </summary>
        [Category("几何约束"), DisplayName("下偏移"), Description("放置定位点平面向下偏移(以中心点为基准)"), Browsable(false)]

        public double BottomRec
        {
            get => _bottomRec;
            set
            {
                if (_bottomRec == value) { return; }
                TransManager.Instance().Push(a => BottomRec = a, _bottomRec);
                _bottomRec = value;
                if (!AutoActivate) return;
                ActAndCal2D();
            }
        }

        /// <summary>
        /// 放置定位点平面上左偏移(以中心点为基准)
        /// </summary>
        public double _leftCir;
        /// <summary>
        /// 放置定位点平面上左偏移(以中心点为基准)
        /// </summary>
        [Category("几何约束"), DisplayName("左偏移"), Description("放置定位点平面上左偏移(以中心点为基准)"), PropertyOrder(1), Browsable(false)]

        public double LeftCir
        {
            get => _leftCir;
            set
            {
                if (_leftCir == value) { return; }
                TransManager.Instance().Push(a => LeftCir = a, _leftCir);
                _leftCir = value;
                if (!AutoActivate) return;
                ActAndCal2D();
            }
        }

        /// <summary>
        /// 放置定位点平面上右偏移(以中心点为基准)
        /// </summary>
        public double _rightCir;
        /// <summary>
        /// 放置定位点平面上右偏移(以中心点为基准)
        /// </summary>
        [Category("几何约束"), DisplayName("右偏移"), Description("放置定位点平面上右偏移(以中心点为基准)"), Browsable(false)]
        public double RightCir
        {
            get => _rightCir;
            set
            {
                if (_rightCir == value) { return; }
                TransManager.Instance().Push(a => RightCir = a, _rightCir);
                _rightCir = value;
                if (!AutoActivate) return;
                ActAndCal2D();
            }
        }

        /// <summary>
        /// 放置定位点平面上向上偏移(以中心点为基准)
        /// </summary>
        public double _topCir;
        /// <summary>
        /// 放置定位点平面上向上偏移(以中心点为基准)
        /// </summary>
        [Category("几何约束"), DisplayName("上偏移"), Description("放置定位点平面上向上偏移(以中心点为基准)"), Browsable(false)]

        public double TopCir
        {
            get => _topCir;
            set
            {
                if (_topCir == value) { return; }
                TransManager.Instance().Push(a => TopCir = a, _topCir);
                _topCir = value;
                if (!AutoActivate) return;
                ActAndCal2D();
            }
        }

        ///圆形柱偏心参数
        /// <summary>
        /// 放置定位点平面向下偏移(以中心点为基准)
        /// </summary>
        public double _bottomCir;
        /// <summary>
        /// 放置定位点平面向下偏移(以中心点为基准)
        /// </summary>
        [Category("几何约束"), DisplayName("下偏移"), Description("放置定位点平面向下偏移(以中心点为基准)"), Browsable(false)]

        public double BottomCir
        {
            get => _bottomCir;
            set
            {
                if (_bottomCir == value) { return; }
                TransManager.Instance().Push(a => BottomCir = a, _bottomCir);
                _bottomCir = value;
                if (!AutoActivate) return;
                ActAndCal2D();
            }
        }

        /// <summary>
        /// 定义截面样式枚举
        /// </summary>
        public enum TopTiltAng
        {
            /// <summary>
            /// 水平
            /// </summary>
            [Description("水平")] 水平,
            /// <summary>
            /// 垂直
            /// </summary>
            [Description("垂直")] 垂直,
            /// <summary>
            /// 自定义
            /// </summary>
            [Description("自定义")] 自定义,

        }

        /// <summary>
        /// 顶截面样式，水平、垂直、自定义
        /// </summary>
        public TopTiltAng _topStyle;
        /// <summary>
        /// 顶截面样式，水平、垂直、自定义
        /// </summary>
        [Category("几何约束"), DisplayName("顶截面样式"), Description("顶部截面样式"), PropertyOrder(14), Browsable(false)]
        public TopTiltAng TopStyle
        {
            get => _topStyle;
            set
            {
                if (_topStyle == value) { return; }
                TransManager.Instance().Push(a => TopStyle = a, _topStyle);
                _topStyle = value;
                if (!AutoActivate) return;
                ActAndCal2D();
            }
        }

        /// <summary>
        /// 顶部截面倾斜角度
        /// </summary>
        public double _topAngle;
        /// <summary>
        /// 顶部截面倾斜角度
        /// </summary>
        [Category("几何约束"), DisplayName("倾斜角度"), Description("斜柱顶部的倾斜角度"), PropertyOrder(15), Browsable(false)]
        public double TopAngle
        {
            get => _topAngle;
            set
            {
                if (_topAngle == value) { return; }
                TransManager.Instance().Push(a => TopAngle = a, _topAngle);
                _topAngle = value;
                if (!AutoActivate) return;
                ActAndCal2D();
            }
        }

        /// <summary>
        /// 底部截面样式
        /// </summary>
        public TopTiltAng _bottomStyle;
        /// <summary>
        /// 底部截面样式
        /// </summary>
        [Category("几何约束"), DisplayName("底截面样式"), Description("斜柱底截面样式"), PropertyOrder(16), Browsable(false)]
        public TopTiltAng BottomStyle
        {
            get => _bottomStyle;
            set
            {
                if (_bottomStyle == value) { return; }
                TransManager.Instance().Push(a => BottomStyle = a, _bottomStyle);
                _bottomStyle = value;
                if (!AutoActivate) return;
                ActAndCal2D();
            }
        }

        /// <summary>
        /// 底部截面倾斜角度
        /// </summary>
        public double _bottomAngle;
        /// <summary>
        /// 底部截面倾斜角度
        /// </summary>
        [Category("几何约束"), DisplayName("倾斜角度"), Description("斜柱底部的倾斜角度"), PropertyOrder(17), Browsable(false)]
        public double BottomAngle
        {
            get => _bottomAngle;
            set
            {
                if (_bottomAngle == value) { return; }
                TransManager.Instance().Push(a => BottomAngle = a, _bottomAngle);
                _bottomAngle = value;
                if (!AutoActivate) return;
                ActAndCal2D();
            }
        }

        /// <summary>
        /// 截面类型，先默认设置为矩形
        /// </summary>
        public string _sectiontype = "矩形";
        /// <summary>
        /// 截面类型，先默认设置为矩形
        /// </summary>
        [Category("几何约束"), DisplayName("截面类型"), Description("截面类型不可修改"), PropertyOrder(18), Browsable(false)]

        public string Sectiontype
        {
            get => _sectiontype;
        }

        /// <summary>
        /// 垂直的枚举
        /// </summary>
        public enum VerOrTilt
        {
            /// <summary>
            /// 垂直
            /// </summary>
            [Description("垂直")] 垂直,
            /// <summary>
            /// 倾斜
            /// </summary>
            [Description("倾斜")] 倾斜,
        }

        /// <summary>
        /// 是否是房间便捷
        /// </summary>
        private bool _ifboundary = false;
        /// <summary>
        /// 是否是房间便捷
        /// </summary>
        [Category("标识数据"), DisplayName("房间边界"), Description("是否作为房间边界"), PropertyOrder(20), Browsable(false)]
        public bool Ifboundary
        {
            get => _ifboundary;
            set
            {
                if (_ifboundary == value) return;
                _ifboundary = value;

            }
        }

        /// <summary>
        /// 柱的长度
        /// </summary>
        public double _length;
        /// <summary>
        /// 柱的长度
        /// </summary>
        [Category("统计数据"), DisplayName("长度"), Description("柱的长度"), PropertyOrder(21)]

        public double Length
        {
            get => _length;
        }

        /// <summary>
        /// 截面面积
        /// </summary>
        public string _sectionarea;
        /// <summary>
        /// 截面面积
        /// </summary>
        [Category("统计数据"), DisplayName("面积"), Description("柱的截面面积"), PropertyOrder(22)]
        public string Sectionarea
        {
            get => _sectionarea;
        }

        /// <summary>
        /// 截面类型
        /// </summary>
        public string _volume;
        /// <summary>
        /// 截面类型
        /// </summary>
        [Category("统计数据"), DisplayName("体积"), Description("柱的体积"), PropertyOrder(23)]
        public string Volume
        {
            get => _volume;
        }

        /// <summary>
        /// 记录矩形柱子主体边界的线
        /// </summary>
        List<DbLine> recordInLines = new List<DbLine>();

        /// <summary>
        /// 记录矩形柱子面层的线
        /// </summary>
        List<DbLine> recordOutLines = new List<DbLine>();

        /// <summary>
        /// 记录圆形柱子主体边界的圆
        /// </summary>
        List<DbCircle> recordInCircles = new List<DbCircle>();

        /// <summary>
        /// 记录矩形柱子面层的圆
        /// </summary>
        List<DbCircle> recordOutCircles = new List<DbCircle>();

        /// <summary>
        /// 记录编辑过后外部轮廓数据
        /// </summary>
        public List<DbLoop> outloops = new List<DbLoop>();

        /// <summary>
        /// 记录面层面域
        /// </summary>
        public DbRegion outregion = new DbRegion();

        /// <summary>
        /// 记录柱子主体的面域
        /// </summary>
        public DbRegion arccolumnregion = new DbRegion();

        /// <summary>
        /// 记录面层与柱体之间裁剪过后的面域
        /// </summary>
        public DbRegion cutregion = new DbRegion();

        /// <summary>
        /// 柱的样式,Rectangle、Circle、Irregular
        /// </summary>
        public CoTy _columntype;

        /// <summary>
        /// 柱的样式,Rectangle、Circle、Irregular
        /// </summary>
        public CoTy Columntype
        {
            get => _columntype;
            set
            {
                _columntype = value;
                if (_columntype == CoTy.Rectangle)
                {
                    _sectiontype = "矩形";
                }

                else if (_columntype == CoTy.Circle)
                {
                    _sectiontype = "圆形";
                }
            }
        }


        /// <summary>
        /// 全局记录编辑过后的所有线，方便后续切换比例时显示，包括未被编辑的线和圆
        /// </summary>
        private List<DbLine> LinesRecorder = new List<DbLine>();

        /// <summary>
        /// 全局记录编辑过后的所有圆，方便后续切换比例时显示，包括未被编辑的线和圆
        /// </summary>
        private List<DbCircle> CircleRecorder = new List<DbCircle>();
        /// <summary>
        /// 面层材料
        /// </summary>
        public TexProperty _surftex = new TexProperty(EB3D.View3D.AddTexturePath("./Mat/涂料.png", true), 1000);

        /// <summary>
        /// 柱子中心控制点
        /// </summary>
        public DbPt _ptcenter = new DbPt();

        /// <summary>
        /// 柱子插入点
        /// </summary>
        public DbPt _insertpt = new DbPt();

        /// <summary>
        /// 记录柱面层的线段
        /// </summary>
        List<DbLine> SurfLines = new List<DbLine>();

        /// <summary>
        /// 记录柱面层的圆
        /// </summary>
        List<DbCircle> SurCircle = new List<DbCircle>();

        /// <summary>
        /// 记录面层到柱主体表面的面域
        /// </summary>
        public DbRegion surfaceregion = new DbRegion();

        /// <summary>
        /// 是否被双击编辑过，默认为false
        /// </summary>
        private bool _ifedited = false;

        /// <summary>
        /// 跳过传递到预览窗口函数的标记
        /// </summary>
        private bool _skipCalculatePassLinesInEleCopy = true;

        /// <summary>
        /// 跳过计算中心控制点的标记
        /// </summary>
        private bool _skipCalcuPtcenter = true;

        /// <summary>
        /// 是否镜像
        /// </summary>
        private bool _ifmirror = false;

        /// <summary>
        /// 记录传递到预览面板的线
        /// </summary>
        public List<DbLine> LinesPassAround = new List<DbLine>();
        /// <summary>
        /// 记录传递到预览面板的线
        /// </summary>
        public List<DbLine> LinesPassInsert = new List<DbLine>();

        /// <summary>
        /// 截面类型，不保存
        /// </summary>
        public ArcSecType SecType = new ArcSecType();


        /// <summary>
        /// 异形柱的旋转点【不保存】
        /// </summary>
        private DbPt IrregularColumnRotatePt = new DbPt();

        /// <summary>
        /// 异形柱是否旋转
        /// </summary>
        private bool ifRptate = false;

        /// <summary>
        /// 镜像的线
        /// </summary>
        private DbLine mirrorLine = new DbLine();

        /// <summary>
        /// 截面中的loop中的线，不保存
        /// </summary>
        private List<DbLine> secLoopLines = new List<DbLine>();

        private int verLinecount = 0;

        private string secTypeID = string.Empty;

        /// <summary>
        /// 记录控制点0的位置
        /// </summary>
        private DbPt conptRecoder = new DbPt();

        /// <summary>
        /// 异形柱双击编辑的状态值，0编辑异形柱截面，1编辑面层【不保存】
        /// </summary>
        private int _select = 0;

        #endregion
        #region 执行代码
        /// <summary>
        /// 构造函数
        /// </summary>
        public ArcColumn()
        {

        }
        /// <summary>
        /// 基本构造函数 - 根据插入点和柱子类型创建柱子
        /// </summary>
        /// <param name="insertpt">插入点坐标 - 用户在图纸上点击的位置</param>
        /// <param name="columntype">柱子类型 - 矩形(Rectangle)、圆形(Circle)或异形(Irregular)</param>
        public ArcColumn(DbPt insertpt, CoTy columntype)
        {
            // 初始化3D相关属性
            _if3D = true;  // 标记为3D对象
            _texP = new TexProperty(EB3D.View3D.AddTexturePath("./Mat/涂料.png", true), 1000);    // 主体纹理
            _surftex = new TexProperty(EB3D.View3D.AddTexturePath("./Mat/涂料.png", true), 1000); // 面层纹理

            _columntype = columntype;  // 保存柱子类型
            
            //矩形柱初始化
            if (_columntype == CoTy.Rectangle)
            {
                _insertpt = insertpt;  // 保存插入点
                ConPts.Clear();        // 清空控制点列表
                
                // 为矩形柱添加9个控制点
                ConPts.Add(new DbPt());//0 - 中心控制点
                ConPts.Add(new DbPt());//1 - 上边中点
                ConPts.Add(new DbPt());//2 - 右边中点
                ConPts.Add(new DbPt());//3 - 下边中点
                ConPts.Add(new DbPt());//4 - 左边中点
                ConPts.Add(new DbPt());//5 - 左上角点
                ConPts.Add(new DbPt());//6 - 右上角点
                ConPts.Add(new DbPt());//7 - 右下角点
                ConPts.Add(new DbPt());//8 - 左下角点
                
                if (_ifVer)  // 如果是斜柱，需要额外的控制点
                {
                    ConPts.Add(new DbPt());//9 - 顶部截面的位置控制点
                }
                _sectiontype = "矩形";
            }

            //圆形柱初始化
            else if (_columntype == CoTy.Circle)
            {
                _insertpt = insertpt;  // 保存插入点
                ConPts.Clear();        // 清空控制点列表
                
                // 为圆形柱添加5个控制点
                ConPts.Add(new DbPt());//0 - 圆心控制点
                ConPts.Add(new DbPt());//1 - 上边界点
                ConPts.Add(new DbPt());//2 - 右边界点
                ConPts.Add(new DbPt());//3 - 下边界点
                ConPts.Add(new DbPt());//4 - 左边界点
                
                _sectiontype = "圆形";
                if (_ifVer)  // 如果是斜柱，需要额外的控制点
                {
                    ConPts.Add(new DbPt());//5 - 顶部截面的位置控制点
                }
            }
            
            CalcuPtcenter();  // 计算中心点位置
        }

        /// <summary>
        /// 矩形柱专用构造函数 - 创建具有完整参数的矩形柱
        /// </summary>
        /// <param name="insertpt">插入点坐标 - 用户在图纸上点击的位置</param>
        /// <param name="transverseWith">横向宽度 - 矩形柱在X方向的尺寸</param>
        /// <param name="verticalwidth">纵向宽度 - 矩形柱在Y方向的尺寸</param>
        /// <param name="topoffset">顶部偏移 - 相对于楼层顶标高的偏移量，默认0</param>
        /// <param name="bottomoffset">底部偏移 - 相对于楼层底标高的偏移量，默认-50</param>
        /// <param name="leftRec">左偏移 - 插入点相对于柱中心的左偏移，默认0</param>
        /// <param name="rightRec">右偏移 - 插入点相对于柱中心的右偏移，默认0</param>
        /// <param name="topRec">上偏移 - 插入点相对于柱中心的上偏移，默认0</param>
        /// <param name="bottonRec">下偏移 - 插入点相对于柱中心的下偏移，默认0</param>
        /// <param name="hoAngle">旋转角度 - 柱子截面的旋转角度，默认0度</param>
        public ArcColumn(DbPt insertpt, double transverseWith, double verticalwidth, double topoffset = 0, double bottomoffset = -50, double leftRec = 0, double rightRec = 0, double topRec = 0, double bottonRec = 0, double hoAngle = 0)
        {
            // 初始化3D渲染相关属性
            _if3D = true;  // 标记这是一个3D对象，需要进行3D渲染
            
            // 创建主体纹理属性：加载涂料纹理图片，尺寸为1000单位
            _texP = new TexProperty(EB3D.View3D.AddTexturePath("./Mat/涂料.png", true), 1000);
            
            // 创建面层纹理属性：同样使用涂料纹理，用于面层装饰
            _surftex = new TexProperty(EB3D.View3D.AddTexturePath("./Mat/涂料.png", true), 1000);

            // 设置柱子类型为矩形柱
            _columntype = CoTy.Rectangle;
            
            // 保存用户点击的插入点坐标
            _insertpt = insertpt;
            // 保存矩形柱的几何参数
            _verticalwidth = verticalwidth;      // 保存纵向宽度（Y方向尺寸）
            _transverseWith = transverseWith;    // 保存横向宽度（X方向尺寸）
            
            // 保存标高偏移参数
            _topOffset = topoffset;              // 保存顶部偏移量
            _bottomOffset = bottomoffset;        // 保存底部偏移量
            
            // 保存插入点相对于中心的偏移参数（用于定位计算）
            _rightRec = rightRec;                // 保存右偏移量
            _leftRec = leftRec;                  // 保存左偏移量
            _topRec = topRec;                    // 保存上偏移量
            _bottomRec = bottonRec;              // 保存下偏移量
            
            // 保存截面旋转角度
            _hoAngle = hoAngle;                  // 保存旋转角度（单位：度）
            
            // 初始化矩形柱的控制点数组（9个控制点）
            ConPts.Add(new DbPt());//0 - 中心控制点，用于定位和旋转
            ConPts.Add(new DbPt());//1 - 上边中点，用于上边编辑
            ConPts.Add(new DbPt());//2 - 右边中点，用于右边编辑
            ConPts.Add(new DbPt());//3 - 下边中点，用于下边编辑
            ConPts.Add(new DbPt());//4 - 左边中点，用于左边编辑
            ConPts.Add(new DbPt());//5 - 左上角点，用于绘制矩形
            ConPts.Add(new DbPt());//6 - 右上角点，用于绘制矩形
            ConPts.Add(new DbPt());//7 - 右下角点，用于绘制矩形
            ConPts.Add(new DbPt());//8 - 左下角点，用于绘制矩形
            
            // 如果是斜柱，需要额外的控制点
            if (_ifVer)
            {
                ConPts.Add(new DbPt());//9 - 顶部截面的位置控制点，用于斜柱顶部定位
            }
            
            // 计算柱子的几何中心点位置
            CalcuPtcenter();
            
            // 初始化面层高度为楼层总高度
            _surfheight = _view.TopLevel - _view.BotLevel;
            
            // 初始化顶部和底部标高
            _topLevel = _view.TopLevel;                    // 设置顶部标高为楼层顶标高
            _bottomLevel = _view.BotLevel.ToString();      // 设置底部标高为楼层底标高（转换为字符串）
        }

        /// <summary>
        /// 圆形柱专用构造函数 - 创建具有完整参数的圆形柱
        /// </summary>
        /// <param name="insertpt">插入点坐标 - 用户在图纸上点击的位置</param>
        /// <param name="radius">圆形柱半径 - 决定柱子的大小</param>
        /// <param name="topoffset">顶部偏移 - 相对于楼层顶标高的偏移量，默认0</param>
        /// <param name="bottomoffset">底部偏移 - 相对于楼层底标高的偏移量，默认-50</param>
        /// <param name="leftCir">左偏移 - 插入点相对于圆心的左偏移，默认0</param>
        /// <param name="rightCir">右偏移 - 插入点相对于圆心的右偏移，默认0</param>
        /// <param name="topCir">上偏移 - 插入点相对于圆心的上偏移，默认0</param>
        /// <param name="bottonCir">下偏移 - 插入点相对于圆心的下偏移，默认0</param>
        /// <param name="hoAngle">旋转角度 - 柱子截面的旋转角度，默认0度</param>
        public ArcColumn(DbPt insertpt, double radius, double topoffset = 0, double bottomoffset = -50, double leftCir = 0, double rightCir = 0, double topCir = 0, double bottonCir = 0, double hoAngle = 0)
        {
            // 初始化3D渲染相关属性
            _if3D = true;  // 标记这是一个3D对象，需要进行3D渲染
            
            // 创建主体纹理属性：加载涂料纹理图片，尺寸为1000单位
            _texP = new TexProperty(EB3D.View3D.AddTexturePath("./Mat/涂料.png", true), 1000);
            
            // 创建面层纹理属性：同样使用涂料纹理，用于面层装饰
            _surftex = new TexProperty(EB3D.View3D.AddTexturePath("./Mat/涂料.png", true), 1000);
            
            // 设置柱子类型为圆形柱
            _columntype = CoTy.Circle;
            
            // 保存用户点击的插入点坐标
            _insertpt = insertpt;
            
            // 保存圆形柱的几何参数
            _radius = radius;                    // 保存半径值
            _doubleradius = 2 * _radius;         // 计算并保存直径值（直径=半径×2）
            
            // 保存标高偏移参数
            _topOffset = topoffset;              // 保存顶部偏移量
            _bottomOffset = bottomoffset;        // 保存底部偏移量
            
            // 保存插入点相对于圆心的偏移参数（用于定位计算）
            _rightCir = rightCir;                // 保存右偏移量
            _leftCir = leftCir;                  // 保存左偏移量
            _topCir = topCir;                    // 保存上偏移量
            _bottomCir = bottonCir;              // 保存下偏移量
            
            // 保存截面旋转角度
            _hoAngle = hoAngle;                  // 保存旋转角度（单位：度）
            
            // 初始化圆形柱的控制点数组（5个控制点）
            ConPts.Add(new DbPt());//0 - 圆心控制点，用于定位和旋转
            ConPts.Add(new DbPt());//1 - 上边界点，圆的最高点
            ConPts.Add(new DbPt());//2 - 右边界点，圆的最右点
            ConPts.Add(new DbPt());//3 - 下边界点，圆的最低点
            ConPts.Add(new DbPt());//4 - 左边界点，圆的最左点
            
            // 如果是斜柱，需要额外的控制点
            if (_ifVer)
            {
                ConPts.Add(new DbPt());//5 - 顶部截面的位置控制点，用于斜柱顶部定位
            }
            
            // 计算柱子的几何中心点位置
            CalcuPtcenter();
            
            // 初始化面层高度为楼层总高度
            _surfheight = _view.TopLevel - _view.BotLevel;
            
            // 初始化顶部和底部标高
            _topLevel = _view.TopLevel;                    // 设置顶部标高为楼层顶标高
            _bottomLevel = _view.BotLevel.ToString();      // 设置底部标高为楼层底标高（转换为字符串）
        }

        /// <summary>
        /// 异形柱专用构造函数 - 创建基于自定义截面类型的异形柱
        /// </summary>
        /// <param name="insertpt">插入点坐标 - 用户在图纸上点击的位置</param>
        /// <param name="secType">截面类型 - 定义异形柱的截面形状和尺寸</param>
        /// <param name="topoffset">顶部偏移 - 相对于楼层顶标高的偏移量，默认0</param>
        /// <param name="bottomoffset">底部偏移 - 相对于楼层底标高的偏移量，默认-50</param>
        /// <param name="hoAngle">旋转角度 - 柱子截面的旋转角度，默认0度</param>
        public ArcColumn(DbPt insertpt, ArcSecType secType, double topoffset = 0, double bottomoffset = -50, double hoAngle = 0)
        {
            // 初始化3D渲染相关属性
            _if3D = true;  // 标记这是一个3D对象，需要进行3D渲染
            
            // 保存异形柱的截面类型定义
            SecType = secType;  // 截面类型包含了异形柱的几何形状信息
            
            // 保存用户点击的插入点坐标
            _insertpt = insertpt;
            
            // 初始化异形柱的控制点数组，只添加插入点作为基准点
            ConPts.Add(insertpt);  // 0 - 插入点，作为异形柱的定位基准
            
            // 保存标高偏移参数
            _topOffset = topoffset;              // 保存顶部偏移量
            _bottomOffset = bottomoffset;        // 保存底部偏移量
            
            // 保存截面旋转角度
            _hoAngle = hoAngle;                  // 保存旋转角度（单位：度）
            
            // 如果是斜柱，需要额外的控制点
            if (_ifVer)
            {
                ConPts.Add(new DbPt());//1 - 顶部截面的位置控制点，用于斜柱顶部定位
            }
            
            // 保存截面类型的唯一标识符，用于后续的类型关联
            secTypeID = SecType.UniqueId;
            
            // 初始化顶部和底部标高
            _topLevel = _view.TopLevel;                                    // 设置顶部标高为楼层顶标高
            _bottomLevel = (_view.BotLevel + bottomoffset).ToString();     // 设置底部标高为楼层底标高加上偏移量
        }

        /// <summary>
        /// 设置截面
        /// </summary>
        /// <_param name="secType"></_param>
        /// <_param name="ifActive"></_param>
        public void SetEleType(ArcSecType secType, bool ifActive = false)
        {
            TransManager.Instance().Push(a => SetEleType(a, ifActive), secType);
            SecType = secType;
            SecType.UniqueId = secType.UniqueId;
            if (ifActive) { Activate(); GFunc.CutElementAndOthers(EBDB.Instance.ActivView2D.GView, this); }
        }

        /// <summary>
        /// 当图元使用了类型时，通过该方法建立图元与类型的关联
        /// </summary>
        /// <returns>关联成功时返回null;关联失败时返回在执行LinkElementType操作前已关联的类型(用于协同时反补被创建者删除的类型)</returns>
        public override DbElementType LinkElementType()
        {
            foreach (var item in EBDB.Instance.GProject.ElementTypes)
            {
                if (item is ArcSecType gtype)
                {
                    if (secTypeID == gtype.UniqueId)
                    {
                        SecType = gtype;
                        return null;
                    }
                }
            }
            if (SecType == null)//避免模型崩溃
            {
                DbLine line1 = new DbLine(new DbPt(-200, -200), new DbPt(-200, 200));
                DbLine line2 = new DbLine(new DbPt(-200, 200), new DbPt(200, 200));
                DbLine line3 = new DbLine(new DbPt(200, 200), new DbPt(200, -200));
                DbLine line4 = new DbLine(new DbPt(200, -200), new DbPt(-200, -200));
                List<DbLine> defaultLines = new List<DbLine>() { line1, line2, line3, line4 };
                List<DbLoop> defaultLoops = GMath.GetCwLoopsOfPls(defaultLines);
                SecType = new ArcSecType("default-替代", ArcSecTypeCategoryEnum.ColumnSec, defaultLoops[0]);
                EBDB.Instance.UICmdLine("异形柱截面类型加载出错", null, null);
                return SecType;
            }
            else { return SecType; }
        }

        /// <summary>
        /// 设置窗类型
        /// </summary>
        public void SetWindowType(ArcSecType sectype)
        {
            TransManager.Instance().Push(SetWindowType, SecType);
            SecType = sectype;
            ActCutCal2D3D();
        }

        /// <summary>
        /// 异形柱截面
        /// </summary>
        /// <returns></returns>
        public HashSet<string> GetDbSectionID()
        {
            HashSet<string> set = new HashSet<string>();

            set.Add(SecType.UniqueId);
            return set;

        }

        /// <summary>
        /// 重写激活函数
        /// </summary>
        /// <summary>
        /// 激活建筑柱图元，计算并生成柱子的所有几何图形
        /// 这是图元显示的核心方法，负责根据当前参数重新计算柱子的几何形状
        /// </summary>
        public override void Activate()
        {
            // 清空所有图形集合，准备重新计算
            Lines.Clear();      // 清空线段集合
            Circles.Clear();    // 清空圆形集合
            Hatchs.Clear();     // 清空填充集合
            
            // 获取全局参数，判断是否显示建筑柱
            bool flag = ArchGlobalParams.GetGlobalParams().IfShowArcColumn;
            
            if (flag) // 如果需要显示建筑柱
            {
                // 重新计算控制点位置
                RecalCuConpts();
                // 验证控制点的有效性
                VerConpts();
                // 设置图层为"柱子-砼柱"
                LayerSet("柱子-砼柱");

                // 处理异形柱的特殊情况
                if (_columntype == CoTy.Irregular)
                {
                    // 如果需要跳过EleCopy中的PassLines计算
                    if (_skipCalculatePassLinesInEleCopy)
                    {
                        // 清空并重新获取截面环线
                        secLoopLines.Clear();
                        secLoopLines.AddRange(SecType.MatPanelInfos[0].Item1.EleCopy().Lines);
                        
                        // 将截面环线移动到控制点位置
                        foreach (var secLoopLine in secLoopLines)
                        {
                            secLoopLine.MoveSelf(ConPts[0].X, ConPts[0].Y);
                        }
                    }
                }

                // 处理未被编辑过的柱子
                if (!_ifedited) // 如果柱子没有被手动编辑过
                {
                    // 处理非镜像状态的柱子
                    if (!_ifmirror)
                    {
                        // 对于非异形柱，需要重新计算控制点
                        if (Columntype != CoTy.Irregular)
                        {
                            CalcuPts(); // 重新计算控制点位置
                        }
                        _ifmirror = false; // 重置镜像标志
                    }

                    // 计算柱子的基本几何形状
                    ArcColumnFill();    // 计算柱子的填充区域
                    CalcuColumnLines(); // 计算柱子的核心层线条
                }

                // 计算默认面层（仅对未编辑的柱子）
                if (!_ifedited) // 如果柱子没有被编辑过，则计算默认面层
                {
                    // 注意：在1:100比例下不显示，在1:50比例下显示，三维模式下也显示
                    CalcuDefaultSurfaceLines(); // 计算默认面层的两个面域
                }

                // 处理已编辑过的柱子
                if (_ifedited) // 如果柱子被编辑过
                {
                    // 注意：在1:50比例下展示编辑后的面层，在1:100比例下默认不展示（但可强制展示）
                    ArcColumnFill();    // 重新计算柱子填充
                    CalcuColumnLines(); // 重新计算柱子核心层
                    ShowSurf();         // 矫正并显示编辑后的面域
                }
                
                // 处理PassLines的计算
                if (_skipCalculatePassLinesInEleCopy)
                {
                    CalculatePassLines(); // 计算通过线
                    _skipCalculatePassLinesInEleCopy = false; // 重置跳过标志
                }
                
                // 计算矩形柱的核心层面域
                if (_columntype == CoTy.Rectangle)
                {
                    // 创建点集合，使用控制点5-8构成矩形
                    List<DbPt> pts = new List<DbPt>();
                    pts.Add(ConPts[5]); // 左上角
                    pts.Add(ConPts[6]); // 右上角
                    pts.Add(ConPts[7]); // 右下角
                    pts.Add(ConPts[8]); // 左下角
                    
                    // 根据这些点生成面域
                    arccolumnregion = GMath.GetRegionByPts(pts);
                }
                
                // 设置所有图形元素的图层
                LayerSet2(); // 将所有的线、圆、填充设置到对应的图层
                
                // 创建图元面积对象
                EleArea = new DbEleArea(this);
                
                // 设置跳过标志和保存中心点
                _skipCalculatePassLinesInEleCopy = true;
                _ptcenter = ConPts[0].EleCopy(); // 保存中心点的副本
            }
            else // 如果不需要显示建筑柱
            {
                ConPts.Clear(); // 清空控制点集合
            }
        }

        /// <summary>
        /// 隐藏柱子后再显示柱子需要计算的函数
        /// </summary>
        private void RecalCuConpts()
        {
            if (ConPts.Count == 0)
            {
                if (_columntype == CoTy.Rectangle)
                {
                    ConPts.Add(new DbPt());//0
                    ConPts.Add(new DbPt());//1
                    ConPts.Add(new DbPt());//2
                    ConPts.Add(new DbPt());//3
                    ConPts.Add(new DbPt());//4
                    ConPts.Add(new DbPt());//5
                    ConPts.Add(new DbPt());//6
                    ConPts.Add(new DbPt());//7
                    ConPts.Add(new DbPt());//8
                    if (_ifVer)
                    {
                        ConPts.Add(new DbPt());//9，顶部截面的位置控制点
                    }
                    CalcuPtcenter();
                }
                else if (_columntype == CoTy.Circle)
                {
                    ConPts.Add(new DbPt());//0
                    ConPts.Add(new DbPt());//1
                    ConPts.Add(new DbPt());//2
                    ConPts.Add(new DbPt());//3
                    ConPts.Add(new DbPt());//4
                    if (_ifVer)
                    {
                        ConPts.Add(new DbPt());//9，顶部截面的位置控制点
                    }
                    CalcuPtcenter();
                }
                else if (_columntype == CoTy.Irregular)
                {
                    ConPts.Add(_ptcenter);
                    if (_ifVer)
                    {
                        ConPts.Add(new DbPt());//9，顶部截面的位置控制点
                    }
                }
            }
        }

        /// <summary>
        /// 当为斜柱时，增加或矫正顶部控制点
        /// </summary>
        private void VerConpts()
        {
            //异形柱记录控制点
            List<DbPt> ConPtsRecorder = new List<DbPt>();
            if (_ifVer)
            {
                if (_columntype == CoTy.Rectangle)
                {
                    if (ConPts.Count == 9)
                    {
                        ConPts.Add(new DbPt());
                    }
                }
                else if (_columntype == CoTy.Circle)
                {
                    if (ConPts.Count == 5)
                    {
                        ConPts.Add(new DbPt());
                    }
                }
                else if (_columntype == CoTy.Irregular)
                {
                    if (ConPts.Count == 1)
                    {
                        ConPts.Add(new DbPt());
                    }
                    ConPtsRecorder.Add(ConPts[0]);
                    ConPtsRecorder.Add(ConPts[01]);
                    ConPts.Clear();
                    ConPts.AddRange(ConPtsRecorder);
                    List<DbLine> temnLines = new List<DbLine>();
                    temnLines.AddRange(SecType.MatPanelInfos[0].Item1.EleCopy().Lines);
                    List<DbPt> CorPts = new List<DbPt>();
                    foreach (var line in temnLines)
                    {
                        CorPts.Add(line.PtSt);
                        CorPts.Add(line.PtEnd);
                    }
                    GMath.MergePts(CorPts);
                    foreach (var pt in CorPts)
                    {
                        pt.MoveSelf(ConPts[0].X, ConPts[0].Y);
                    }
                    ConPts.AddRange(CorPts);
                }
            }
            else
            {
                if (_columntype == CoTy.Rectangle)
                {
                    if (ConPts.Count == 10)
                    {
                        ConPts.RemoveAt(8);
                    }
                }
                else if (_columntype == CoTy.Circle)
                {
                    if (ConPts.Count == 6)
                    {
                        ConPts.RemoveAt(4);
                    }
                }
                else if (_columntype == CoTy.Irregular)
                {
                    if (ConPts.Count == 2)
                    {
                        ConPts.RemoveAt(1);
                    }
                    ConPtsRecorder.Add(ConPts[0]);
                    ConPts.Clear();
                    ConPts.AddRange(ConPtsRecorder);
                    List<DbLine> temnLines = new List<DbLine>();
                    temnLines.AddRange(SecType.MatPanelInfos[0].Item1.EleCopy().Lines);
                    List<DbPt> CorPts = new List<DbPt>();
                    foreach (var line in temnLines)
                    {
                        CorPts.Add(line.PtSt);
                        CorPts.Add(line.PtEnd);
                    }
                    GMath.MergePts(CorPts);
                    foreach (var pt in CorPts)
                    {
                        pt.MoveSelf(ConPts[0].X, ConPts[0].Y);
                    }
                    ConPts.AddRange(CorPts);
                }
            }
        }

        /// <summary>
        /// 斜柱的顶部面域,保存
        /// </summary>
        private DbRegion verTopSecRegion = new DbRegion();

        /// <summary>
        /// 计算柱填充
        /// </summary>
        public void ArcColumnFill()
        {
            bottomLines.Clear();
            topLines.Clear();
            DbHatch columnsecfill = new DbHatch();

            var sc = 100;
            if (_view != null)
            {
                sc = _view.ViewScale;
            }

            if (_columntype == CoTy.Rectangle)
            {
                List<DbPt> ConPtsFill = new List<DbPt>();
                if (!_ifVer)
                {
                    ConPtsFill.AddRange(new DbPt[] { ConPts[5], ConPts[6], ConPts[7], ConPts[8] });
                    foreach (var pt in ConPtsFill)
                    {
                        pt.Z = 0;
                    }
                    if (_coreMateria.IsHatchSecHasValue(_view.ViewScale, out var hatchParam)) //填充前需要判断是否有填充样式
                    {
                        columnsecfill = new DbHatch(ConPtsFill, hatchParam.HatchIdx, hatchParam.Scale, hatchParam.Color);
                    }
                }
                else
                {
                    double verang = GMath.AngleToRadians(_veAngle);
                    double Xdis = ArcGFunc.SectionLevel / Math.Tan(verang);
                    double height = _view.TopLevel + _topOffset - (_view.BotLevel + _bottomOffset);
                    double XTopDis = height / Math.Tan(verang);
                    double dcos = 1 / Math.Sin(verang);
                    DbPt vec65 = ConPts[6] - ConPts[5]; vec65.Normalize();
                    if (_skipCalculatePassLinesInEleCopy)
                    {
                        ConPts[9] = ConPts[0].EleCopy().Move(vec65, XTopDis).Move(0, 0, height);
                    }

                    DbLine axis = new DbLine(ConPts[9].EleCopy(), ConPts[0].EleCopy());
                    for (int i = 0; i < ConPts.Count - 1; i++)
                    {
                        ConPts[i] = GetSkewColPt(axis, ConPts[i], dcos);
                    }
                    List<DbPt> topPts = new List<DbPt>();
                    for (int i = 1; i < ConPts.Count - 1; i++)
                    {
                        topPts.Add(ConPts[i].EleCopy().Move(vec65, Xdis));
                    }
                    topPts = ArcGFunc.SortPointsClockwise(topPts);
                    foreach (var pt in topPts)
                    {
                        pt.Z = 0;
                    }
                    verTopSecRegion = new DbRegion(topPts);
                    if (_coreMateria.IsHatchSecHasValue(_view.ViewScale, out var hatchParam)) //填充前需要判断是否有填充样式
                    {
                        columnsecfill = new DbHatch(verTopSecRegion, hatchParam.HatchIdx, hatchParam.Scale, hatchParam.Color);
                    }
                }
            }

            else if (_columntype == CoTy.Circle)
            {
                if (!_ifVer)
                {
                    DbCircle temcircle = new DbCircle(ConPts[0], _radius * 2);
                    var region = GMath.GetRegionOfCircle(temcircle);
                    if (_coreMateria.IsHatchSecHasValue(_view.ViewScale, out var hatchParam)) //填充前需要判断是否有填充样式
                    {
                        columnsecfill = new DbHatch(region, hatchParam.HatchIdx, hatchParam.Scale, hatchParam.Color);
                    }
                }
                else
                {
                    double verang = GMath.AngleToRadians(_veAngle);
                    double Xdis = ArcGFunc.SectionLevel / Math.Tan(verang);
                    double height = _view.TopLevel + _topOffset - (_view.BotLevel + _bottomOffset);
                    double XTopDis = height / Math.Tan(verang);
                    double dcos = 1 / Math.Sin(verang);
                    DbPt vec02 = ConPts[2] - ConPts[0];
                    vec02.Normalize();
                    if (_skipCalculatePassLinesInEleCopy)
                    {
                        ConPts[5] = ConPts[0].EleCopy().Move(vec02, XTopDis);
                    }
                    //计算底部核心层
                    DbLine axis = new DbLine(ConPts[0].EleCopy(), ConPts[5].EleCopy());
                    DbCircle temcircle = new DbCircle(ConPts[0], _radius * 2);
                    DbLine paraL = GMath.ParaLine(axis, temcircle.CPt);
                    double rad1 = temcircle.Rad1 * dcos;
                    DbPt cpt1 = GMath.GetPtOnLine(paraL, true, rad1);
                    DbPt cpt2 = GMath.GetPtOutLine(paraL, true, rad1);
                    temcircle = new DbCircle(cpt1, cpt2, temcircle.Rad1);
                    //计算顶部核心层
                    DbPt topCenPt = ConPts[0].EleCopy().Move(vec02, Xdis);
                    DbCircle topCir = new DbCircle(topCenPt, _radius * 2);
                    paraL = GMath.ParaLine(axis, topCenPt);
                    rad1 = topCir.Rad1 * dcos;
                    cpt1 = GMath.GetPtOnLine(paraL, true, rad1);
                    cpt2 = GMath.GetPtOutLine(paraL, true, rad1);
                    topCir = new DbCircle(cpt1, cpt2, topCir.Rad1);
                    List<DbLine> cirLines = ArcGFunc.FitEllipseToLines(topCir, 30);
                    List<DbLoop> cirLoops = GMath.GetCwLoopsOfPls(cirLines);
                    verTopSecRegion = new DbRegion(cirLoops[0]);
                    if (_coreMateria.IsHatchSecHasValue(_view.ViewScale, out var hatchParam)) //填充前需要判断是否有填充样式
                    {
                        columnsecfill = new DbHatch(verTopSecRegion, hatchParam.HatchIdx, hatchParam.Scale, hatchParam.Color);
                    }
                }
            }

            else if (_columntype == CoTy.Irregular)
            {
                if (!_ifVer)
                {
                    //线作为驱动数据再行为函数中进行矫正
                    List<DbLine> inneeLines = new List<DbLine>();
                    for (int i = 0; i < secLoopLines.Count; i++)
                    {
                        DbLine line = secLoopLines[i];
                        if (_hoAngle != 0)
                        {
                            double horang = GMath.AngleToRadians(_hoAngle);
                            line.RotateSelf(ConPts[0], horang);
                        }
                        inneeLines.Add(line);
                    }
                    bottomLines.AddRange(inneeLines);

                    List<DbLoop> coorLoops = GMath.GetCwLoopsOfPls(inneeLines);
                    if (coorLoops.Count > 0)
                    {
                        DbRegion corRegion = new DbRegion(coorLoops[0]);
                        columnsecfill = new DbHatch(corRegion, _coreMateria.HatchSecInd, _coreMateria.HatchSecScale, _coreMateria.HatchSecColor);
                    }

                }
                else
                {
                    //线作为驱动数据再行为函数中进行矫正
                    List<DbLine> BottomInneeLines = new List<DbLine>();
                    for (int i = 0; i < secLoopLines.Count; i++)
                    {
                        BottomInneeLines.Add(secLoopLines[i]);
                    }

                    if (_skipCalculatePassLinesInEleCopy)
                    {
                        if (_hoAngle != 0)
                        {
                            double ang = GMath.AngleToRadians(_hoAngle);
                            foreach (var line in secLoopLines)
                            {
                                line.RotateSelf(ConPts[0], ang);
                            }
                        }
                        //_skipCalculatePassLinesInEleCopy = false;
                    }
                    ifRptate = true;
                    DbPt referPt = ConPts[0].EleCopy().Move(1, 0);
                    if (_hoAngle != 0)
                    {
                        double horang = GMath.AngleToRadians(_hoAngle);
                        referPt.RotateSelf(ConPts[0], horang);
                    }
                    DbPt vec = referPt - ConPts[0];
                    vec.Normalize();

                    double verang = GMath.AngleToRadians(_veAngle);
                    double Xdis = ArcGFunc.SectionLevel / Math.Tan(verang);

                    double height = _view.TopLevel + _topOffset - (_view.BotLevel + _bottomOffset);
                    double XTopDis = height / Math.Tan(verang);
                    double dcos = 1 / Math.Sin(verang);
                    if (_skipCalculatePassLinesInEleCopy)
                    {
                        ConPts[1] = ConPts[0].EleCopy().Move(vec, XTopDis).Move(0, 0, height);//顶部控制点
                    }

                    DbLine axis = new DbLine(ConPts[1].EleCopy(), ConPts[0].EleCopy());
                    //投影底部的多边形【未考虑弧形】
                    //使用一个列表记录底部线的情况，同时考虑直线段和弧线段
                    List<(bool ifArc, DbPt ptst, DbPt ptend, DbPt ptmid)> bottomInneeLinesInfo = new List<(bool ifArc, DbPt ptst, DbPt ptend, DbPt ptmid)>();
                    foreach (var line in BottomInneeLines)
                    {
                        var bottomLineIfarc = line.IfArc;
                        var bottomLinePtst = line.PtSt.EleCopy();
                        var bootomLinePtend = line.PtEnd.EleCopy();
                        var bootomLinePtmid = line.PtMid.EleCopy();

                        bottomLinePtst = GetSkewColPt(axis, bottomLinePtst, dcos);
                        bootomLinePtend = GetSkewColPt(axis, bootomLinePtend, dcos);
                        bootomLinePtmid = GetSkewColPt(axis, bootomLinePtmid, dcos);
                        //if (bottomLinePtst.X==double.NaN)
                        //{
                        //    return;
                        //}

                        bottomInneeLinesInfo.Add((bottomLineIfarc, bottomLinePtst, bootomLinePtend, bootomLinePtmid));
                    }

                    foreach (var info in bottomInneeLinesInfo)
                    {
                        if (!info.ifArc) //如果不是弧线段
                        {
                            bottomLines.Add(new DbLine(info.ptst, info.ptend));
                        }
                        else //如果是弧线段
                        {
                            bottomLines.Add(new DbLine(info.ptst, info.ptend, info.ptmid));
                        }
                    }

                    //投影顶部的多边形
                    List<DbPt> topInneeLinesPts = new List<DbPt>();
                    foreach (var line in bottomLines)
                    {
                        var movedLine = line.EleCopy().Move(vec, Xdis);
                        topLines.Add(movedLine);
                    }

                    List<DbLoop> topFillLoops = GMath.GetCwLoopsOfPls(topLines);
                    if (topFillLoops.Count > 0)
                    {
                        DbRegion corRegion = new DbRegion(topFillLoops[0]);
                        columnsecfill = new DbHatch(corRegion, _coreMateria.HatchSecInd, _coreMateria.HatchSecScale, _coreMateria.HatchSecColor);
                    }

                }
            }
            Hatchs.Add(columnsecfill);
        }

        /// <summary>
        /// 返回图元使用材料的Name
        /// </summary>
        public HashSet<string> GetDbMaterial()
        {
            var list = new HashSet<string>();
            list.Add(_coreMateria.Name);
            list.Add(_surfaceMateria.Name);
            return list;
        }

        /// <summary>
        /// 计算柱子的几何中心点 - 核心几何计算方法
        /// 根据插入点和偏移参数，计算出柱子的真实几何中心位置
        /// 这个方法考虑了插入点可能在柱子的任意位置（不一定是中心）
        /// 算法原理：通过分析插入点相对于中心的象限位置，计算出正确的中心点坐标
        /// </summary>
        public void CalcuPtcenter()
        {
            // 判断柱子类型：矩形柱的中心点计算
            if (_columntype == CoTy.Rectangle)
            {
                // 矩形柱中心点计算算法
                // 核心思想：根据左右偏移和上下偏移的大小关系，判断插入点在哪个象限
                // 然后计算从插入点到中心点的向量偏移
                
                // 第一象限判断：插入点相对于中心控制点在右上象限
                // 条件：左偏移≥右偏移 且 下偏移≥上偏移
                if (_leftRec >= _rightRec && _bottomRec >= _topRec)
                {
                    // 计算逻辑：从插入点向左下移动到中心点
                    // X方向：向左移动 (柱宽/2 - 右偏移) 的距离
                    // Y方向：向下移动 (柱高/2 - 上偏移) 的距离
                    _ptcenter = _insertpt.Move(-(_transverseWith / 2 - _rightRec), -(_verticalwidth / 2 - _topRec));
                }

                // 第二象限判断：插入点相对于中心控制点在左上象限
                // 条件：左偏移≤右偏移 且 上偏移≤下偏移
                else if (_leftRec <= _rightRec && _topRec <= _bottomRec)
                {
                    // 计算逻辑：从插入点向右下移动到中心点
                    // X方向：向右移动 (柱宽/2 - 左偏移) 的距离
                    // Y方向：向下移动 (柱高/2 - 上偏移) 的距离
                    _ptcenter = _insertpt.Move(_transverseWith / 2 - _leftRec, -(_verticalwidth / 2 - _topRec));
                }

                // 第三象限判断：插入点相对于中心控制点在左下象限
                // 条件：左偏移≤右偏移 且 上偏移≥下偏移
                else if (_leftRec <= _rightRec && _topRec >= _bottomRec)
                {
                    // 计算逻辑：从插入点向右上移动到中心点
                    // X方向：向右移动 (柱宽/2 - 左偏移) 的距离
                    // Y方向：向上移动 (柱高/2 - 下偏移) 的距离
                    _ptcenter = _insertpt.Move((_transverseWith / 2 - _leftRec), (_verticalwidth / 2 - _bottomRec));
                }

                // 第四象限判断：插入点相对于中心控制点在右下象限
                // 条件：上偏移≥下偏移 且 左偏移≥右偏移
                else if (_topRec >= _bottomRec && _leftRec >= _rightRec)
                {
                    // 计算逻辑：从插入点向左上移动到中心点
                    // X方向：向左移动 (柱宽/2 - 右偏移) 的距离
                    // Y方向：向上移动 (柱高/2 - 下偏移) 的距离
                    _ptcenter = _insertpt.Move(-(_transverseWith / 2 - _rightRec), _verticalwidth / 2 - _bottomRec);
                }
                else 
                {
                    // 异常情况处理：如果偏移参数不符合任何象限判断条件
                    // 直接使用插入点作为中心点（容错处理）
                    _ptcenter = _insertpt;
                }

                // 将计算出的中心点设置为第0个控制点（中心控制点）
                ConPts[0] = _ptcenter;
            }

            // 判断柱子类型：圆形柱的中心点计算
            if (_columntype == CoTy.Circle)
            {
                // 圆形柱中心点计算算法
                // 核心思想：类似矩形柱，但使用半径代替宽度和高度
                
                // 第一象限判断：插入点相对于圆心在右上象限
                // 条件：左偏移≥右偏移 且 下偏移≥上偏移
                if (_leftCir >= _rightCir && _bottomCir >= _topCir)
                {
                    // 计算逻辑：从插入点向左下移动到圆心
                    // X方向：向左移动 (半径 - 右偏移) 的距离
                    // Y方向：向下移动 (半径 - 上偏移) 的距离
                    _ptcenter = _insertpt.Move(-(_radius - _rightCir), -(_radius - _topCir));
                }

                // 第二象限判断：插入点相对于圆心在左上象限
                // 条件：左偏移≤右偏移 且 上偏移≤下偏移
                else if (_leftCir <= _rightCir && _topCir <= _bottomCir)
                {
                    // 计算逻辑：从插入点向右下移动到圆心
                    // X方向：向右移动 (半径 - 左偏移) 的距离
                    // Y方向：向下移动 (半径 - 上偏移) 的距离
                    _ptcenter = _insertpt.Move(_radius - _leftCir, -(_radius - _topCir));
                }

                // 第三象限判断：插入点相对于圆心在左下象限
                // 条件：左偏移≤右偏移 且 上偏移≥下偏移
                else if (_leftCir <= _rightCir && _topCir >= _bottomCir)
                {
                    // 计算逻辑：从插入点向右上移动到圆心
                    // X方向：向右移动 (半径 - 左偏移) 的距离
                    // Y方向：向上移动 (半径 - 下偏移) 的距离
                    _ptcenter = _insertpt.Move((_radius - _leftCir), (_radius - _bottomCir));
                }

                // 第四象限判断：插入点相对于圆心在右下象限
                // 条件：上偏移≥下偏移 且 左偏移≥右偏移
                else if (_topCir >= _bottomCir && _leftCir >= _rightCir)
                {
                    // 计算逻辑：从插入点向左上移动到圆心
                    // X方向：向左移动 (半径 - 右偏移) 的距离
                    // Y方向：向上移动 (半径 - 下偏移) 的距离
                    _ptcenter = _insertpt.Move(-(_radius - _rightCir), _radius - _bottomCir);
                }
                else 
                {
                    // 异常情况处理：如果偏移参数不符合任何象限判断条件
                    // 直接使用插入点作为圆心（容错处理）
                    _ptcenter = _insertpt;
                }
            }

            // 最终设置：将计算出的中心点设置为第0个控制点
            // 这个控制点是所有后续几何计算的基础
            ConPts[0] = _ptcenter;
        }

        /// <summary>
        /// 根据中心控制点ConPts[0]计算所有其他控制点的位置 - 控制点生成算法
        /// 这个方法是几何计算的核心，负责确定柱子的形状和大小
        /// 算法原理：基于中心点和几何参数，计算出所有用于绘制和编辑的控制点
        /// </summary>
        public void CalcuPts()
        {
            // 判断柱子类型：矩形柱的控制点计算
            if (_columntype == CoTy.Rectangle)
            {
                // 矩形柱控制点计算算法
                // 总共需要计算8个控制点：4个边中点 + 4个角点
                
                // 第一步：计算四个边的中点（用于显示和编辑操作）
                // 这些中点是用户可以拖拽的编辑点
                ConPts[1] = ConPts[0].Move(new DbPt(0, 1), _verticalwidth / 2);   // 上边中点：从中心向上移动半个高度
                ConPts[2] = ConPts[0].Move(new DbPt(1, 0), _transverseWith / 2);  // 右边中点：从中心向右移动半个宽度
                ConPts[3] = ConPts[0].Move(new DbPt(0, -1), _verticalwidth / 2);  // 下边中点：从中心向下移动半个高度
                ConPts[4] = ConPts[0].Move(new DbPt(-1, 0), _transverseWith / 2); // 左边中点：从中心向左移动半个宽度

                // 第二步：计算四个角点（用于绘制矩形边界线）
                // 这些角点定义了矩形的实际边界
                ConPts[5] = ConPts[1].Move(new DbPt(-1, 0), _transverseWith / 2); // 左上角点：从上边中点向左移动半个宽度
                ConPts[6] = ConPts[1].Move(new DbPt(1, 0), _transverseWith / 2);  // 右上角点：从上边中点向右移动半个宽度
                ConPts[7] = ConPts[3].Move(new DbPt(1, 0), _transverseWith / 2);  // 右下角点：从下边中点向右移动半个宽度
                ConPts[8] = ConPts[3].Move(new DbPt(-1, 0), _transverseWith / 2); // 左下角点：从下边中点向左移动半个宽度
                
                // 第三步：应用旋转变换（如果柱子有旋转角度）
                // 根据柱子是否为斜柱来决定旋转策略
                if (!_ifVer)  // 垂直柱：所有控制点都需要绕中心点旋转
                {
                    // 遍历所有控制点，逐个应用旋转变换
                    foreach (var item in ConPts)
                    {
                        // 绕中心点旋转指定角度（角度转换为弧度）
                        item.RotateSelf(ConPts[0], _hoAngle * Math.PI / 180);
                    }
                }
                else  // 斜柱：除了最后一个控制点外都需要旋转
                {
                    // 最后一个控制点是斜柱的顶部定位点，不参与旋转
                    for (int i = 0; i < ConPts.Count - 1; i++)
                    {
                        // 绕中心点旋转指定角度（角度转换为弧度）
                        ConPts[i].RotateSelf(ConPts[0], _hoAngle * Math.PI / 180);
                    }
                }
            }

            // 判断柱子类型：圆形柱的控制点计算
            else if (_columntype == CoTy.Circle)
            {
                // 圆形柱控制点计算算法
                // 总共需要计算4个控制点：圆周上的4个边界点
                
                // 计算圆周上的四个边界点（用于显示和编辑操作）
                // 这些点定义了圆形的边界，也是用户可以拖拽的编辑点
                ConPts[1] = ConPts[0].Copy().Move(new DbPt(0, 1), _radius);   // 上边界点：从圆心向上移动半径距离
                ConPts[2] = ConPts[0].Copy().Move(new DbPt(1, 0), _radius);   // 右边界点：从圆心向右移动半径距离
                ConPts[3] = ConPts[0].Copy().Move(new DbPt(0, -1), _radius);  // 下边界点：从圆心向下移动半径距离
                ConPts[4] = ConPts[0].Copy().Move(new DbPt(-1, 0), _radius);  // 左边界点：从圆心向左移动半径距离
                
                // 应用旋转变换（如果柱子有旋转角度）
                // 根据柱子是否为斜柱来决定旋转策略
                if (!_ifVer)  // 垂直柱：所有控制点都需要绕圆心旋转
                {
                    // 遍历所有控制点，逐个应用旋转变换
                    foreach (var item in ConPts)
                    {
                        // 绕圆心旋转指定角度（角度转换为弧度）
                        item.RotateSelf(ConPts[0], _hoAngle * Math.PI / 180);
                    }
                }
                else  // 斜柱：除了最后一个控制点外都需要旋转
                {
                    // 最后一个控制点是斜柱的顶部定位点，不参与旋转
                    for (int i = 0; i < ConPts.Count - 1; i++)
                    {
                        // 绕圆心旋转指定角度（角度转换为弧度）
                        ConPts[i].RotateSelf(ConPts[0], _hoAngle * Math.PI / 180);
                    }
                }
            }
        }

        /// <summary>
        /// 根据控制点计算得到所有柱主体截面的线
        /// </summary>
        public void CalcuColumnLines()
        {
            List<DbLine> inneeLines = new List<DbLine>();
            List<DbCircle> innerCircles = new List<DbCircle>();

            if (_columntype == CoTy.Rectangle)
            {
                DbLine linetop = new DbLine(ConPts[5], ConPts[6]);
                DbLine lineright = new DbLine(ConPts[6], ConPts[7]);
                DbLine linebottom = new DbLine(ConPts[7], ConPts[8]);
                DbLine lineleft = new DbLine(ConPts[8], ConPts[5]);
                inneeLines.Add(linetop);
                inneeLines.Add(lineright);
                inneeLines.Add(linebottom);
                inneeLines.Add(lineleft);
                if (_ifVer)
                {
                    double verang = GMath.AngleToRadians(_veAngle);
                    double Xdis = ArcGFunc.SectionLevel / Math.Tan(verang);
                    DbPt vec65 = ConPts[6] - ConPts[5]; vec65.Normalize();
                    DbLine line5 = linetop.EleCopy().Move(vec65, Xdis);
                    DbLine line6 = lineright.EleCopy().Move(vec65, Xdis);
                    DbLine line7 = linebottom.EleCopy().Move(vec65, Xdis);
                    DbLine line8 = lineleft.EleCopy().Move(vec65, Xdis);
                    DbLine line9 = new DbLine(linetop.PtEnd.EleCopy(), line5.PtSt.EleCopy());
                    DbLine line10 = new DbLine(linebottom.PtSt.EleCopy(), line7.PtEnd.EleCopy());
                    inneeLines.Add(line5);
                    inneeLines.Add(line6);
                    inneeLines.Add(line7);
                    inneeLines.Add(line8);
                    inneeLines.Add(line9);
                    inneeLines.Add(line10);
                }
            }

            else if (_columntype == CoTy.Circle)
            {
                DbCircle bottomCircle = new DbCircle(ConPts[0], _radius * 2);
                if (!_ifVer)
                {
                    innerCircles.Add(bottomCircle);
                }
                else
                {
                    double verang = GMath.AngleToRadians(_veAngle);
                    double Xdis = ArcGFunc.SectionLevel / Math.Tan(verang);
                    double height = _view.TopLevel + _topOffset - (_view.BotLevel + _bottomOffset);
                    double XTopDis = height / Math.Tan(verang);
                    double dcos = 1 / Math.Sin(verang);

                    DbPt vec02 = ConPts[2] - ConPts[0];
                    vec02.Normalize();

                    //ConPts[5] = ConPts[0].EleCopy().Move(vec02, XTopDis);
                    DbLine axis = new DbLine(ConPts[0].EleCopy(), ConPts[5].EleCopy());

                    DbLine paraL = GMath.ParaLine(axis, bottomCircle.CPt);
                    double rad1 = bottomCircle.Rad1 * dcos;
                    DbPt cpt1 = GMath.GetPtOnLine(paraL, true, rad1);
                    DbPt cpt2 = GMath.GetPtOutLine(paraL, true, rad1);
                    bottomCircle = new DbCircle(cpt1, cpt2, bottomCircle.Rad1);

                    DbCircle topCir = bottomCircle.EleCopy().Move(vec02, Xdis);
                    DbPt secCirCenPt = ConPts[0].EleCopy().Move(vec02, Xdis);
                    DbLine midLine = new DbLine(ConPts[0].EleCopy(), secCirCenPt);
                    DbLine line1 = GMath.LineOffset(midLine, true, _radius);
                    DbLine line2 = GMath.LineOffset(midLine, false, _radius);
                    innerCircles.Add(bottomCircle);
                    innerCircles.Add(topCir);
                    inneeLines.Add(line1);
                    inneeLines.Add(line2);
                }
            }

            else if (_columntype == CoTy.Irregular)
            {
                if (!_ifVer)
                {
                    //在计算填充时已经完成线的操作
                    inneeLines.AddRange(bottomLines);
                    inneeLines.AddRange(topLines);
                }
                else
                {
                    //计算斜向的面层线
                    for (int i = 0; i < bottomLines.Count; i++)
                    {
                        DbLine verLine = new DbLine();
                        if (!bottomLines[i].IfArc)
                        {
                            verLine = new DbLine(bottomLines[i].PtSt.EleCopy(), topLines[i].PtSt.EleCopy());
                            verLines.Add(verLine);
                        }
                        else
                        {
                            verLine = new DbLine(bottomLines[i].PtSt.EleCopy(), topLines[i].PtSt.EleCopy());
                            DbLine midPtLine = new DbLine(bottomLines[i].PtMid.EleCopy(), topLines[i].PtMid.EleCopy());
                            verLines.Add(verLine);
                            verLines.Add(midPtLine);
                        }
                    }

                    //使用底部和顶部的全面域裁剪掉斜向的面层线
                    List<DbLoop> bottomLoops = GMath.GetCwLoopsOfPls(bottomLines);
                    DbRegion bottomFullRegion = new DbRegion(bottomLoops);
                    verLines = GMath.GetLinesOutRegion(verLines, bottomFullRegion);

                    List<DbLoop> topLoops = GMath.GetCwLoopsOfPls(topLines);
                    DbRegion topFullRegion = new DbRegion(topLoops);
                    verLines = GMath.GetLinesOutRegion(verLines, topFullRegion);

                    GMath.LineMergeAll(verLines);

                    //在计算填充时已经完成线的操作
                    inneeLines.AddRange(bottomLines);
                    inneeLines.AddRange(topLines);
                    inneeLines.AddRange(verLines);
                }
            }

            Lines.AddRange(inneeLines);
            Circles.AddRange(innerCircles);
        }

        /// <summary>
        /// 计算需要再PreviewControl面板展示的线，包括周围的线和中心插入点的线
        /// </summary>
        public void CalculatePassLines()
        {
            if (_skipCalculatePassLinesInEleCopy)
            {
                if (_columntype == CoTy.Rectangle)
                {
                    LinesPassAround.Clear();
                    var lineleft = GMath.LineOffset(new DbLine(ConPts[5], ConPts[8]), false, 100);
                    var lineright = GMath.LineOffset(new DbLine(ConPts[8], ConPts[7]), false, 100);
                    var linetop = GMath.LineOffset(new DbLine(ConPts[7], ConPts[6]), false, 100);
                    var linebuttom = GMath.LineOffset(new DbLine(ConPts[6], ConPts[5]), false, 100);
                    LinesPassAround.Add(lineleft);
                    LinesPassAround.Add(lineright);
                    LinesPassAround.Add(linetop);
                    LinesPassAround.Add(linebuttom);

                    var ptleft = _insertpt.Copy().Move(new DbPt(-1, 0), 50);
                    var ptright = _insertpt.Copy().Move(new DbPt(1, 0), 50);
                    var pttop = _insertpt.Copy().Move(new DbPt(0, 1), 50);
                    var ptbottom = _insertpt.Copy().Move(new DbPt(0, -1), 50);

                    var lineH = new DbLine(ptleft, ptright);
                    var lineV = new DbLine(pttop, ptbottom);

                    LinesPassInsert.Add(lineH);
                    LinesPassInsert.Add(lineV);
                }

                else if (_columntype == CoTy.Circle)
                {
                    LinesPassAround.Clear();
                }

                //else if (_columntype == CoTy.Irregular)
                //{
                //    MessageBox.Show("还没写");
                //}

            }

            _skipCalculatePassLinesInEleCopy = true;

        }

        /// <summary>
        /// 底部面层的线储存列表，不保存
        /// </summary>
        private List<DbLine> bottomLines = new List<DbLine>();
        /// <summary>
        /// 顶部面层的线储存列表，不保存
        /// </summary>
        private List<DbLine> topLines = new List<DbLine>();
        /// <summary>
        /// 斜部面层的线储存列表
        /// </summary>
        private List<DbLine> verLines = new List<DbLine>();

        /// <summary>
        /// 计算默认面层，如果出图比例大于等于1:100，则计算并显示，并通过SurfLines传递到solid计算；如果小于：100，则只计算但不显示，并通过SurfLines传递到solid计算
        /// </summary>
        public void CalcuDefaultSurfaceLines()
        {
            verLines.Clear();
            SurCircle.Clear();
            SurfLines.Clear();
            var sc = 50;
            if (_view != null) { sc = _view.ViewScale; }

            if (_columntype == CoTy.Rectangle)
            {
                DbLine linetop;
                DbLine lineright;
                DbLine linebottom;
                DbLine lineleft;

                if (!_ifVer)
                {
                    linetop = new DbLine(ConPts[5].Copy(), ConPts[6].Copy());
                    DbPt vec01 = ConPts[0] - ConPts[1];
                    vec01.Normalize();
                    linetop = linetop.EleCopy().Move(-vec01, _surfthick);
                    linetop = ArcGFunc.LineExtend(linetop, _surfthick, _surfthick);

                    lineright = new DbLine(ConPts[6].Copy(), ConPts[7].Copy());
                    DbPt vec02 = ConPts[0] - ConPts[2];
                    vec02.Normalize();
                    lineright = lineright.EleCopy().Move(-vec02, _surfthick);
                    lineright = ArcGFunc.LineExtend(lineright, _surfthick, _surfthick);

                    linebottom = new DbLine(ConPts[7].Copy(), ConPts[8].Copy());
                    DbPt vec03 = ConPts[0] - ConPts[3];
                    vec03.Normalize();
                    linebottom = linebottom.EleCopy().Move(-vec03, _surfthick);
                    linebottom = ArcGFunc.LineExtend(linebottom, _surfthick, _surfthick);

                    lineleft = new DbLine(ConPts[8].Copy(), ConPts[5].Copy());
                    DbPt vec04 = ConPts[0] - ConPts[4];
                    vec04.Normalize();
                    lineleft = lineleft.EleCopy().Move(-vec04, _surfthick);
                    lineleft = ArcGFunc.LineExtend(lineleft, _surfthick, _surfthick);

                    SurfLines.Add(linetop);
                    SurfLines.Add(linebottom);
                    SurfLines.Add(lineleft);
                    SurfLines.Add(lineright);
                }
                else
                {
                    double verang = GMath.AngleToRadians(_veAngle);
                    double sinVa = Math.Sin(verang);
                    List<DbLine> offsetLines = new List<DbLine>();//收集最后所有的面层的线
                    DbPt footPt = new DbPt();
                    //计算顶部面层的线
                    for (int i = 4; i < Lines.Count - 2; i++)
                    {
                        topLines.Add(Lines[i]);
                    }
                    DbPt topCenPt = ArcGFunc.CalculateCentroid(topLines);
                    List<DbLine> TopOffsetLines = new List<DbLine>();//收集顶部截面的面层的线
                    foreach (var line in topLines)
                    {
                        footPt = GMath.FootPt(line, topCenPt);
                        DbPt vec = footPt - topCenPt;
                        vec.Normalize();
                        DbLine offsetLine = line.EleCopy().Move(vec, _surfthick / sinVa);
                        TopOffsetLines.Add(offsetLine);
                    }

                    TopOffsetLines.Add(TopOffsetLines[0]);
                    for (int i = 0; i < TopOffsetLines.Count - 1; i++)
                    {
                        (bool Intersects, DbLine ExtendedLine1, DbLine ExtendedLine2) = ArcGFunc.CheckIntersection(TopOffsetLines[i], TopOffsetLines[i + 1]);
                        if (ExtendedLine1 != null)
                        {
                            TopOffsetLines[i] = ExtendedLine1.EleCopy();
                        }
                        if (ExtendedLine2 != null)
                        {
                            TopOffsetLines[i + 1] = ExtendedLine2.EleCopy();
                        }
                    }
                    GMath.LineMergeAll(TopOffsetLines);

                    //计算底部面层的线
                    for (int i = 0; i < 4; i++)
                    {
                        bottomLines.Add(Lines[i]);
                    }
                    DbPt bottomCenPt = ArcGFunc.CalculateCentroid(bottomLines);
                    List<DbLine> bottomOffsetLines = new List<DbLine>();//收集顶部截面的面层的线

                    for (int i = 0; i < bottomLines.Count; i++)
                    {
                        footPt = GMath.FootPt(bottomLines[i], bottomCenPt);
                        DbPt vec = footPt - bottomCenPt;
                        vec.Normalize();
                        DbLine offsetLine = bottomLines[i].EleCopy().Move(vec, _surfthick / sinVa);
                        bottomOffsetLines.Add(offsetLine);
                    }
                    bottomOffsetLines.Add(bottomOffsetLines[0]);
                    for (int i = 0; i < bottomOffsetLines.Count - 1; i++)
                    {
                        (bool Intersects, DbLine ExtendedLine1, DbLine ExtendedLine2) = ArcGFunc.CheckIntersection(bottomOffsetLines[i], bottomOffsetLines[i + 1]);
                        if (ExtendedLine1 != null)
                        {
                            bottomOffsetLines[i] = ExtendedLine1.EleCopy();
                        }
                        if (ExtendedLine2 != null)
                        {
                            bottomOffsetLines[i + 1] = ExtendedLine2.EleCopy();
                        }
                    }
                    GMath.LineMergeAll(bottomOffsetLines);
                    bottomLines.Clear();
                    bottomLines.AddRange(bottomOffsetLines);
                    bottomOffsetLines.RemoveAt(0);

                    //计算斜柱斜向的线
                    for (int i = 8; i < Lines.Count; i++)
                    {
                        verLines.Add(Lines[i]);
                    }
                    List<DbLine> verOffsetLines = new List<DbLine>();//收集斜部截面的面层的线
                    DbLine verlinetop = GMath.LineOffset(verLines[0], true, _surfthick / sinVa);
                    DbLine verlinebottom = GMath.LineOffset(verLines[1], false, _surfthick / sinVa);

                    offsetLines.Add(verlinetop);
                    offsetLines.Add(verlinebottom);
                    offsetLines.AddRange(bottomOffsetLines);
                    offsetLines.AddRange(TopOffsetLines);

                    SurfLines.AddRange(offsetLines);
                }

                if (sc >= 100 && _isshowsurface) //默认不显示，但是可以强制显示
                {
                    Lines.AddRange(SurfLines);
                }

                else if (sc >= 0 && sc < 100) //默认显示，但是可以强制不显示
                {
                    Lines.AddRange(SurfLines);
                    if (!_isshowsurface)
                    {
                        Lines = Lines.Except(SurfLines).ToList();
                    }
                }
            }

            else if (_columntype == CoTy.Circle)
            {
                if (!_ifVer)
                {
                    DbCircle bottomSurfCircle = new DbCircle(ConPts[0], (_radius + _surfthick) * 2);
                    SurCircle.Add(bottomSurfCircle);

                    arccolumnregion = GMath.GetRegionOfCircle(Circles[0]);
                    cutregion = GMath.GetRegionOfCircle(SurCircle[0]);
                    cutregion = GMath.GetSubtractRegion(cutregion, arccolumnregion);
                }
                else
                {
                    //把圆形柱的控制点映射过来
                    double verang = GMath.AngleToRadians(_veAngle);
                    double Xdis = ArcGFunc.SectionLevel / Math.Tan(verang);
                    double height = _view.TopLevel + _topOffset - (_view.BotLevel + _bottomOffset);
                    double XTopDis = height / Math.Tan(verang);
                    double dcos = 1 / Math.Sin(verang);
                    DbPt vec02 = ConPts[2] - ConPts[0];
                    vec02.Normalize();
                    ConPts[5] = ConPts[0].EleCopy().Move(vec02, XTopDis);
                    DbLine axis = new DbLine(ConPts[0].EleCopy(), ConPts[5].EleCopy());
                    for (int i = 0; i < ConPts.Count - 1; i++)
                    {
                        ConPts[i] = GetSkewColPt(axis, ConPts[i], dcos);
                    }

                    //计算圆斜柱底部面层                    
                    DbCircle buttomSurfCircle = new DbCircle(Circles[0].CPt, (_radius + _surfthick) * 2);
                    DbLine paraL = GMath.ParaLine(axis, buttomSurfCircle.CPt);
                    double rad1 = buttomSurfCircle.Rad1 * dcos;
                    DbPt cpt1 = GMath.GetPtOnLine(paraL, true, rad1);
                    DbPt cpt2 = GMath.GetPtOutLine(paraL, true, rad1);
                    buttomSurfCircle = new DbCircle(cpt1, cpt2, buttomSurfCircle.Rad1);
                    //计算顶部面层
                    DbCircle topSurfCircle = new DbCircle(Circles[1].CPt, (_radius + _surfthick) * 2);
                    paraL = GMath.ParaLine(axis, topSurfCircle.CPt);
                    rad1 = topSurfCircle.Rad1 * dcos;
                    cpt1 = GMath.GetPtOnLine(paraL, true, rad1);
                    cpt2 = GMath.GetPtOutLine(paraL, true, rad1);
                    topSurfCircle = new DbCircle(cpt1, cpt2, topSurfCircle.Rad1);

                    DbLine midLine = new DbLine(Circles[0].CPt, Circles[1].CPt);
                    DbLine line1 = GMath.LineOffset(midLine, true, _radius + _surfthick);
                    DbLine line2 = GMath.LineOffset(midLine, false, _radius + _surfthick);

                    List<DbPt> temPts = new List<DbPt>
                    {
                        line1.PtSt,
                        line1.PtEnd,
                        line2.PtSt,
                        line2.PtEnd
                    };
                    temPts = ArcGFunc.SortPointsClockwise(temPts);
                    DbRegion cutRegon = new DbRegion(temPts);
                    List<DbLine> buttomSurfCircleLines = ArcGFunc.FitEllipseToLines(buttomSurfCircle, 40);

                    List<DbLine> buttomSurfCircleCutLines = GMath.GetLinesOutRegion(buttomSurfCircleLines, cutRegon);
                    SurCircle.Add(topSurfCircle);

                    SurfLines.AddRange(buttomSurfCircleCutLines);
                    SurfLines.Add(line1);
                    SurfLines.Add(line2);

                    //计算底部核心层面域
                    List<DbLine> buttomCircleCutLines = ArcGFunc.FitEllipseToLines(Circles[0], 40);
                    List<DbLoop> buttonCircleLoops = GMath.GetCwLoopsOfPls(buttomCircleCutLines);
                    arccolumnregion = new DbRegion(buttonCircleLoops[0]);
                    //计算底部全面域
                    List<DbLoop> buttomSurfLoops = GMath.GetCwLoopsOfPls(buttomSurfCircleLines);
                    DbRegion fullRegion = new DbRegion(buttomSurfLoops[0]);
                    //计算面层的面域
                    cutregion = GMath.GetSubtractRegion(fullRegion, arccolumnregion);
                }

                if (sc >= 100 && _isshowsurface) //默认不显示，但是可以强制显示
                {
                    Circles.AddRange(SurCircle);
                    Lines.AddRange(SurfLines);
                }
                else if (sc >= 0 && sc < 100)
                {
                    Circles.AddRange(SurCircle);
                    Lines.AddRange(SurfLines);
                    if (!_isshowsurface)
                    {
                        Circles = Circles.Except(SurCircle).ToList();
                        Lines = Lines.Except(SurfLines).ToList();
                    }
                }
            }

            else if (_columntype == CoTy.Irregular)
            {
                List<DbLine> defaultSurfLines = new List<DbLine>();
                if (!_ifVer)
                {
                    List<DbLine> linesCopy = ArcGFunc.ArcLinesCopy(Lines);
                    DbPt Centroidpt = ArcGFunc.GetCentroid(linesCopy); //求的面积中心
                    foreach (var line in linesCopy)
                    {
                        DbLine offsetline = new DbLine();
                        DbPt footPt = GMath.GetFoot(Centroidpt, line);
                        DbPt vec = footPt - Centroidpt;
                        vec.Normalize();
                        if (!line.IfArc)
                        {
                            offsetline = line.EleCopy().Move(vec, _surfthick);
                        }
                        else
                        {
                            offsetline = GMath.LineArcOffset(line, true, _surfthick);
                        }

                        defaultSurfLines.Add(offsetline);
                    }
                    defaultSurfLines.Add(defaultSurfLines[0]);
                    for (int i = 0; i < defaultSurfLines.Count - 1; i++)
                    {
                        (bool ifInter, DbLine extendedLine1, DbLine extendedLine2) = ArcGFunc.CheckIntersection(defaultSurfLines[i], defaultSurfLines[i + 1]);
                        if (!ifInter && extendedLine1 != null && extendedLine2 != null)
                        {
                            defaultSurfLines[i] = extendedLine1;
                            if ((i) == defaultSurfLines.Count)
                            {
                                defaultSurfLines[0] = extendedLine2;
                            }
                            else
                            {
                                defaultSurfLines[i + 1] = extendedLine2;
                            }
                        }
                    }
                    linesCopy = ArcGFunc.SortConnectLines(linesCopy);
                    List<DbLoop> coreLoops = GMath.GetCwLoopsOfPls(linesCopy);
                    arccolumnregion = new DbRegion(coreLoops);
                    GMath.LineMergeAll(defaultSurfLines);
                    defaultSurfLines = ArcGFunc.SortConnectLines(defaultSurfLines);
                    List<DbLoop> surfLoops = GMath.GetCwLoopsOfPls(defaultSurfLines);
                    DbRegion fullSurfLines = new DbRegion(surfLoops);

                    cutregion = GMath.GetSubtractRegion(fullSurfLines, arccolumnregion);

                }
                else
                {
                    List<DbLine> bottomOffsetLines = new List<DbLine>();
                    List<DbLine> topOffsetLines = new List<DbLine>();
                    List<DbLine> verOffsetLines = new List<DbLine>();
                    double verang = GMath.AngleToRadians(_veAngle);
                    double sinVa = Math.Sin(verang);

                    //计算底部截面的全面层线
                    DbPt BotCenPt = ArcGFunc.GetCentroid(bottomLines);
                    foreach (DbLine line in bottomLines)
                    {
                        DbLine offsetline = new DbLine();
                        DbPt footPt = GMath.GetFoot(BotCenPt, line);
                        DbPt vec = footPt - BotCenPt;
                        vec.Normalize();
                        if (!line.IfArc)
                        {
                            offsetline = line.EleCopy().Move(vec, _surfthick / sinVa);
                        }
                        else
                        {
                            offsetline = GMath.LineArcOffset(line, true, _surfthick / sinVa);
                        }
                        bottomOffsetLines.Add(offsetline);
                    }


                    //计算顶部截面的全面层线
                    DbPt TopCenPt = ArcGFunc.GetCentroid(topLines);
                    foreach (DbLine line in topLines)
                    {
                        DbLine offsetline = new DbLine();
                        DbPt footPt = GMath.GetFoot(TopCenPt, line);
                        DbPt vec = footPt - TopCenPt;
                        vec.Normalize();
                        if (!line.IfArc)
                        {
                            offsetline = line.EleCopy().Move(vec, _surfthick / sinVa);
                        }
                        else
                        {
                            offsetline = GMath.LineArcOffset(line, true, _surfthick / sinVa);
                        }
                        topOffsetLines.Add(offsetline);
                    }

                    bottomOffsetLines.Add(bottomOffsetLines[0]);
                    for (int i = 0; i < bottomOffsetLines.Count - 1; i++)
                    {
                        (bool ifInter, DbLine extendedLine1, DbLine extendedLine2) = ArcGFunc.CheckIntersection(bottomOffsetLines[i], bottomOffsetLines[i + 1]);
                        if (!ifInter && extendedLine1 != null && extendedLine2 != null)
                        {
                            bottomOffsetLines[i] = extendedLine1;
                            if ((i) == bottomOffsetLines.Count)
                            {
                                bottomOffsetLines[0] = extendedLine2;
                            }
                            else
                            {
                                bottomOffsetLines[i + 1] = extendedLine2;
                            }
                        }
                    }
                    GMath.LineMergeAll(bottomOffsetLines);

                    topOffsetLines.Add(topOffsetLines[0]);
                    for (int i = 0; i < topOffsetLines.Count - 1; i++)
                    {
                        (bool ifInter, DbLine extendedLine1, DbLine extendedLine2) = ArcGFunc.CheckIntersection(topOffsetLines[i], topOffsetLines[i + 1]);
                        if (!ifInter && extendedLine1 != null && extendedLine2 != null)
                        {
                            topOffsetLines[i] = extendedLine1;
                            if ((i) == topOffsetLines.Count)
                            {
                                topOffsetLines[0] = extendedLine2;
                            }
                            else
                            {
                                topOffsetLines[i + 1] = extendedLine2;
                            }
                        }
                    }
                    GMath.LineMergeAll(topOffsetLines);

                    //计算斜向的面层线
                    for (int i = 0; i < bottomOffsetLines.Count; i++)
                    {
                        DbLine verLine = new DbLine();
                        if (!bottomOffsetLines[i].IfArc)
                        {
                            verLine = new DbLine(bottomOffsetLines[i].PtSt.EleCopy(), topOffsetLines[i].PtSt.EleCopy());
                            verOffsetLines.Add(verLine);
                        }
                        else
                        {
                            verLine = new DbLine(bottomOffsetLines[i].PtSt.EleCopy(), topOffsetLines[i].PtSt.EleCopy());
                            DbLine midPtLine = new DbLine(bottomOffsetLines[i].PtMid.EleCopy(), topOffsetLines[i].PtMid.EleCopy());
                            verOffsetLines.Add(verLine);
                            verOffsetLines.Add(midPtLine);
                        }
                    }
                    GMath.LineMergeAll(verOffsetLines);

                    //使用底部和顶部的全面域裁剪掉斜向的面层线
                    List<DbLoop> bottomLoops = GMath.GetCwLoopsOfPls(bottomOffsetLines);
                    DbRegion bottomFullRegion = new DbRegion(bottomLoops);
                    verOffsetLines = GMath.GetLinesOutRegion(verOffsetLines, bottomFullRegion);

                    List<DbLoop> topLoops = GMath.GetCwLoopsOfPls(topOffsetLines);
                    DbRegion topFullRegion = new DbRegion(topLoops);
                    verOffsetLines = GMath.GetLinesOutRegion(verOffsetLines, topFullRegion);

                    #region 先注释
                    //GMath.LineMergeAll(bottomOffsetLines);
                    //GMath.LineMergeAll(topOffsetLines);
                    //GMath.LineMergeAll(verOffsetLines);

                    ////使用斜向部分面层全面域裁剪底部面层的线
                    //List<DbLine> verSurfLinesFull1 = ArcGFunc.FindIntersectingLines(ArcGFunc.ArcLinesCopy(verOffsetLines), bottomOffsetLines);
                    //List<DbLine> verSurfLinesFull2 = ArcGFunc.FindIntersectingLines(verOffsetLines, topOffsetLines);
                    //List<DbLine> verSurfLinesFull = new List<DbLine>();
                    //verSurfLinesFull.AddRange(verSurfLinesFull1);
                    //verSurfLinesFull.AddRange(verSurfLinesFull2);
                    //GMath.LineMergeAll(verSurfLinesFull);
                    //verSurfLinesFull = ArcGFunc.CutOutLines(verSurfLinesFull);
                    //GMath.LineMergeAll(verSurfLinesFull);

                    //List<List<DbLine>> verSurfLines = ArcGFunc.FindAllClosedLoops2(verSurfLinesFull,0.01);

                    //List<DbLoop> verLoops = GMath.GetCwLoopsOfPls(verSurfLinesFull);
                    //List<DbRegion> verregion = new List<DbRegion>();
                    //foreach (DbLoop loop in verLoops)
                    //{
                    //    verregion.Add(new DbRegion(loop));
                    //}

                    //List<DbLine> outbotLines = new List<DbLine>();
                    //foreach (var reg in verregion)
                    //{
                    //    outbotLines= GMath.GetLinesOutRegion(bottomOffsetLines, reg);
                    //}
                    ////ArcGFunc.ReplaceOverlappingLines(bottomOffsetLines, verSurfLinesFull);

                    //GMath.LineMergeAll(outbotLines);

                    #endregion


                    GMath.LineMergeAll(bottomOffsetLines);
                    GMath.LineMergeAll(topOffsetLines);
                    GMath.LineMergeAll(verOffsetLines);
                    defaultSurfLines.AddRange(topOffsetLines);
                    defaultSurfLines.AddRange(verOffsetLines);
                    defaultSurfLines.AddRange(bottomOffsetLines);

                    verLinecount = verOffsetLines.Count;

                    List<DbLoop> coreLoops = GMath.GetCwLoopsOfPls(ArcGFunc.ArcLinesCopy(bottomLines));
                    arccolumnregion = new DbRegion(coreLoops);

                    List<DbLoop> surfLoops = GMath.GetCwLoopsOfPls(ArcGFunc.ArcLinesCopy(bottomOffsetLines));
                    cutregion = new DbRegion(surfLoops);

                    cutregion = GMath.GetSubtractRegion(cutregion, arccolumnregion);

                }
                SurfLines.AddRange(defaultSurfLines);
                //GMath.LineMergeAll(defaultSurfLines);
                if (sc >= 100 && _isshowsurface)
                {
                    Lines.AddRange(defaultSurfLines);
                }

                else if (sc >= 0 && sc < 100)
                {
                    Lines.AddRange(defaultSurfLines);
                    if (!_isshowsurface)
                    {
                        Lines = Lines.Except(defaultSurfLines).ToList();
                    }
                }
            }
        }

        /// <summary>
        /// 不同比例下面层显示情况，1:100及以上不显示，1:100以下显示
        /// </summary>
        public void ShowSurf()
        {
            if (Isshowsurface)  //如果选中强制显示，则在所有出图比例都显示
            {
                if (_columntype == CoTy.Rectangle)
                {
                    if (LinesRecorder.Count > 0)
                    {
                        for (var i = 4; i < LinesRecorder.Count; i++)
                        {
                            Lines.Add(LinesRecorder[i]);
                        }
                    }
                    if (CircleRecorder.Count > 0)
                    {
                        Circles.AddRange(CircleRecorder);
                    }
                }

                else if (_columntype == CoTy.Circle)
                {
                    if (LinesRecorder.Count > 0)
                    {
                        Lines.AddRange(LinesRecorder);
                    }
                    if (CircleRecorder.Count > 0)
                    {
                        Circles.AddRange(CircleRecorder);
                    }
                }

                else if (_columntype == CoTy.Irregular)
                {
                    if (LinesRecorder.Count > 0)
                    {
                        for (var i = secLoopLines.Count; i < LinesRecorder.Count; i++)
                        {
                            Lines.Add(LinesRecorder[i]);
                        }
                    }
                    if (CircleRecorder.Count > 0)
                    {
                        Circles.AddRange(CircleRecorder);
                    }
                }
            }

            else //如果没有选中强制显示，则根据视图比例来显示，默认执行
            {
                if (_columntype == CoTy.Rectangle)
                {
                    if (_view.ViewScale < 100 && _view.ViewScale >= 0)
                    {
                        if (LinesRecorder.Count > 0)
                        {
                            for (var i = 4; i < LinesRecorder.Count; i++)
                            {
                                Lines.Add(LinesRecorder[i]);
                            }
                        }
                        if (CircleRecorder.Count > 0)
                        {
                            Circles.AddRange(CircleRecorder);
                        }
                    }
                }

                else if (_columntype == CoTy.Circle)
                {
                    if (_view.ViewScale < 100 && _view.ViewScale >= 0)
                    {
                        if (LinesRecorder.Count > 0)
                        {

                            Lines.AddRange(LinesRecorder);
                        }
                        if (CircleRecorder.Count > 0)
                        {
                            Circles.AddRange(CircleRecorder);
                        }
                    }
                }

                else if (_columntype == CoTy.Irregular)
                {
                    if (_view.ViewScale < 100 && _view.ViewScale >= 0)
                    {
                        if (LinesRecorder.Count > 0)
                        {
                            for (var i = secLoopLines.Count; i < LinesRecorder.Count; i++)
                            {
                                Lines.Add(LinesRecorder[i]);
                            }
                        }
                        if (CircleRecorder.Count > 0)
                        {
                            Circles.AddRange(CircleRecorder);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 斜柱时，将
        /// </summary>
        /// <_param name="axis"></_param>
        /// <_param name="pt"></_param>
        /// <_param name="dCos"></_param>
        /// <returns></returns>
        private DbPt GetSkewColPt(DbLine axis, DbPt pt, double dCos)
        {
            DbPt mpt = pt;
            DbLine paraL = GMath.ParaLine(axis, mpt);
            DbPt opt = GMath.FootPt(paraL, ConPts[0]);
            if (!GMath.IfOverlap(opt, mpt))
            {
                double acDis = GMath.Distance(opt, mpt);
                mpt = GMath.GetPtOnLine(new DbLine(opt, mpt), true, acDis * dCos);
            }
            return mpt;
        }



        /// <summary>
        /// 将所有的线、圆、填充设置到对应的图层
        /// </summary>
        public void LayerSet2()
        {
            if (_columntype == CoTy.Rectangle)
            {
                //为矩形柱时，柱主体为4根线，Lines为4；，默认面层时Lines=8，编辑过面层后Lines的数量可能大于4，Circles可能大于1
                if (!_ifVer)
                {
                    if (Lines.Count >= 4)
                    {
                        for (int i = 0; i < 4; i++)
                        {
                            Lines[i].LayerId = _layerId;
                            Lines[i].WidthIndex = -1;
                            Lines[i].ColorIndex = -1;
                            Lines[i].StyleIndex = -1;
                        }
                    }

                    if (Lines.Count > 4) //当面层中有直线段时
                    {
                        for (int i = 4; i < Lines.Count; i++)
                        {
                            Lines[i].LayerId = PreLayerManage.GetLayerId("柱子-面层");
                            Lines[i].WidthIndex = -1;
                            Lines[i].ColorIndex = -1;
                            Lines[i].StyleIndex = -1;
                        }
                    }
                    if (Circles.Count > 0) //当面层中有圆时
                    {
                        for (int i = 0; i < Circles.Count; i++)
                        {
                            Circles[i].LayerId = PreLayerManage.GetLayerId("柱子-面层");
                            Circles[i].WidthIndex = -1;
                            Circles[i].ColorIndex = -1;
                            Circles[i].StyleIndex = -1;
                        }
                    }
                }
                else
                {
                    if (Lines.Count >= 10)
                    {
                        for (int i = 4; i < 8; i++)
                        {
                            Lines[i].LayerId = _layerId;
                            Lines[i].WidthIndex = -1;
                            Lines[i].ColorIndex = -1;
                            Lines[i].StyleIndex = -1;
                        }
                    }
                    if (Lines.Count > 10) //当面层中有直线段时
                    {
                        for (int i = Lines.Count - 4; i < Lines.Count; i++)
                        {
                            Lines[i].LayerId = PreLayerManage.GetLayerId("柱子-面层");
                            Lines[i].WidthIndex = -1;
                            Lines[i].ColorIndex = -1;
                            Lines[i].StyleIndex = -1;
                        }
                        for (int i = 10; i < Lines.Count - 4; i++)
                        {
                            Lines[i].LayerId = PreLayerManage.GetLayerId("柱子-面层");
                            Lines[i].WidthIndex = -1;
                            Lines[i].ColorIndex = 8;
                            Lines[i].StyleIndex = -1;
                        }
                    }
                    if (Circles.Count > 0) //当面层中有圆时
                    {
                        for (int i = 0; i < Circles.Count; i++)
                        {
                            Circles[i].LayerId = PreLayerManage.GetLayerId("柱子-面层");
                            Circles[i].WidthIndex = -1;
                            Circles[i].ColorIndex = -1;
                            Circles[i].StyleIndex = -1;
                        }
                    }
                }
            }

            else if (_columntype == CoTy.Circle)
            {
                if (!_ifVer)
                {
                    Circles[0].LayerId = _layerId;
                    if (Circles.Count > 1) //面层中有圆时
                    {
                        for (int i = 1; i < Circles.Count; i++)
                        {
                            Circles[i].LayerId = PreLayerManage.GetLayerId("柱子-面层");
                            Circles[i].ColorIndex = -1;
                            Circles[i].StyleIndex = -1;
                            Circles[i].WidthIndex = -1;
                        }
                    }
                    if (Lines.Count > 0)//面层中有直线段时
                    {
                        for (int i = 0; i < Lines.Count; i++)
                        {
                            Lines[i].LayerId = PreLayerManage.GetLayerId("柱子-面层");
                            Lines[i].ColorIndex = -1;
                            Lines[i].StyleIndex = -1;
                            Lines[i].WidthIndex = -1;
                        }
                    }
                }
                else
                {
                    Circles[1].LayerId = _layerId;
                    Circles[1].ColorIndex = -1;
                    Circles[1].WidthIndex = -1;
                    Circles[1].StyleIndex = -1;
                    if (Circles.Count > 2) //面层中有圆时
                    {
                        for (int i = 2; i < Circles.Count; i++)
                        {
                            Circles[i].LayerId = PreLayerManage.GetLayerId("柱子-面层");
                            Circles[i].ColorIndex = -1;
                            Circles[i].StyleIndex = -1;
                            Circles[i].WidthIndex = -1;
                        }
                    }
                    if (Lines.Count > 4)//面层中有直线段时
                    {
                        for (int i = 2; i < Lines.Count; i++)
                        {
                            Lines[i].LayerId = PreLayerManage.GetLayerId("柱子-面层");
                            Lines[i].ColorIndex = 8;
                            Lines[i].StyleIndex = -1;
                            Lines[i].WidthIndex = -1;
                        }
                    }
                }
            }

            else if (_columntype == CoTy.Irregular)
            {
                if (!_ifVer)
                {
                    for (int i = 0; i < secLoopLines.Count; i++)
                    {
                        Lines[i].LayerId = _layerId;
                        Lines[i].ColorIndex = -1;
                        Lines[i].StyleIndex = -1;
                    }

                    for (int i = secLoopLines.Count; i < Lines.Count; i++)
                    {
                        Lines[i].LayerId = PreLayerManage.GetLayerId("柱子-面层");
                        Lines[i].ColorIndex = -1;
                        Lines[i].StyleIndex = -1;
                    }
                    foreach (var circle in Circles)
                    {
                        circle.LayerId = PreLayerManage.GetLayerId("柱子-面层");
                        circle.ColorIndex = -1;
                        circle.StyleIndex = -1;
                    }
                }
                else
                {
                    for (int i = secLoopLines.Count; i < secLoopLines.Count * 2; i++)
                    {
                        Lines[i].LayerId = _layerId;
                    }
                    if (_isshowsurface)
                    {
                        for (int i = secLoopLines.Count * 2 + verLinecount; i < secLoopLines.Count * 3 + verLinecount; i++)
                        {
                            Lines[i].LayerId = PreLayerManage.GetLayerId("柱子-面层");
                            Lines[i].ColorIndex = -1;
                            Lines[i].StyleIndex = -1;
                        }
                        for (int i = secLoopLines.Count * 3 + verLinecount; i < Lines.Count; i++)
                        {
                            Lines[i].LayerId = PreLayerManage.GetLayerId("柱子-面层");
                            Lines[i].ColorIndex = 8;
                            Lines[i].StyleIndex = -1;
                        }
                    }

                }

            }

            if (Hatchs.Count > 0)
            {
                for (int i = 0; i < Hatchs.Count; i++)
                {
                    Hatchs[i].LayerId = PreLayerManage.GetLayerId("通用-填充");
                }
            }
        }


        /// <summary>
        /// 重写数据保存
        /// </summary>
        /// <_param name="binaryWriter"></_param>
        public override void DataSave(BinaryWriter binaryWriter)
        {
            //图元类标识
            binaryWriter.Write(GetType().ToString());

            //版本号
            binaryWriter.Write(5);

            //图元共有参数
            PubSave(binaryWriter);

            //图元特有参数

            binaryWriter.Write(_skipCalculatePassLinesInEleCopy);
            binaryWriter.Write(_ifedited);


            binaryWriter.Write(_ifboundary);
            binaryWriter.Write(_isshowsurface);
            binaryWriter.Write(_length);
            binaryWriter.Write(_sectionarea);
            binaryWriter.Write(_volume);
            binaryWriter.Write(_surfthick);
            binaryWriter.Write(_surfheight);
            binaryWriter.Write(_leftRec);
            binaryWriter.Write(_rightRec);
            binaryWriter.Write(_topRec);
            binaryWriter.Write(_bottomRec);
            binaryWriter.Write(_leftCir);
            binaryWriter.Write(_rightCir);
            binaryWriter.Write(_topCir);
            binaryWriter.Write(_bottomCir);
            binaryWriter.Write(_transverseWith);
            binaryWriter.Write(_verticalwidth);
            binaryWriter.Write(_topLevel);
            binaryWriter.Write(_bottomLevel);
            binaryWriter.Write(_topOffset);
            binaryWriter.Write(_bottomOffset);
            binaryWriter.Write(_sectiontype);
            binaryWriter.Write(_hoAngle);
            binaryWriter.Write(_ifVer);
            binaryWriter.Write(_veAngle);
            binaryWriter.Write((int)_topStyle);
            binaryWriter.Write(_topAngle);
            binaryWriter.Write((int)_bottomStyle);
            binaryWriter.Write(_bottomAngle);
            binaryWriter.Write((int)_columntype);
            binaryWriter.Write(_radius);
            binaryWriter.Write(_doubleradius);
            int surcirnum = SurCircle.Count;
            binaryWriter.Write(surcirnum);
            for (int i = 0; i < surcirnum; i++)
            {
                SurCircle[i].DataSave(binaryWriter);
            }

            int surlinenum = SurfLines.Count;
            binaryWriter.Write(surlinenum);
            for (int i = 0; i < surlinenum; i++)
            {
                SurfLines[i].DataSave(binaryWriter);
            }
            _ptcenter.DataSave(binaryWriter);
            _surftex.DataSave(binaryWriter);
            arccolumnregion.DataSave(binaryWriter);
            cutregion.DataSave(binaryWriter);
            _coreMateria.DataSave(binaryWriter);
            _surfaceMateria.DataSave(binaryWriter);
            binaryWriter.Write(secTypeID);
            _ptcenter.DataSave(binaryWriter);
            _insertpt.DataSave(binaryWriter);

            int secLoopLinesNum = secLoopLines.Count;
            binaryWriter.Write(secLoopLinesNum);
            for (int i = 0; i < secLoopLinesNum; i++)
            {
                secLoopLines[i].DataSave(binaryWriter);
            }
        }

        /// <summary>
        /// 重写数据加载
        /// </summary>
        /// <_param name="binaryReader"></_param>
        public override void DataLoad(BinaryReader binaryReader)
        {
            int VerNum = binaryReader.ReadInt32();//读取版本号
            if (VerNum == 0)//判断版本号
            {
                PubLoad(binaryReader);
                _skipCalculatePassLinesInEleCopy = binaryReader.ReadBoolean();
                _ifedited = binaryReader.ReadBoolean();
                _ifboundary = binaryReader.ReadBoolean();
                _isshowsurface = binaryReader.ReadBoolean();
                _length = binaryReader.ReadDouble();
                _sectionarea = binaryReader.ReadString();
                _volume = binaryReader.ReadString();
                _surfthick = binaryReader.ReadDouble();
                _surfheight = binaryReader.ReadDouble();
                _leftRec = binaryReader.ReadDouble();
                _rightRec = binaryReader.ReadDouble();
                _topRec = binaryReader.ReadDouble();
                _bottomRec = binaryReader.ReadDouble();
                _leftCir = binaryReader.ReadDouble();
                _rightCir = binaryReader.ReadDouble();
                _topCir = binaryReader.ReadDouble();
                _bottomCir = binaryReader.ReadDouble();
                _transverseWith = binaryReader.ReadDouble();
                _verticalwidth = binaryReader.ReadDouble();
                _topLevel = binaryReader.ReadDouble();
                _bottomLevel = binaryReader.ReadString();
                _topOffset = binaryReader.ReadDouble();
                _bottomOffset = binaryReader.ReadDouble();
                _sectiontype = binaryReader.ReadString();
                _hoAngle = binaryReader.ReadDouble();
                _ifVer = binaryReader.ReadBoolean();
                _veAngle = binaryReader.ReadDouble();
                _topStyle = (TopTiltAng)binaryReader.ReadInt32();
                _topAngle = binaryReader.ReadDouble();
                _bottomStyle = (TopTiltAng)binaryReader.ReadInt32();
                _bottomAngle = binaryReader.ReadDouble();
                _columntype = (CoTy)binaryReader.ReadInt32();
                _radius = binaryReader.ReadDouble();
                _doubleradius = binaryReader.ReadDouble();
                int surcirnum = binaryReader.ReadInt32();
                for (int i = 0; i < surcirnum; i++)
                {
                    SurCircle.Add(new DbCircle(binaryReader));
                }

                int surlinenum = binaryReader.ReadInt32();
                for (int i = 0; i < surlinenum; i++)
                {
                    SurfLines.Add(new DbLine(binaryReader));
                }

                _ptcenter = new DbPt(binaryReader);
                _surftex = new TexProperty(binaryReader);
                arccolumnregion = new DbRegion(binaryReader);
            }
            else if (VerNum == 1)//判断版本号
            {
                PubLoad(binaryReader);
                _skipCalculatePassLinesInEleCopy = binaryReader.ReadBoolean();
                _ifedited = binaryReader.ReadBoolean();
                _ifboundary = binaryReader.ReadBoolean();
                _isshowsurface = binaryReader.ReadBoolean();
                _length = binaryReader.ReadDouble();
                _sectionarea = binaryReader.ReadString();
                _volume = binaryReader.ReadString();
                _surfthick = binaryReader.ReadDouble();
                _surfheight = binaryReader.ReadDouble();
                _leftRec = binaryReader.ReadDouble();
                _rightRec = binaryReader.ReadDouble();
                _topRec = binaryReader.ReadDouble();
                _bottomRec = binaryReader.ReadDouble();
                _leftCir = binaryReader.ReadDouble();
                _rightCir = binaryReader.ReadDouble();
                _topCir = binaryReader.ReadDouble();
                _bottomCir = binaryReader.ReadDouble();
                _transverseWith = binaryReader.ReadDouble();
                _verticalwidth = binaryReader.ReadDouble();
                _topLevel = binaryReader.ReadDouble();
                _bottomLevel = binaryReader.ReadString();
                _topOffset = binaryReader.ReadDouble();
                _bottomOffset = binaryReader.ReadDouble();
                _sectiontype = binaryReader.ReadString();
                _hoAngle = binaryReader.ReadDouble();
                _ifVer = binaryReader.ReadBoolean();
                _veAngle = binaryReader.ReadDouble();
                _topStyle = (TopTiltAng)binaryReader.ReadInt32();
                _topAngle = binaryReader.ReadDouble();
                _bottomStyle = (TopTiltAng)binaryReader.ReadInt32();
                _bottomAngle = binaryReader.ReadDouble();
                _columntype = (CoTy)binaryReader.ReadInt32();
                _radius = binaryReader.ReadDouble();
                _doubleradius = binaryReader.ReadDouble();
                int surcirnum = binaryReader.ReadInt32();
                for (int i = 0; i < surcirnum; i++)
                {
                    SurCircle.Add(new DbCircle(binaryReader));
                }

                int surlinenum = binaryReader.ReadInt32();
                for (int i = 0; i < surlinenum; i++)
                {
                    SurfLines.Add(new DbLine(binaryReader));
                }

                _ptcenter = new DbPt(binaryReader);
                _surftex = new TexProperty(binaryReader);
                arccolumnregion = new DbRegion(binaryReader);
                _coreMateria = new DbMaterial(binaryReader);
                _surfaceMateria = new DbMaterial(binaryReader);
            }
            else if (VerNum == 2)//判断版本号
            {
                PubLoad(binaryReader);
                _skipCalculatePassLinesInEleCopy = binaryReader.ReadBoolean();
                _ifedited = binaryReader.ReadBoolean();
                _ifboundary = binaryReader.ReadBoolean();
                _isshowsurface = binaryReader.ReadBoolean();
                _length = binaryReader.ReadDouble();
                _sectionarea = binaryReader.ReadString();
                _volume = binaryReader.ReadString();
                _surfthick = binaryReader.ReadDouble();
                _surfheight = binaryReader.ReadDouble();
                _leftRec = binaryReader.ReadDouble();
                _rightRec = binaryReader.ReadDouble();
                _topRec = binaryReader.ReadDouble();
                _bottomRec = binaryReader.ReadDouble();
                _leftCir = binaryReader.ReadDouble();
                _rightCir = binaryReader.ReadDouble();
                _topCir = binaryReader.ReadDouble();
                _bottomCir = binaryReader.ReadDouble();
                _transverseWith = binaryReader.ReadDouble();
                _verticalwidth = binaryReader.ReadDouble();
                _topLevel = binaryReader.ReadDouble();
                _bottomLevel = binaryReader.ReadString();
                _topOffset = binaryReader.ReadDouble();
                _bottomOffset = binaryReader.ReadDouble();
                _sectiontype = binaryReader.ReadString();
                _hoAngle = binaryReader.ReadDouble();
                _ifVer = binaryReader.ReadBoolean();
                _veAngle = binaryReader.ReadDouble();
                _topStyle = (TopTiltAng)binaryReader.ReadInt32();
                _topAngle = binaryReader.ReadDouble();
                _bottomStyle = (TopTiltAng)binaryReader.ReadInt32();
                _bottomAngle = binaryReader.ReadDouble();
                _columntype = (CoTy)binaryReader.ReadInt32();
                _radius = binaryReader.ReadDouble();
                _doubleradius = binaryReader.ReadDouble();
                int surcirnum = binaryReader.ReadInt32();
                for (int i = 0; i < surcirnum; i++)
                {
                    SurCircle.Add(new DbCircle(binaryReader));
                }

                int surlinenum = binaryReader.ReadInt32();
                for (int i = 0; i < surlinenum; i++)
                {
                    SurfLines.Add(new DbLine(binaryReader));
                }

                _ptcenter = new DbPt(binaryReader);
                _surftex = new TexProperty(binaryReader);
                arccolumnregion = new DbRegion(binaryReader);
                cutregion = new DbRegion(binaryReader);
                _coreMateria = new DbMaterial(binaryReader);
                _surfaceMateria = new DbMaterial(binaryReader);
            }
            else if (VerNum == 3)//判断版本号
            {
                PubLoad(binaryReader);
                _skipCalculatePassLinesInEleCopy = binaryReader.ReadBoolean();
                _ifedited = binaryReader.ReadBoolean();
                _ifboundary = binaryReader.ReadBoolean();
                _isshowsurface = binaryReader.ReadBoolean();
                _length = binaryReader.ReadDouble();
                _sectionarea = binaryReader.ReadString();
                _volume = binaryReader.ReadString();
                _surfthick = binaryReader.ReadDouble();
                _surfheight = binaryReader.ReadDouble();
                _leftRec = binaryReader.ReadDouble();
                _rightRec = binaryReader.ReadDouble();
                _topRec = binaryReader.ReadDouble();
                _bottomRec = binaryReader.ReadDouble();
                _leftCir = binaryReader.ReadDouble();
                _rightCir = binaryReader.ReadDouble();
                _topCir = binaryReader.ReadDouble();
                _bottomCir = binaryReader.ReadDouble();
                _transverseWith = binaryReader.ReadDouble();
                _verticalwidth = binaryReader.ReadDouble();
                _topLevel = binaryReader.ReadDouble();
                _bottomLevel = binaryReader.ReadString();
                _topOffset = binaryReader.ReadDouble();
                _bottomOffset = binaryReader.ReadDouble();
                _sectiontype = binaryReader.ReadString();
                _hoAngle = binaryReader.ReadDouble();
                _ifVer = binaryReader.ReadBoolean();
                _veAngle = binaryReader.ReadDouble();
                _topStyle = (TopTiltAng)binaryReader.ReadInt32();
                _topAngle = binaryReader.ReadDouble();
                _bottomStyle = (TopTiltAng)binaryReader.ReadInt32();
                _bottomAngle = binaryReader.ReadDouble();
                _columntype = (CoTy)binaryReader.ReadInt32();
                _radius = binaryReader.ReadDouble();
                _doubleradius = binaryReader.ReadDouble();
                int surcirnum = binaryReader.ReadInt32();
                for (int i = 0; i < surcirnum; i++)
                {
                    SurCircle.Add(new DbCircle(binaryReader));
                }

                int surlinenum = binaryReader.ReadInt32();
                for (int i = 0; i < surlinenum; i++)
                {
                    SurfLines.Add(new DbLine(binaryReader));
                }

                _ptcenter = new DbPt(binaryReader);
                _surftex = new TexProperty(binaryReader);
                arccolumnregion = new DbRegion(binaryReader);
                cutregion = new DbRegion(binaryReader);
                _coreMateria = new DbMaterial(binaryReader);
                _surfaceMateria = new DbMaterial(binaryReader);
                secTypeID = binaryReader.ReadString();
            }
            else if (VerNum == 4)//判断版本号
            {
                PubLoad(binaryReader);
                _skipCalculatePassLinesInEleCopy = binaryReader.ReadBoolean();
                _ifedited = binaryReader.ReadBoolean();
                _ifboundary = binaryReader.ReadBoolean();
                _isshowsurface = binaryReader.ReadBoolean();
                _length = binaryReader.ReadDouble();
                _sectionarea = binaryReader.ReadString();
                _volume = binaryReader.ReadString();
                _surfthick = binaryReader.ReadDouble();
                _surfheight = binaryReader.ReadDouble();
                _leftRec = binaryReader.ReadDouble();
                _rightRec = binaryReader.ReadDouble();
                _topRec = binaryReader.ReadDouble();
                _bottomRec = binaryReader.ReadDouble();
                _leftCir = binaryReader.ReadDouble();
                _rightCir = binaryReader.ReadDouble();
                _topCir = binaryReader.ReadDouble();
                _bottomCir = binaryReader.ReadDouble();
                _transverseWith = binaryReader.ReadDouble();
                _verticalwidth = binaryReader.ReadDouble();
                _topLevel = binaryReader.ReadDouble();
                _bottomLevel = binaryReader.ReadString();
                _topOffset = binaryReader.ReadDouble();
                _bottomOffset = binaryReader.ReadDouble();
                _sectiontype = binaryReader.ReadString();
                _hoAngle = binaryReader.ReadDouble();
                _ifVer = binaryReader.ReadBoolean();
                _veAngle = binaryReader.ReadDouble();
                _topStyle = (TopTiltAng)binaryReader.ReadInt32();
                _topAngle = binaryReader.ReadDouble();
                _bottomStyle = (TopTiltAng)binaryReader.ReadInt32();
                _bottomAngle = binaryReader.ReadDouble();
                _columntype = (CoTy)binaryReader.ReadInt32();
                _radius = binaryReader.ReadDouble();
                _doubleradius = binaryReader.ReadDouble();
                int surcirnum = binaryReader.ReadInt32();
                for (int i = 0; i < surcirnum; i++)
                {
                    SurCircle.Add(new DbCircle(binaryReader));
                }

                int surlinenum = binaryReader.ReadInt32();
                for (int i = 0; i < surlinenum; i++)
                {
                    SurfLines.Add(new DbLine(binaryReader));
                }

                _ptcenter = new DbPt(binaryReader);
                _surftex = new TexProperty(binaryReader);
                arccolumnregion = new DbRegion(binaryReader);
                cutregion = new DbRegion(binaryReader);
                _coreMateria = new DbMaterial(binaryReader);
                _surfaceMateria = new DbMaterial(binaryReader);
                secTypeID = binaryReader.ReadString();
                _ptcenter = new DbPt(binaryReader);
                _insertpt = new DbPt(binaryReader);
            }
            else if (VerNum == 5)//判断版本号
            {
                PubLoad(binaryReader);
                _skipCalculatePassLinesInEleCopy = binaryReader.ReadBoolean();
                _ifedited = binaryReader.ReadBoolean();
                _ifboundary = binaryReader.ReadBoolean();
                _isshowsurface = binaryReader.ReadBoolean();
                _length = binaryReader.ReadDouble();
                _sectionarea = binaryReader.ReadString();
                _volume = binaryReader.ReadString();
                _surfthick = binaryReader.ReadDouble();
                _surfheight = binaryReader.ReadDouble();
                _leftRec = binaryReader.ReadDouble();
                _rightRec = binaryReader.ReadDouble();
                _topRec = binaryReader.ReadDouble();
                _bottomRec = binaryReader.ReadDouble();
                _leftCir = binaryReader.ReadDouble();
                _rightCir = binaryReader.ReadDouble();
                _topCir = binaryReader.ReadDouble();
                _bottomCir = binaryReader.ReadDouble();
                _transverseWith = binaryReader.ReadDouble();
                _verticalwidth = binaryReader.ReadDouble();
                _topLevel = binaryReader.ReadDouble();
                _bottomLevel = binaryReader.ReadString();
                _topOffset = binaryReader.ReadDouble();
                _bottomOffset = binaryReader.ReadDouble();
                _sectiontype = binaryReader.ReadString();
                _hoAngle = binaryReader.ReadDouble();
                _ifVer = binaryReader.ReadBoolean();
                _veAngle = binaryReader.ReadDouble();
                _topStyle = (TopTiltAng)binaryReader.ReadInt32();
                _topAngle = binaryReader.ReadDouble();
                _bottomStyle = (TopTiltAng)binaryReader.ReadInt32();
                _bottomAngle = binaryReader.ReadDouble();
                _columntype = (CoTy)binaryReader.ReadInt32();
                _radius = binaryReader.ReadDouble();
                _doubleradius = binaryReader.ReadDouble();
                int surcirnum = binaryReader.ReadInt32();
                for (int i = 0; i < surcirnum; i++)
                {
                    SurCircle.Add(new DbCircle(binaryReader));
                }

                int surlinenum = binaryReader.ReadInt32();
                for (int i = 0; i < surlinenum; i++)
                {
                    SurfLines.Add(new DbLine(binaryReader));
                }

                _ptcenter = new DbPt(binaryReader);
                _surftex = new TexProperty(binaryReader);
                arccolumnregion = new DbRegion(binaryReader);
                cutregion = new DbRegion(binaryReader);
                _coreMateria = new DbMaterial(binaryReader);
                _surfaceMateria = new DbMaterial(binaryReader);
                secTypeID = binaryReader.ReadString();
                _ptcenter = new DbPt(binaryReader);
                _insertpt = new DbPt(binaryReader);
                int secLoopLinesNum = binaryReader.ReadInt32();
                for (int i = 0; i < secLoopLinesNum; i++)
                {
                    secLoopLines.Add(new DbLine(binaryReader));
                }
            }
        }

        /// <summary>
        /// 操作控制点过后重新计算矩形柱全局柱截面线
        /// </summary>
        private void RecalRecColLines()
        {
            var tempoutlines = new List<DbLine>();
            for (int i = 4; i < Lines.Count; i++)
            {
                //Lines[i].ColorIndex = 0;
                tempoutlines.Add(Lines[i]);
            }
            List<DbLine> inneeLines = new List<DbLine>();
            DbLine linetop = new DbLine(ConPts[5], ConPts[6]);
            DbLine lineright = new DbLine(ConPts[6], ConPts[7]);
            DbLine linebottom = new DbLine(ConPts[7], ConPts[8]);
            DbLine lineleft = new DbLine(ConPts[8], ConPts[5]);
            inneeLines.Add(linetop);
            inneeLines.Add(lineright);
            inneeLines.Add(linebottom);
            inneeLines.Add(lineleft);


            LinesRecorder.Clear();
            LinesRecorder.AddRange(inneeLines);
            LinesRecorder.AddRange(tempoutlines);
            tempoutlines.Clear();
        }

        /// <summary>
        /// 重写拉伸，写控制点移动行为
        /// </summary>
        /// <_param name="X"></_param>
        /// <_param name="Y"></_param>
        /// <summary>
        /// 控制点选择移动方法 - 处理用户拖拽控制点时的图元变形逻辑
        /// </summary>
        /// <param name="X">X方向移动距离</param>
        /// <param name="Y">Y方向移动距离</param>
        public override void EleMove_s(double X, double Y)
        {
            // 处理矩形柱的控制点移动逻辑
            if (_columntype == CoTy.Rectangle)
            {
                // 统计当前选中的控制点数量，用于判断移动模式
                int count = ConPts.Count(pt => pt.Status == 1);
                
                if (count == 9)
                {
                    // 如果选中了所有控制点，执行整体移动而非变形
                    EleMove(X, Y);
                }
                else if (ConPts[1].Status == 1)
                {
                    // 处理选中第一个控制点（ConPts[1] - 纵向边界点）的拖拽变形
                    
                    // 1. 保存原始方向向量，用于后续方向判断
                    DbPt vec_o = new DbPt(ConPts[1].X - ConPts[3].X, ConPts[1].Y - ConPts[3].Y);
                    
                    // 2. 移动控制点到新位置
                    ConPts[1].MoveSelf(X, Y);
                    
                    // 3. 计算移动后的方向向量
                    DbPt vec_n = new DbPt(ConPts[1].X - ConPts[3].X, ConPts[1].Y - ConPts[3].Y);
                    
                    // 4. 将控制点约束到垂直于中心轴的位置（保持矩形特性）
                    ConPts[1] = GMath.FootPt(ConPts[3], ConPts[0], ConPts[1]);
                    
                    // 5. 计算最终的方向向量
                    DbPt vec = new DbPt(ConPts[1].X - ConPts[3].X, ConPts[1].Y - ConPts[3].Y);
                    
                    // 6. 更新中心点位置（始终保持在两个主控制点的中点）
                    ConPts[0] = new DbPt((ConPts[1].X + ConPts[3].X) / 2, (ConPts[1].Y + ConPts[3].Y) / 2);
                    
                    // 7. 通过点积判断拖拽方向，确定法向量计算方向
                    double res = DbPt.Dot2D(vec_o, vec_n);
                    DbPt normalvec_L; // 左侧法向量
                    DbPt normalvec_R; // 右侧法向量
                    
                    if (res > 0)
                    {
                        // 正方向拖拽：保持原有的左右法向量定义
                        normalvec_L = GMath.CalNormDirPt(vec, true);  // 计算左法向量
                        normalvec_R = GMath.CalNormDirPt(vec, false); // 计算右法向量
                    }
                    else
                    {
                        // 反方向拖拽：交换左右法向量定义，保持几何一致性
                        normalvec_L = GMath.CalNormDirPt(vec, false);
                        normalvec_R = GMath.CalNormDirPt(vec, true);
                    }
                    
                    // 8. 基于法向量和横向宽度更新横向控制点
                    ConPts[5] = ConPts[1].Copy().Move(normalvec_L, _transverseWith / 2); // 左侧控制点
                    ConPts[6] = ConPts[1].Copy().Move(normalvec_R, _transverseWith / 2); // 右侧控制点
                    
                    // 9. 更新边界中点控制点（用于显示和编辑）
                    ConPts[4] = new DbPt((ConPts[5].X + ConPts[8].X) / 2, (ConPts[5].Y + ConPts[8].Y) / 2); // 左边中点
                    ConPts[2] = new DbPt((ConPts[6].X + ConPts[7].X) / 2, (ConPts[6].Y + ConPts[7].Y) / 2); // 右边中点
                    
                    // 10. 更新纵向宽度属性，保持数据一致性
                    _verticalwidth = (int)GMath.Distance(ConPts[1], ConPts[3]);
                }
                // 其他控制点的处理逻辑类似，省略部分注释...
                else if (ConPts[0].Status == 1)
                {
                    // 如果选中中心点（ConPts[0]），直接调用普通移动方法
                    EleMove(X, Y);
                }

                // 如果柱子被编辑过，重新计算柱子轮廓线
                if (_ifedited)
                {
                    RecalRecColLines();
                }
                // 如果是斜柱，计算倾斜角度
                if (_ifVer)
                {
                    if (ConPts[9].Status == 1)
                    {
                        ConPts[9].MoveSelf(X, Y);
                        DbLine refLine = new DbLine(ConPts[2], ConPts[4]);
                        ConPts[9] = GMath.FootPt(refLine, ConPts[9]);
                        double x = Math.Abs(ConPts[9].X - ConPts[0].X);
                        double y = Math.Abs(ConPts[9].Y - ConPts[0].Y);
                        double height = _view.TopLevel + _topOffset - (_view.BotLevel + _bottomOffset);
                        double verLen = Math.Sqrt(Math.Pow(x, 2) + Math.Pow(y, 2));
                        double tan = Math.Atan(verLen / height);
                        _veAngle = GMath.RadiansToAngle(tan);
                        _veAngle = 90 - VeAngle;
                    }
                }
            }
            // 处理圆形柱的移动逻辑
            else if (_columntype == CoTy.Circle)
            {
                if (ConPts[0].Status == 1)
                {
                    // 如果选中中心点（ConPts[0]），直接调用普通移动方法
                    EleMove(X, Y);
                }
                else if (ConPts[1].Status == 1 || ConPts[2].Status == 1 || ConPts[3].Status == 1 || ConPts[4].Status == 1)
                {
                    // 处理选中圆形柱边缘控制点的情况
                    if (ConPts[1].Status == 1)
                    {
                        ConPts[1].MoveSelf(X, Y);
                        _radius = (int)DbPt.Length2D(ConPts[0], ConPts[1]); // 更新半径
                        if (_ifedited)
                        {
                            // 更新圆形柱的几何数据
                            DbCircle cirtem = new DbCircle(ConPts[0], 2 * _radius)
                            {
                                ColorIndex = 2,
                            };
                            CircleRecorder[0] = cirtem;
                            ConPts[2] = ConPts[0].Move(new DbPt(1, 0), _radius);
                            ConPts[3] = ConPts[0].Move(new DbPt(0, -1), _radius);
                            ConPts[4] = ConPts[0].Move(new DbPt(-1, 0), _radius);
                        }
                    }
                    // 其他边缘控制点的处理逻辑类似，省略部分注释...
                }
                // 如果是斜柱，计算倾斜角度
                if (_ifVer)
                {
                    if (ConPts[5].Status == 1)
                    {
                        ConPts[5].MoveSelf(X, Y);
                        DbLine refLine = new DbLine(ConPts[2], ConPts[4]);
                        ConPts[5] = GMath.FootPt(refLine, ConPts[5]);
                        double x = Math.Abs(ConPts[5].X - ConPts[0].X);
                        double y = Math.Abs(ConPts[5].Y - ConPts[0].Y);
                        double height = _view.TopLevel + _topOffset - (_view.BotLevel + _bottomOffset);
                        double verLen = Math.Sqrt(Math.Pow(x, 2) + Math.Pow(y, 2));
                        double tan = Math.Atan(verLen / height);
                        _veAngle = GMath.RadiansToAngle(tan);
                        _veAngle = 90 - VeAngle;
                    }
                }
            }
            // 处理不规则柱的移动逻辑
            else if (_columntype == CoTy.Irregular)
            {
                // 直接调用普通移动方法
                EleMove(X, Y);
            }

            // 重置标志位并激活柱子
            _skipCalculatePassLinesInEleCopy = false;
            _skipCalcuPtcenter = false;
            Activate();
        }
        /// <summary>
        /// 名称
        /// </summary>
        /// <_param name="str1"></_param>
        /// <_param name="str2"></_param>
        public override void EleTips(out string str1, out string str2)
        {
            str1 = "建筑柱";
            str2 = null;
        }

        /// <summary>
        /// 寻找梁图元，用于柱裁剪
        /// </summary>
        /// <_param name="cutReleEles"></_param>
        /// <_param name="oldArea"></_param>
        /// <_param name="view"></_param>
        public override void FindCutReleEle(List<DbElement> cutReleEles, DbEleArea oldArea, View view = null)
        {
            Floor floor = view?.Floor; //获取所有楼层
            var floorWalls = view.GetElements(new ArchWallFilter()); //通过墙过滤器获取所有墙
            var floorBeams = view.GetElements(new ArcBeam_filter()); //通过墙过滤器获取所有梁
            var floorFloorHoles = view.GetElements(new FloorHole_Filter()); //通过墙过滤器获取所有梁
            var floorRooms = view.GetElements(new ArchRoom_Filter()); //通过墙过滤器获取所有房间

            List<DbElement> releWalls = new List<DbElement>();//被排除的墙的集合
            for (int i = 0; i < floorWalls.Count; i++)
            {
                var wall = floorWalls[i] as ArchWall;
                if (this.EleArea.IfCross(wall.EleArea))
                {
                    //cutReleEles.Add(wall);
                    bool isAnyPointInOrOnRegion = ConPts.Any(pt => GMath.IfPtInRegion(pt, wall.OriReg[ArchWallRegType.Middle]) || GMath.IfPtOnRegion(pt, wall.OriReg[ArchWallRegType.Middle]));
                    if (isAnyPointInOrOnRegion)
                    {
                        cutReleEles.Add(wall);
                    }
                }
                if (oldArea.IfCross(wall.EleArea))
                {
                    if (!releWalls.Contains_uid(wall) && wall.UniqueId != this.UniqueId)
                    {
                        cutReleEles.Add(wall);
                    }
                }
            }
            //cutReleEles.AddRange(releWalls);

            List<DbElement> releBeams = new List<DbElement>();//被排除的梁的集合
            for (int i = 0; i < floorBeams.Count; i++)
            {
                var beam = floorBeams[i] as ArcBeam;
                if (this.EleArea.IfCross(beam.EleArea))
                {
                    cutReleEles.Add(beam);
                }
                if (oldArea.IfCross(beam.EleArea))
                {
                    if (!releBeams.Contains_uid(beam) && beam.UniqueId != this.UniqueId)
                    {
                        releWalls.Add(beam);
                    }
                }
            }

            List<DbElement> releFloorHoles = new List<DbElement>();
            for (int i = 0; i < floorFloorHoles.Count; i++)
            {
                var floorHole = floorFloorHoles[i] as FloorHoleSpare;
                if (this.EleArea.IfCross(floorHole.EleArea))
                {
                    cutReleEles.Add(floorHole);
                }
                if (oldArea.IfCross(floorHole.EleArea))
                {
                    if (!releBeams.Contains_uid(floorHole) && floorHole.UniqueId != this.UniqueId)
                    {
                        releFloorHoles.Add(floorHole);
                    }
                }
            }
        }

        /// <summary>
        /// 重写移动
        /// </summary>
        /// <_param name="X"></_param>
        /// <_param name="Y"></_param>
        public override void EleMove(double X, double Y)
        {
            _skipCalculatePassLinesInEleCopy = false;
            _skipCalcuPtcenter = false;
            foreach (DbPt pt in ConPts) { pt.MoveSelf(X, Y); }

            if (!_ifedited)
            {
                if (_columntype == CoTy.Irregular)
                {
                    foreach (var line in secLoopLines)
                    {
                        line.MoveSelf(X, Y);
                    }
                }
            }

            else
            {
                if (_columntype == CoTy.Rectangle)
                {
                    for (int i = 4; i < LinesRecorder.Count; i++)
                    {
                        LinesRecorder[i].MoveSelf(X, Y);
                    }
                    foreach (var item in CircleRecorder)
                    {
                        item.MoveSelf(X, Y);
                    }
                }

                if (_columntype == CoTy.Circle)
                {
                    CircleRecorder[0] = new DbCircle(ConPts[0], 2 * _radius);
                    CircleRecorder[0].ColorIndex = 2;
                    if (CircleRecorder.Count > 1)
                    {
                        CircleRecorder[1].MoveSelf(X, Y);
                    }
                    if (LinesRecorder.Count > 0)
                    {
                        foreach (var item in LinesRecorder)
                        {
                            item.MoveSelf(X, Y);
                        }
                    }
                }
                if (_columntype == CoTy.Irregular)
                {
                    foreach (var line in secLoopLines)
                    {
                        line.MoveSelf(X, Y);
                    }
                    foreach (var line in LinesRecorder)
                    {
                        line.MoveSelf(X, Y);
                    }
                    foreach (var line in CircleRecorder)
                    {
                        line.MoveSelf(X, Y);
                    }
                }
            }
            Activate();
        }

        /// <summary>
        /// 重写镜像
        /// </summary>
        /// <_param name="line"></_param>
        /// <summary>
        /// 图元镜像操作
        /// 对建筑柱图元进行镜像变换，包括控制点、几何形状和编辑数据的镜像处理
        /// </summary>
        /// <param name="line">镜像轴线，图元将相对于此直线进行镜像</param>
        public override void EleMove_m(DbLine line)
        {
            // 1. 重置计算标志位，确保镜像后重新计算几何数据
            _skipCalculatePassLinesInEleCopy = false;  // 允许重新计算穿越线数据
            _skipCalcuPtcenter = false;                // 允许重新计算中心点坐标
            _ifmirror = true;                          // 标记图元为镜像状态

            // 2. 镜像所有控制点
            // ConPts包含图元的基本控制点，如插入点、边界点等
            foreach (DbPt pt in ConPts) 
            { 
                pt.MirrorSelf(line);  // 每个控制点相对于镜像轴进行镜像变换
            }

            // 3. 处理异形柱的特殊几何数据
            // 异形柱具有自定义的截面轮廓线，需要单独镜像处理
            if (_columntype == CoTy.Irregular)
            {
                // 镜像异形柱的截面轮廓线集合
                // secLoopLines存储了异形柱的原始截面几何定义
                foreach (var loopline in secLoopLines)
                {
                    loopline.MirrorSelf(line);  // 对每条轮廓线进行镜像变换
                }
            }

            // 4. 处理用户编辑后的几何数据
            // 当图元被用户编辑过时，需要镜像编辑产生的附加几何元素
            if (_ifedited)
            {
                // 镜像编辑过程中添加的圆弧几何
                // CircleRecorder记录了用户编辑时添加的圆弧元素
                foreach (var item in CircleRecorder)
                {
                    item.MirrorSelf(line);  // 对每个圆弧进行镜像变换
                }

                // 镜像编辑过程中添加的直线几何
                // LinesRecorder记录了用户编辑时添加的直线元素
                foreach (var item in LinesRecorder)
                {
                    item.MirrorSelf(line);  // 对每条直线进行镜像变换
                }
            }

            // 5. 保存镜像轴线的副本
            // 用于后续可能的镜像状态查询或撤销操作
            mirrorLine = line.EleCopy();

            // 6. 激活图元，触发几何重新计算和显示更新
            // 镜像操作完成后，需要重新计算图元的几何数据和显示状态
            Activate();
        }
        /// <summary>
        /// 复制
        /// </summary>
        /// <_param name="changeUid"></_param>
        /// <returns></returns>
        public override DbElement EleCopy(bool changeUid = false)
        {
            _skipCalculatePassLinesInEleCopy = false;// 设置标志位，跳过CalculatePassLines方法
            _skipCalcuPtcenter = false;

            //特有参数复制
            ArcColumn ele = new ArcColumn();

            //共有参数复制
            PubCopy(ele);
            ele._transverseWith = _transverseWith;
            ele._verticalwidth = _verticalwidth;
            ele._radius = _radius;
            ele._doubleradius = _doubleradius;
            ele._topOffset = _topOffset;
            ele._bottomOffset = _bottomOffset;
            ele._topLevel = _topLevel;
            ele._bottomLevel = _bottomLevel;
            ele._hoAngle = _hoAngle;
            ele._surfthick = _surfthick;
            ele._surfheight = _surfheight;
            ele._rightRec = _rightRec;
            ele._topRec = _topRec;
            ele._leftRec = _leftRec;
            ele._bottomRec = _bottomRec;
            ele._rightCir = _rightCir;
            ele._topCir = _topCir;
            ele._leftCir = _leftCir;
            ele._bottomCir = _bottomCir;
            ele._ifVer = _ifVer;
            ele._veAngle = _veAngle;
            ele._topStyle = _topStyle;
            ele._topAngle = _topAngle;
            ele._bottomStyle = _bottomStyle;
            ele._bottomAngle = _bottomAngle;
            ele._sectiontype = _sectiontype;
            ele._isshowsurface = _isshowsurface;
            ele._ifboundary = _ifboundary;
            ele._length = _length;
            ele._sectionarea = _sectionarea;
            ele._volume = _volume;
            ele._coreMateria = _coreMateria;
            ele._surfaceMateria = _surfaceMateria;
            ele._ptcenter = _ptcenter.EleCopy();
            //ele._insertpt = _insertpt.EleCopy();
            ele._columntype = _columntype;

            ele._ifedited = _ifedited;
            ele._skipCalculatePassLinesInEleCopy = _skipCalculatePassLinesInEleCopy;
            ele._skipCalcuPtcenter = _skipCalcuPtcenter;
            ele.cutregion = cutregion.EleCopy();
            ele._surftex = _surftex;

            ele.LinesRecorder = GFunc.CopyLines(LinesRecorder);
            ele.CircleRecorder = CopyCircles(CircleRecorder);
            ele.SurCircle = CopyCircles(SurCircle);
            ele.SurfLines = GFunc.CopyLines(SurfLines);
            ele.arccolumnregion = arccolumnregion.EleCopy();
            ele.cutregion = cutregion.EleCopy();
            ele.outregion = outregion.EleCopy();
            ele._ifmirror = _ifmirror;

            ele.SecType = SecType;
            ele.secLoopLines = ArcGFunc.LineListEleCopy(secLoopLines);
            ele.IrregularColumnRotatePt = IrregularColumnRotatePt.EleCopy();
            ele.ifRptate = ifRptate;
            ele.mirrorLine = mirrorLine.EleCopy();
            if (changeUid) { ele.UniqueId = Guid.NewGuid().ToString(); }
            return ele;
        }

        /// <summary>
        ///  复制圆集合
        /// </summary>
        /// <_param name="circles"></_param>
        /// <returns></returns>
        private List<DbCircle> CopyCircles(List<DbCircle> circles)
        {
            if (circles == null) { return null; }
            List<DbCircle> circlesT = new List<DbCircle>();
            for (int i = 0; i < circles.Count; i++) { circlesT.Add(circles[i].EleCopy()); }
            return circlesT;
        }



        /// <summary>
        /// 重写旋转
        /// </summary>
        /// <_param name="Pt"></_param>
        /// <_param name="ang"></_param>
        /// <summary>
        /// 图元旋转操作
        /// 围绕指定点旋转建筑柱图元，同时更新所有相关的几何数据
        /// </summary>
        /// <param name="Pt">旋转中心点</param>
        /// <param name="ang">旋转角度（弧度制）</param>
        public override void EleMove_r(DbPt Pt, double ang)
        {
            // 启用计算标志，确保旋转后重新计算几何数据
            _skipCalculatePassLinesInEleCopy = false;  // 允许重新计算穿越线
            _skipCalcuPtcenter = false;                // 允许重新计算中心点
            ifRptate = true;                           // 标记为已旋转状态
            IrregularColumnRotatePt = Pt.EleCopy();    // 记录异形柱的旋转中心点

            // 旋转所有控制点
            foreach (DbPt pt in ConPts) { pt.RotateSelf(Pt, ang); }
            
            // 更新柱子的截面旋转角度（转换为角度制）
            _hoAngle += ang * 180 / Math.PI;

            // 根据柱子是否被编辑过，采用不同的旋转策略
            if (!_ifedited)
            {
                // 未编辑状态：只需旋转异形柱的截面轮廓线
                if (_columntype == CoTy.Irregular)
                {
                    // 旋转异形柱的原始截面轮廓线
                    foreach (var line in secLoopLines)
                    {
                        line.RotateSelf(Pt, ang);
                    }
                }
            }
            else
            {
                // 已编辑状态：需要旋转编辑后的几何线条
                if (_columntype == CoTy.Rectangle)
                {
                    // 矩形柱：旋转除前4条基础线外的所有编辑线条
                    // 前4条线是基础几何线，由参数自动生成，不需要单独旋转
                    for (int i = 4; i < LinesRecorder.Count; i++)
                    {
                        LinesRecorder[i].RotateSelf(Pt, ang);
                    }
                }
                else if (_columntype == CoTy.Irregular)
                {
                    // 异形柱：旋转除截面轮廓线外的所有编辑线条
                    // secLoopLines.Count之前的线条是截面轮廓线，之后的是编辑添加的线条
                    for (int i = secLoopLines.Count; i < LinesRecorder.Count; i++)
                    {
                        LinesRecorder[i].RotateSelf(Pt, ang);
                    }
                }
            }
            
            // 重新激活图元，触发几何计算和显示更新
            Activate();
        }
        /// <summary>
        /// 视图改变
        /// </summary>
        public override void ViewScaleChange()
        {
            ActAndCal2D();
        }
        /// <summary>
        /// 单位控制点
        /// </summary>
        /// <returns></returns>
        public override List<DbPt> ConPts3D()
        {
            return new List<DbPt>();
        }

        /// <summary>
        /// 立剖面
        /// </summary>
        /// <_param name="pl"></_param>
        /// <_param name="pairCut"></_param>
        /// <_param name="pairSee"></_param>
        public override void SetSecViewEle(CutPlane pl, EleSecLinePair pairCut, EleSecLinePair pairSee)
        {
            DbHatch hatch = new DbHatch();
            if (_columntype == CoTy.Rectangle)
            {
                if (pairSee != null) //处理看线
                {
                    foreach (var item in pairSee.linesCut)
                    {
                        if (item is DbLine line)
                        {
                            line.LayerId = PreLayerManage.GetLayerId("立面-梁板柱");
                            line.ColorIndex = -1;
                            line.StyleIndex = -1;
                            line.WidthIndex = -1;
                        }
                    }
                }

                if (pairCut != null) //处理剖线
                {
                    pairCut._addEles = new List<Db>();

                    if (_isshowsurface) //显示面层的情况下，返回的是两个面域
                    {
                        List<DbLine> CutLines = GFunc.CopyLines(pairCut.linesCut);
                        pairCut.linesCut.Clear();

                        List<DbRegion> regs = new List<DbRegion>();
                        regs.Add(pairCut.reg);
                        regs.AddRange(pairCut.regs);
                        DbRegion coreRegion = new DbRegion();
                        DbRegion surfRegion = new DbRegion();

                        if (!_ifVer)
                        {
                            foreach (var re in regs)
                            {
                                if (re.Loops.Count == 2)
                                {
                                    surfRegion = re.EleCopy();
                                }
                                else if (re.Loops.Count == 1)
                                {
                                    coreRegion = re.EleCopy();
                                }
                            }
                        }
                        else
                        {
                            if (regs.Count == 2)
                            {
                                coreRegion = regs.OrderByDescending(r => r.Area()).FirstOrDefault();
                                surfRegion = regs.OrderBy(r => r.Area()).FirstOrDefault();
                            }
                        }
                        List<DbLine> coreLines = new List<DbLine>(); //记录面层的线
                        List<DbLine> surfLines = new List<DbLine>(); //记录剖面的线
                        foreach (var loop in coreRegion.Loops)
                        {
                            coreLines.AddRange(loop.Lines);
                        }
                        foreach (var loop in surfRegion.Loops)
                        {
                            surfLines.AddRange(loop.Lines);
                        }
                        surfLines = GMath.GetLinesOutRegion(surfLines, coreRegion);

                        foreach (var item in coreLines)
                        {
                            if (item is DbLine line)
                            {
                                line.LayerId = PreLayerManage.GetLayerId("剖面-梁板柱");
                                line.ColorIndex = -1;
                                line.StyleIndex = -1;
                                line.WidthIndex = -1;
                                pairCut._addEles.Add(line);
                            }
                        }

                        foreach (var item in surfLines)
                        {
                            if (item is DbLine line)
                            {
                                line.LayerId = PreLayerManage.GetLayerId("剖面-面层");
                                line.ColorIndex = -1;
                                line.StyleIndex = -1;
                                line.WidthIndex = -1;
                                pairCut._addEles.Add(line);
                            }
                        }

                        //处理剖面填充及其图层
                        DbRegion reg = pairCut.reg;
                        if (_coreMateria.IsHatchSecHasValue(pl.ViewSc, out var hatchParam)) //填充前需要判断是否有填充样式
                        {
                            hatch = new DbHatch(reg, hatchParam.HatchIdx, hatchParam.Scale, hatchParam.Color);
                        }
                        hatch.LayerId = PreLayerManage.GetLayerId("剖面-填充");
                        hatch.ColorIndex = -1;
                        pairCut._addEles.Add(hatch);
                    }
                    else
                    {
                        //处理剖面填充及其图层
                        DbRegion reg = pairCut.reg;
                        if (_coreMateria.IsHatchSecHasValue(pl.ViewSc, out var hatchParam)) //填充前需要判断是否有填充样式
                        {
                            hatch = new DbHatch(reg, hatchParam.HatchIdx, hatchParam.Scale, hatchParam.Color);
                        }
                        hatch.LayerId = PreLayerManage.GetLayerId("剖面-填充");
                        hatch.ColorIndex = -1;
                        pairCut._addEles.Add(hatch);
                    }

                }
            }
            else if (_columntype == CoTy.Circle)
            {
                List<double> disliet = new List<double>();
                foreach (var pt in ConPts)
                {
                    double depthOfConpts = pl.GetZDepth(pt);
                    disliet.Add(depthOfConpts);
                }
                if (pairSee != null) //处理看线
                {
                    double height = _view.Height + TopOffset - _bottomOffset;
                    pairSee.linesCut = new List<DbLine>();//清除图元立面时看线的集合    
                    pairSee._addEles = new List<Db>();//清除图元立面增加的图元的集合
                    if (!_ifVer)
                    {
                        if (disliet[0] > 0)
                        {
                            DbPt pt = ConPts[0].EleCopy();
                            DbPt ptC = pl.GetLocalCoord(pt);
                            double depth = pl.GetZDepth(ConPts[0]);
                            DbPt ptleftbottom = new DbPt();
                            DbPt ptrightbottom = new DbPt();
                            DbPt ptlefttop = new DbPt();
                            DbPt ptrighttop = new DbPt();
                            if (_isshowsurface)
                            {
                                ptleftbottom = ptC.Move(-(_radius + 20), 0, depth);
                                ptrightbottom = ptC.Move((_radius + 20), 0, depth);
                                ptlefttop = ptC.Move(-(_radius + 20), height, depth);
                                ptrighttop = ptC.Move((_radius + 20), height, depth);
                            }
                            else
                            {
                                ptleftbottom = ptC.Move(-(_radius), 0, depth);
                                ptrightbottom = ptC.Move((_radius), 0, depth);
                                ptlefttop = ptC.Move(-(_radius), height, depth);
                                ptrighttop = ptC.Move((_radius), height, depth);
                            }

                            DbLine linebottom = new DbLine(ptrightbottom.EleCopy(), ptleftbottom.EleCopy());
                            DbLine lineleft = new DbLine(ptleftbottom.EleCopy(), ptlefttop.EleCopy());
                            DbLine linetop = new DbLine(ptlefttop.EleCopy(), ptrighttop.EleCopy());
                            DbLine lineright = new DbLine(ptrighttop.EleCopy(), ptrightbottom.EleCopy());

                            //计算圆柱立面示意线
                            double baseNum = _radius / 15;
                            double dis1 = baseNum;
                            double dis2 = baseNum * 2;
                            double dis3 = baseNum * 3;
                            double dis4 = baseNum * 4;
                            double dis5 = baseNum * 5;

                            DbLine l1 = lineleft.Move(dis1, 0);
                            DbLine l2 = l1.Move(dis2, 0);
                            DbLine l3 = l2.Move(dis3, 0);
                            DbLine l4 = l3.Move(dis4, 0);
                            DbLine l5 = l4.Move(dis5, 0);

                            DbLine r1 = lineright.Move(-dis1, 0);
                            DbLine r2 = r1.Move(-dis2, 0);
                            DbLine r3 = r2.Move(-dis3, 0);
                            DbLine r4 = r3.Move(-dis4, 0);
                            pairSee._addEles.Add(linebottom);
                            pairSee._addEles.Add(lineleft);
                            pairSee._addEles.Add(linetop);
                            pairSee._addEles.Add(lineright);

                            pairSee._addEles.Add(l1);
                            pairSee._addEles.Add(l2);
                            pairSee._addEles.Add(l3);
                            pairSee._addEles.Add(l4);
                            pairSee._addEles.Add(l5);

                            pairSee._addEles.Add(r1);
                            pairSee._addEles.Add(r2);
                            pairSee._addEles.Add(r3);
                            pairSee._addEles.Add(r4);
                            foreach (var item in pairSee._addEles)
                            {
                                if (item is DbLine line)
                                {
                                    line.ColorIndex = -1;
                                }
                            }
                            for (var i = 4; i < pairSee._addEles.Count; i++)
                            {
                                if (pairSee._addEles[i] is DbLine line)
                                {
                                    line.ColorIndex = 8;
                                }
                            }

                            for (int i = 0; i < 4; i++)
                            {
                                if (pairSee._addEles[i] is DbLine line)
                                {
                                    line.LayerId = PreLayerManage.GetLayerId("立面-梁板柱");
                                }
                            }
                        }
                    }
                    else
                    {
                        if (disliet[0] > 0)
                        {
                            DbPt ptC1 = pl.GetLocalCoord(ConPts[0].EleCopy());
                            double depth1 = pl.GetZDepth(ConPts[0]);
                            DbPt ptC2 = pl.GetLocalCoord(ConPts[5].EleCopy());
                            double depth2 = pl.GetZDepth(ConPts[5]);
                            DbPt botCorePt = new DbPt(ptC1.X, ptC1.Y, depth1);
                            DbPt topCorePt = new DbPt(ptC2.X, height, depth2);
                            DbLine axis = new DbLine(botCorePt, topCorePt);

                            if (_isshowsurface)
                            {

                            }
                            (double botCoreXMax, double botCoreXMin) = GMath.GetXMaxMinOfLines(arccolumnregion.GetRegionLines());
                            (double botSurfXMax, double botSurfXMin) = GMath.GetXMaxMinOfLines(cutregion.GetRegionLines());

                            List<DbPt> botCorePts = new List<DbPt>();
                            foreach (var line in arccolumnregion.GetRegionLines())
                            {
                                botCorePts.Add(line.PtSt);
                                botCorePts.Add(line.PtMid);
                                botCorePts.Add(line.PtMid);
                            }
                            List<DbPt> botSurfPts = new List<DbPt>();
                            foreach (var line in cutregion.GetRegionLines())
                            {
                                botSurfPts.Add(line.PtSt);
                                botSurfPts.Add(line.PtMid);
                                botSurfPts.Add(line.PtMid);
                            }

                            DbPt botCoreMinXPt = botCorePts.OrderBy(pt => pt.X).FirstOrDefault();
                            DbPt botCoreMaxXPt = botCorePts.OrderByDescending(pt => pt.X).FirstOrDefault();
                            DbPt botSurfMinXPt = botSurfPts.OrderBy(pt => pt.X).FirstOrDefault();
                            DbPt botSurfMaxXPt = botSurfPts.OrderByDescending(pt => pt.X).FirstOrDefault();

                            DbPt botCoreMinXPtC = pl.GetLocalCoord(botCoreMinXPt.EleCopy());
                            DbPt botCoreMaxXPtC = pl.GetLocalCoord(botCoreMaxXPt.EleCopy());
                            DbPt botSurfMinXPtC = pl.GetLocalCoord(botSurfMinXPt.EleCopy());
                            DbPt botSurfMaxXPtC = pl.GetLocalCoord(botSurfMaxXPt.EleCopy());

                            DbPt topCoreMinXPtC = botCoreMinXPtC.EleCopy().Move(topCorePt.X - botCorePt.X, topCorePt.Y - botCorePt.Y, topCorePt.Z - botCorePt.Z);
                            DbPt topCoreMaxXPtC = botCoreMaxXPtC.EleCopy().Move(topCorePt.X - botCorePt.X, topCorePt.Y - botCorePt.Y, topCorePt.Z - botCorePt.Z);
                            DbPt topSurfMinXPtC = botSurfMinXPtC.EleCopy().Move(topCorePt.X - botCorePt.X, topCorePt.Y - botCorePt.Y, topCorePt.Z - botCorePt.Z);
                            DbPt topSurfMaxXPtC = botSurfMaxXPtC.EleCopy().Move(topCorePt.X - botCorePt.X, topCorePt.Y - botCorePt.Y, topCorePt.Z - botCorePt.Z);

                            List<DbLine> coreLines = new List<DbLine>();
                            coreLines.Add(new DbLine(botCoreMinXPtC, topCoreMinXPtC));
                            coreLines.Add(new DbLine(botCoreMaxXPtC, topCoreMaxXPtC));

                            List<DbLine> surfLines = new List<DbLine>();
                            surfLines.Add(new DbLine(botSurfMinXPtC, topSurfMinXPtC));
                            surfLines.Add(new DbLine(botSurfMaxXPtC, topSurfMaxXPtC));

                            DbLine topLine = new DbLine(topSurfMinXPtC, topSurfMaxXPtC);
                            DbLine botLine = new DbLine(botSurfMinXPtC, botSurfMaxXPtC);

                            pairSee._addEles.Add(topLine);
                            pairSee._addEles.Add(botLine);
                            pairSee._addEles.AddRange(surfLines);

                            for (int i = 0; i < pairSee._addEles.Count; i++)
                            {
                                if (pairSee._addEles[i] is DbLine line)
                                {
                                    line.LayerId = PreLayerManage.GetLayerId("立面-梁板柱");
                                }
                            }
                        }
                    }
                }
                if (pairCut != null) //处理剖线
                {
                    pairCut._addEles = new List<Db>();
                    if (_isshowsurface)
                    {
                        List<DbLine> CutLines = GFunc.CopyLines(pairCut.linesCut);
                        pairCut.linesCut.Clear();
                        List<DbRegion> regs = new List<DbRegion>();
                        regs.Add(pairCut.reg);
                        regs.AddRange(pairCut.regs);
                        DbRegion coreRegion = new DbRegion();
                        DbRegion surfRegion = new DbRegion();

                        if (!_ifVer)
                        {
                            foreach (var re in regs)
                            {
                                if (re.Loops.Count == 2)
                                {
                                    surfRegion = re.EleCopy();
                                }
                                else if (re.Loops.Count == 1)
                                {
                                    coreRegion = re.EleCopy();
                                }
                            }
                        }
                        else
                        {
                            if (regs.Count == 2)
                            {
                                coreRegion = regs.OrderByDescending(r => r.Area()).FirstOrDefault();
                                surfRegion = regs.OrderBy(r => r.Area()).FirstOrDefault();

                            }
                        }
                        List<DbLine> coreLines = new List<DbLine>(); //记录面层的线
                        List<DbLine> surfLines = new List<DbLine>(); //记录剖面的线
                        foreach (var loop in coreRegion.Loops)
                        {
                            coreLines.AddRange(loop.Lines);
                        }
                        foreach (var loop in surfRegion.Loops)
                        {
                            surfLines.AddRange(loop.Lines);
                        }
                        surfLines = GMath.GetLinesOutRegion(surfLines, coreRegion);

                        foreach (var item in coreLines)
                        {
                            if (item is DbLine line)
                            {
                                line.LayerId = PreLayerManage.GetLayerId("剖面-梁板柱");
                                line.ColorIndex = -1;
                                line.StyleIndex = -1;
                                line.WidthIndex = -1;
                                pairCut._addEles.Add(line);
                            }
                        }

                        foreach (var item in surfLines)
                        {
                            if (item is DbLine line)
                            {
                                line.LayerId = PreLayerManage.GetLayerId("剖面-面层");
                                line.ColorIndex = -1;
                                line.StyleIndex = -1;
                                line.WidthIndex = -1;
                                pairCut._addEles.Add(line);
                            }
                        }
                    }


                    //处理剖面填充及其图层
                    DbRegion reg = pairCut.reg;
                    if (_coreMateria.IsHatchSecHasValue(pl.ViewSc, out var hatchParam)) //填充前需要判断是否有填充样式
                    {
                        hatch = new DbHatch(reg, hatchParam.HatchIdx, hatchParam.Scale, hatchParam.Color);
                    }
                    hatch.LayerId = PreLayerManage.GetLayerId("剖面-填充");
                    hatch.ColorIndex = -1;
                    pairCut._addEles.Add(hatch);
                }
            }

            else if (_columntype == CoTy.Irregular)
            {
                double height = _view.Height + TopOffset - _bottomOffset;
                cutregion.ChangeLevel(_view.BotLevel + _bottomOffset);
                arccolumnregion.ChangeLevel(_view.BotLevel + _bottomOffset);

                if (pairSee != null)
                {
                    pairSee.linesCut = new List<DbLine>();//清除图元立面时看线的集合    
                    pairSee._addEles = new List<Db>();//清除图元立面增加的图元的集合
                    List<DbLine> outBottomCoreLines = new List<DbLine>();
                    if (_isshowsurface)
                    {
                        //获取面层面积最大的loop
                        DbLoop loop0 = cutregion.Loops[0];
                        DbLoop loop1 = cutregion.Loops[1];
                        DbLoop coreLoop = new DbLoop();
                        DbLoop surfLoop = new DbLoop();

                        if (GMath.GetBulLoopAs(loop0) > GMath.GetBulLoopAs(loop1))
                        {
                            surfLoop = loop0.EleCopy();
                            coreLoop = loop1.EleCopy();
                        }
                        else
                        {
                            surfLoop = loop1.EleCopy();
                            coreLoop = loop0.EleCopy();
                        }
                        outBottomCoreLines = surfLoop.Lines;
                    }
                    else
                    {
                        outBottomCoreLines = arccolumnregion.EleCopy().GetRegionLines();
                    }
                    List<DbPt> outBottomCorePts = new List<DbPt>();
                    foreach (var line in outBottomCoreLines)
                    {
                        if (line.IfArc)
                        {
                            outBottomCorePts.Add(line.PtSt);
                            outBottomCorePts.Add(line.PtMid);
                            outBottomCorePts.Add(line.PtEnd);
                        }
                        else
                        {
                            outBottomCorePts.Add(line.PtSt);
                            outBottomCorePts.Add(line.PtEnd);
                        }
                    }
                    GMath.MergePts(outBottomCorePts);

                    double depthOfConpts = pl.GetZDepth(ConPts[0]);
                    if (depthOfConpts >= 0)
                    {
                        DbPt ptC = pl.GetLocalCoord(ConPts[0]);
                        List<DbPt> inBottomCorePts = new List<DbPt>();
                        foreach (var pt in outBottomCorePts)
                        {
                            double depth = pl.GetZDepth(pt);
                            DbPt inPt = pl.GetLocalCoord(pt);
                            inPt.Z = depth;
                            inBottomCorePts.Add(inPt);
                        }

                        List<DbPt> inTopCorePts = new List<DbPt>();

                        if (!_ifVer)
                        {
                            foreach (var pt in inBottomCorePts)
                            {
                                inTopCorePts.Add(pt.EleCopy().Move(0, height, 0));
                            }
                        }
                        else
                        {
                            double verang = GMath.AngleToRadians(_veAngle);
                            double Xdis = ArcGFunc.SectionLevel / Math.Tan(verang);
                            double XTopDis = height / Math.Tan(verang);
                            double dcos = 1 / Math.Sin(verang);
                            foreach (var pt in inBottomCorePts)
                            {
                                inTopCorePts.Add(pt.EleCopy().Move(XTopDis, height));
                            }
                        }
                        List<DbLine> showLines = new List<DbLine>();
                        for (int i = 0; i < inTopCorePts.Count; i++)
                        {

                            showLines.Add(new DbLine(inTopCorePts[i], inBottomCorePts[i]));
                        }
                        DbPt bottomXminPt = inBottomCorePts.OrderBy(p => p.X).FirstOrDefault();
                        DbPt bottomXMaxPt = inBottomCorePts.OrderByDescending(p => p.X).FirstOrDefault();
                        DbPt topXminPt = inTopCorePts.OrderBy(p => p.X).FirstOrDefault();
                        DbPt topXmaxPt = inTopCorePts.OrderByDescending(p => p.X).FirstOrDefault();
                        DbLine topLine = new DbLine(bottomXminPt, bottomXMaxPt);
                        DbLine botLine = new DbLine(topXminPt, topXmaxPt);

                        pairSee._addEles.Add(topLine);
                        pairSee._addEles.Add(botLine);
                        pairSee._addEles.AddRange(showLines);

                        for (int i = 0; i < pairSee._addEles.Count; i++)
                        {
                            if (pairSee._addEles[i] is DbLine line)
                            {
                                line.LayerId = PreLayerManage.GetLayerId("立面-梁板柱");
                            }
                        }
                    }
                }
                if (pairCut != null)
                {
                    pairCut._addEles = new List<Db>();
                    if (_isshowsurface)
                    {
                        List<DbLine> CutLines = GFunc.CopyLines(pairCut.linesCut);
                        pairCut.linesCut.Clear();
                        List<DbRegion> regs = new List<DbRegion>();
                        regs.Add(pairCut.reg);
                        regs.AddRange(pairCut.regs);
                        DbRegion coreRegion = new DbRegion();
                        DbRegion surfRegion = new DbRegion();

                        if (!_ifVer)
                        {
                            foreach (var re in regs)
                            {
                                if (re.Loops.Count == 2)
                                {
                                    surfRegion = re.EleCopy();
                                }
                                else if (re.Loops.Count == 1)
                                {
                                    coreRegion = re.EleCopy();
                                }
                            }
                        }
                        else
                        {
                            if (regs.Count == 2)
                            {
                                coreRegion = regs.OrderByDescending(r => r.Area()).FirstOrDefault();
                                surfRegion = regs.OrderBy(r => r.Area()).FirstOrDefault();
                            }
                        }
                        List<DbLine> coreLines = new List<DbLine>(); //记录面层的线
                        List<DbLine> surfLines = new List<DbLine>(); //记录剖面的线
                        foreach (var loop in coreRegion.Loops)
                        {
                            coreLines.AddRange(loop.Lines);
                        }
                        foreach (var loop in surfRegion.Loops)
                        {
                            surfLines.AddRange(loop.Lines);
                        }
                        surfLines = GMath.GetLinesOutRegion(surfLines, coreRegion);

                        foreach (var item in coreLines)
                        {
                            if (item is DbLine line)
                            {
                                line.LayerId = PreLayerManage.GetLayerId("剖面-梁板柱");
                                line.ColorIndex = -1;
                                line.StyleIndex = -1;
                                line.WidthIndex = -1;
                                pairCut._addEles.Add(line);
                            }
                        }

                        foreach (var item in surfLines)
                        {
                            if (item is DbLine line)
                            {
                                line.LayerId = PreLayerManage.GetLayerId("剖面-面层");
                                line.ColorIndex = -1;
                                line.StyleIndex = -1;
                                line.WidthIndex = -1;
                                pairCut._addEles.Add(line);
                            }
                        }
                    }

                    //处理剖面填充及其图层
                    DbRegion reg = pairCut.reg;
                    if (_coreMateria.IsHatchSecHasValue(pl.ViewSc, out var hatchParam)) //填充前需要判断是否有填充样式
                    {
                        hatch = new DbHatch(reg, hatchParam.HatchIdx, hatchParam.Scale, hatchParam.Color);
                    }
                    hatch.LayerId = PreLayerManage.GetLayerId("剖面-填充");
                    hatch.ColorIndex = -1;
                    pairCut._addEles.Add(hatch);
                }
            }
        }
        /// <summary>
        /// 实体
        /// </summary>
        public override void CalSolid()
        {
            bool flag = ArchGlobalParams.GetGlobalParams().IfShowArcColumn;
            //_solid = new Solid();
            if (flag)
            {
                List<DbLoop> loops = new List<DbLoop>(); //收集所有用于生成实体的loop
                                                         //以下为创建柱实体
                List<Solid> columnallsolid = new List<Solid>(); //收集所有的实体
                var bot = _view.BotLevel + _bottomOffset; //底部标高
                var top = _view.TopLevel + _topOffset; //顶部标高
                var culumnheight = top - bot; //柱长
                var tempoutlines = new List<DbLine>();
                var temCirlines = new List<DbLine>();
                DbPt vec = new DbPt();//斜柱实体拉伸的方向向量

                if (_columntype == CoTy.Rectangle)
                {
                    List<DbPt> pts = new List<DbPt>();
                    pts.Add(ConPts[5]);
                    pts.Add(ConPts[6]);
                    pts.Add(ConPts[7]);
                    pts.Add(ConPts[8]);
                    arccolumnregion = GMath.GetRegionByPts(pts);
                    arccolumnregion.ChangeLevel(bot);
                    var sf = new SolidFace(arccolumnregion);
                    if (!_ifVer)
                    {
                        columnallsolid.Add(new Solid(sf, culumnheight, new DbPt(0, 0, 1), _coreMateria.Alpha, _coreMateria.ColorF, _coreMateria.ColorL) { id = UniqueId, _mat = SolidMaterial.Concrete });
                    }
                    else
                    {
                        DbPt vec02 = ConPts[2] - ConPts[0];
                        vec02.Normalize();
                        double verang = GMath.AngleToRadians(_veAngle);
                        double Xdis = culumnheight / Math.Tan(verang);
                        DbPt topCenPt = ConPts[0].EleCopy().Move(vec02, Xdis).Move(0, 0, culumnheight);
                        vec = topCenPt - ConPts[0];
                        vec.Normalize();
                        culumnheight = culumnheight / Math.Sin(verang);
                        columnallsolid.Add(new Solid(sf, culumnheight, vec, _coreMateria.Alpha, _coreMateria.ColorF, _coreMateria.ColorL) { id = UniqueId, _mat = SolidMaterial.Concrete });
                    }

                    //创建柱实体结束
                    //接下来创建矩形柱的面层与柱主体之前形成的实体
                    DbLoop columnLoop = new DbLoop(pts);//创建柱主体的loop
                    loops.Add(columnLoop); //把柱主体的loop加入loops
                                           //以下创建面层实体，如果没有被编辑过则使用默认面层创建，如果被编辑过则使用编辑过后的面层创建
                    if (_isshowsurface)
                    {
                        if (!_ifedited) //如果没有被编辑过
                        {
                            Solid coreSolid = new Solid();
                            if (!_ifVer)
                            {
                                foreach (var item in SurfLines)
                                {
                                    if (item.PtSt.Z != bot)
                                    {
                                        item.MoveSelf(new DbPt(0, 0, 1), _view.BotLevel - item.PtSt.Z);
                                    }
                                }
                                loops.AddRange(GMath.GetCwLoopsOfPls(SurfLines));
                                cutregion = new DbRegion(loops); //把面层和主体的loop合并到一起，形成面域
                                cutregion.ChangeLevel(bot);
                                var sfc = new SolidFace(cutregion);
                                //默认面层高度为柱实体高度
                                coreSolid = new Solid(sfc, culumnheight, new DbPt(0, 0, 1), _surfaceMateria.Alpha, _surfaceMateria.ColorF, _surfaceMateria.ColorL) { id = UniqueId, _mat = SolidMaterial.GypsumAsbestos }; //把默认面层形成实体
                            }
                            else
                            {
                                foreach (var item in bottomLines)
                                {
                                    if (item.PtSt.Z != bot)
                                    {
                                        item.MoveSelf(new DbPt(0, 0, 1), _view.BotLevel - item.PtSt.Z);
                                    }
                                }
                                loops.AddRange(GMath.GetCwLoopsOfPls(bottomLines));
                                cutregion = new DbRegion(loops); //把面层和主体的loop合并到一起，形成面域
                                List<DbLine> lines = cutregion.GetRegionLines();
                                cutregion.ChangeLevel(bot);
                                var sfc = new SolidFace(cutregion);
                                //默认面层高度为柱实体高度
                                coreSolid = new Solid(sfc, culumnheight, vec, _surfaceMateria.Alpha, _surfaceMateria.ColorF, _surfaceMateria.ColorL) { id = UniqueId, _mat = SolidMaterial.GypsumAsbestos }; //把默认面层形成实体
                            }

                            columnallsolid.Add(coreSolid);
                        }

                        else //如果被编辑过
                        {
                            tempoutlines = new List<DbLine>();
                            if (LinesRecorder.Count > 4)
                            {
                                for (int i = 4; i < LinesRecorder.Count; i++)
                                {
                                    tempoutlines.Add(LinesRecorder[i]);
                                }
                                loops.AddRange(GMath.GetCwLoopsOfPls(tempoutlines));
                            }
                            if (Circles.Count > 0)
                            {
                                temCirlines.AddRange(GMath.GetCircleArc(Circles[0]));
                                loops.AddRange(GMath.GetCwLoopsOfPls(temCirlines));
                            }

                            //把面层和主体的loop合并到一起，形成面域
                            cutregion = new DbRegion(loops);
                            cutregion.ChangeLevel(bot);
                            var sfc = new SolidFace(cutregion);
                            var surfacedolid = new Solid(sfc, culumnheight, new DbPt(0, 0, 1), _surfaceMateria.Alpha, _surfaceMateria.ColorF, _surfaceMateria.ColorL) { id = UniqueId, _mat = SolidMaterial.GypsumAsbestos };
                            columnallsolid.Add(surfacedolid); //把编辑过的面层形成实体加入到columnallsolid 
                        }
                    }

                    _coreMateria.SetSolidProperty(columnallsolid[0]);
                    if (_isshowsurface)
                    {
                        _surfaceMateria.SetSolidProperty(columnallsolid[1]);
                    }

                    _solid = TDMath.JoinSolids(columnallsolid, false);
                }

                else if (_columntype == CoTy.Circle)
                {
                    foreach (var item in ConPts)
                    {
                        if (item.Z != bot)
                        {
                            item.MoveSelf(new DbPt(0, 0, 1), _view.BotLevel - item.Z);
                        }
                    }

                    foreach (var item in Circles)
                    {
                        if (item.CPt.Z != bot)
                        {
                            item.MoveSelf(new DbPt(0, 0, 1), _view.BotLevel - item.CPt.Z);
                        }
                    }

                    //创建圆形柱的主体实体
                    //arccolumnregion = GMath.GetRegionOfCircle(Circles[0]);
                    arccolumnregion.ChangeLevel(bot);
                    var sf = new SolidFace(arccolumnregion);
                    if (!_ifVer)
                    {
                        columnallsolid.Add(new EB3DDB.Solid(sf, culumnheight, new DbPt(0, 0, 1), _coreMateria.Alpha, _coreMateria.ColorF, _coreMateria.ColorL) { id = UniqueId, _mat = SolidMaterial.Concrete });
                    }
                    else
                    {
                        DbPt vec02 = ConPts[2] - ConPts[0];
                        vec02.Normalize();
                        double verang = GMath.AngleToRadians(_veAngle);
                        double Xdis = culumnheight / Math.Tan(verang);
                        DbPt topCenPt = ConPts[0].EleCopy().Move(vec02, Xdis).Move(0, 0, culumnheight);
                        vec = topCenPt - ConPts[0];
                        vec.Normalize();
                        culumnheight = culumnheight / Math.Sin(verang);
                        columnallsolid.Add(new Solid(sf, culumnheight, vec, _coreMateria.Alpha, _coreMateria.ColorF, _coreMateria.ColorL) { id = UniqueId, _mat = SolidMaterial.Concrete });
                    }



                    //以上创建圆形柱本体的的实体结束
                    //创建圆形柱的loop并加入到loops
                    temCirlines.AddRange(GMath.GetCircleArc(Circles[0]));
                    loops.AddRange(GMath.GetCwLoopsOfPls(temCirlines));
                    if (_isshowsurface)
                    {
                        //以下创建圆形柱面层实体，如果没有被编辑过则使用默认面层创建，如果被编辑过则使用编辑过后的面层创建
                        if (!_ifedited) //如果没有被编辑过
                        {
                            var cirSolid = new Solid();
                            if (!_ifVer)
                            {
                                cutregion.ChangeLevel(bot);
                                var sfc = new SolidFace(cutregion);
                                cirSolid = new Solid(sfc, culumnheight, new DbPt(0, 0, 1), _surfaceMateria.Alpha, _surfaceMateria.ColorF, _surfaceMateria.ColorL) { id = UniqueId, _mat = SolidMaterial.GypsumAsbestos }; //把默认面层形成实体
                            }
                            else
                            {

                                cutregion.ChangeLevel(bot);
                                var sfc = new SolidFace(cutregion);
                                cirSolid = new Solid(sfc, culumnheight, vec, _surfaceMateria.Alpha, _surfaceMateria.ColorF, _surfaceMateria.ColorL) { id = UniqueId, _mat = SolidMaterial.GypsumAsbestos }; //把默认面层形成实体
                            }
                            columnallsolid.Add(cirSolid);
                            loops.Clear();
                        }

                        else //如果被编辑过
                        {
                            var temloopline = new List<DbLine>();

                            if (LinesRecorder.Count > 0)
                            {
                                for (int i = 0; i < LinesRecorder.Count; i++)
                                {
                                    tempoutlines.Add(LinesRecorder[i]);
                                }

                                loops.AddRange(GMath.GetCwLoopsOfPls(tempoutlines));
                            }

                            if (Circles.Count > 1)
                            {
                                temloopline = (GMath.GetCircleArc(Circles[1]));
                                loops.AddRange(GMath.GetCwLoopsOfPls(temloopline));
                                GuiFunction.GFunc.DebugSend(temCirlines);
                            }


                            //把面层和主体的loop合并到一起，形成面域
                            cutregion = new DbRegion(loops);
                            cutregion.ChangeLevel(bot);

                            var sfc = new SolidFace(cutregion);
                            var surfacedolid = new Solid(sfc, culumnheight, new DbPt(0, 0, 1), _surfaceMateria.Alpha, _surfaceMateria.ColorF, _surfaceMateria.ColorL) { id = UniqueId, _mat = SolidMaterial.GypsumAsbestos };
                            columnallsolid.Add(surfacedolid); //把编辑过的面层形成实体加入到columnallsolid  
                            loops.Clear();
                        }
                    }

                    _coreMateria.SetSolidProperty(columnallsolid[0]);
                    if (_isshowsurface)
                    {
                        _surfaceMateria.SetSolidProperty(columnallsolid[1]);
                    }

                    _solid = TDMath.JoinSolids(columnallsolid, false);
                    columnallsolid.Clear();
                    temCirlines.Clear();
                    tempoutlines.Clear();
                    loops.Clear();
                }

                else if (_columntype == CoTy.Irregular) //异形柱的实体创建逻辑
                {
                    //矫正控制点再Z方向上的位置
                    foreach (var item in ConPts)
                    {
                        if (item.Z != bot)
                        {
                            item.MoveSelf(new DbPt(0, 0, 1), _view.BotLevel - item.Z);
                        }
                    }

                    foreach (var item in Circles)
                    {
                        if (item.CPt.Z != bot)
                        {
                            item.MoveSelf(new DbPt(0, 0, 1), _view.BotLevel - item.CPt.Z);
                        }
                    }

                    //创建异形柱核心层的实体
                    DbRegion coreRegion = arccolumnregion.EleCopy();
                    coreRegion.ChangeLevel(bot);
                    SolidFace coreSf = new SolidFace(coreRegion);
                    if (!_ifVer) //直柱
                    {
                        columnallsolid.Add(new Solid(coreSf, culumnheight, new DbPt(0, 0, 1), _coreMateria.Alpha, _coreMateria.ColorF, _coreMateria.ColorL) { id = UniqueId, _mat = SolidMaterial.Concrete });
                    }
                    else //斜柱
                    {
                        DbPt vec02 = ConPts[1] - ConPts[0];
                        vec02.Normalize();
                        double verang = GMath.AngleToRadians(_veAngle);
                        double Xdis = culumnheight / Math.Tan(verang);
                        DbPt topCenPt = ConPts[0].EleCopy().Move(vec02, Xdis).Move(0, 0, culumnheight);
                        vec = topCenPt - ConPts[0];
                        vec.Normalize();
                        culumnheight = culumnheight / Math.Sin(verang);
                        columnallsolid.Add(new Solid(coreSf, culumnheight, vec, _coreMateria.Alpha, _coreMateria.ColorF, _coreMateria.ColorL) { id = UniqueId, _mat = SolidMaterial.Concrete });
                    }

                    //计算面层
                    if (_isshowsurface)
                    {
                        if (!_ifedited) //如果没有被编辑过
                        {
                            if (!_ifVer)
                            {
                                cutregion.ChangeLevel(bot);
                                var sfc = new SolidFace(cutregion);
                                //默认面层高度为柱实体高度
                                var surfacedolid = new Solid(sfc, culumnheight, new DbPt(0, 0, 1), _surfaceMateria.Alpha, _surfaceMateria.ColorF, _surfaceMateria.ColorL) { id = UniqueId, _mat = SolidMaterial.GypsumAsbestos }; //把默认面层形成实体
                                columnallsolid.Add(surfacedolid);
                            }
                            else
                            {
                                cutregion.ChangeLevel(bot);
                                var sfc = new SolidFace(cutregion);
                                columnallsolid.Add(new Solid(sfc, culumnheight, vec, _coreMateria.Alpha, _coreMateria.ColorF, _coreMateria.ColorL) { id = UniqueId, _mat = SolidMaterial.Concrete });
                            }
                        }
                        else //如果被编辑过
                        {
                            tempoutlines = new List<DbLine>();
                            if (LinesRecorder.Count > 4)
                            {
                                for (int i = secLoopLines.Count; i < LinesRecorder.Count; i++)
                                {
                                    tempoutlines.Add(LinesRecorder[i]);
                                }
                                loops.AddRange(GMath.GetCwLoopsOfPls(tempoutlines));
                            }
                            if (CircleRecorder.Count > 0)
                            {
                                temCirlines.AddRange(GMath.GetCircleArc(CircleRecorder[0]));
                                loops.AddRange(GMath.GetCwLoopsOfPls(temCirlines));
                            }

                            //把面层和主体的loop合并到一起，形成面域
                            cutregion = new DbRegion(loops);
                            cutregion = GMath.GetSubtractRegion(cutregion, arccolumnregion);
                            cutregion.ChangeLevel(bot);
                            var sfc = new SolidFace(cutregion);
                            var surfacedolid = new Solid(sfc, culumnheight, new DbPt(0, 0, 1), _surfaceMateria.Alpha, _surfaceMateria.ColorF, _surfaceMateria.ColorL) { id = UniqueId, _mat = SolidMaterial.GypsumAsbestos };
                            columnallsolid.Add(surfacedolid); //把编辑过的面层形成实体加入到columnallsolid 
                        }
                    }
                    _coreMateria.SetSolidProperty(columnallsolid[0]);
                    if (_isshowsurface)
                    {
                        _surfaceMateria.SetSolidProperty(columnallsolid[1]);
                    }

                    _solid = TDMath.JoinSolids(columnallsolid, false);



                }
                //_bottomLevel = bot.ToString();
                //_topLevel = top;
                _length = culumnheight;
                _sectionarea = ((double)_transverseWith * (double)_verticalwidth / 1000000).ToString() + "㎡";
                _volume = ((double)_transverseWith * (double)_verticalwidth * (double)_length / 1000000000).ToString() + "m³";
            }
            else
            {
                _solid = Solid.EmptySolid();
            }

        }

        /// <summary>
        /// 图元被双击进入编辑状态时执行的操作
        /// </summary>
        /// <_param name="showEles">图元被双击编辑时需要显示的图元,可根据需要将图元展开并存放到showEles列表中</_param>
        /// <_param name="quickToolBar">图元被双击编辑时可自定义的的快捷工具条</_param>
        /// <returns>true:需要进入图元编辑状态 false：该图元无编辑状态</returns>
        public override bool EleBeginEdit(List<DbElement> showEles, QuickToolBar quickToolBar)
        {
            //base.EleBeginEdit();
            if (!_ifVer)
            {
                //限定只能在大样视图中编辑

                if (_columntype == CoTy.Rectangle)
                {
                    if (!_isshowsurface)
                    {
                        EBDB.Instance.UIGuideDialog("提示", new List<string>() { "请显示面层后再进行编辑" }, new List<string>() { "确定" }); ;
                        return false;
                    }
                    string uril = @"pack://application:,,,/ArchUI;component/Icons/";
                    quickToolBar.AddButton("直线", "EBPlugIn.CreatLine", uril + "F_10.png");
                    quickToolBar.AddButton("弧线", "EBPlugIn.CreatArc", uril + "F_12.png");
                    quickToolBar.AddButton("矩形", "EBPlugIn.CreatRec", uril + "F_16.png");
                    quickToolBar.AddButton("圆", "EBPlugIn.CreatCircle", uril + "F_13.png");
                    //在大样视图中编辑，将面层线条传入，将柱主体线条传入EleEditStorage，能显示但不能编辑
                    recordInLines.Clear();
                    recordOutLines.Clear();
                    recordInCircles.Clear();
                    recordOutCircles.Clear();
                    for (int i = 0; i < 4; i++) //获取柱主体线条
                    {
                        recordInLines.Add(Lines[i]);
                    }

                    for (int i = 4; i < Lines.Count(); i++) //获取柱面层线条
                    {
                        recordOutLines.Add(Lines[i]);
                    }
                    if (recordInLines != null)
                    {
                        recordInLines.ForEach(line => _view.EleEditStorage.Add(line.ToLine()));
                    }

                    if (recordOutLines != null) //把面层线加入显示且能编辑
                    {
                        GFunc.EleDbLineToLine(showEles, recordOutLines);
                    }
                    if (Circles != null) //把面层圆加入编辑
                    {
                        foreach (var item in Circles) { showEles.Add(item.ToCircle()); }
                    }
                    return true;
                }

                else if (_columntype == CoTy.Circle)
                {
                    if (!_isshowsurface)
                    {

                        EBDB.Instance.UIGuideDialog("提示", new List<string>() { "请显示面层后再进行编辑" }, new List<string>() { "确定" }); ;
                        return false;
                    }
                    string uril = @"pack://application:,,,/ArchUI;component/Icons/";
                    quickToolBar.AddButton("直线", "EBPlugIn.CreatLine", uril + "F_10.png");
                    quickToolBar.AddButton("弧线", "EBPlugIn.CreatArc", uril + "F_12.png");
                    quickToolBar.AddButton("矩形", "EBPlugIn.CreatRec", uril + "F_16.png");
                    quickToolBar.AddButton("圆", "EBPlugIn.CreatCircle", uril + "F_13.png");
                    recordInLines.Clear();
                    recordOutLines.Clear();
                    recordInCircles.Clear();
                    recordOutCircles.Clear();
                    if (Circles.Count != 0) //把柱主体圆加入recordInCircles，后续加入_view.EleEditStorage，按理说应该Circles.Count=1
                    {
                        recordInCircles.Add(Circles[0]);
                    }

                    if (Circles.Count > 1)
                    {
                        recordOutCircles.Add((Circles[1]));
                    }

                    if (Lines.Count > 0)
                    {
                        recordOutLines.AddRange(Lines);
                    }

                    _view.EleEditStorage.Add(recordInCircles[0].ToCircle());//把圆柱体截面添加到背景显示，但不能选中或编辑

                    if (recordOutLines != null) //把面层线加入显示且能编辑
                    {
                        GFunc.EleDbLineToLine(showEles, recordOutLines);
                    }
                    if (recordOutCircles != null) //把面层圆加入编辑
                    {
                        foreach (var item in recordOutCircles) { showEles.Add(item.ToCircle()); }
                    }
                    return true;
                }

                else if (_columntype == CoTy.Irregular)
                {
                    _select = EBDB.Instance.UIGuideDialog("编辑异形柱", new List<string> { "编辑异形柱" }, new List<string> { "编辑异形柱截面", "编辑面层" });
                    if (_select == 0)
                    {
                        string uril = @"pack://application:,,,/ArchUI;component/Icons/";
                        quickToolBar.AddButton("直线", "EBPlugIn.CreatLine", uril + "F_10.png");
                        quickToolBar.AddButton("弧线", "EBPlugIn.CreatArc", uril + "F_12.png");
                        quickToolBar.AddButton("矩形", "EBPlugIn.CreatRec", uril + "F_16.png");
                        quickToolBar.AddButton("圆", "EBPlugIn.CreatCircle", uril + "F_13.png");
                        recordInLines.Clear();
                        recordOutLines.Clear();
                        recordInCircles.Clear();
                        recordOutCircles.Clear();
                        //分别获取主体线条、面层线条
                        for (int i = 0; i < secLoopLines.Count; i++)
                        {
                            recordInLines.Add(Lines[i]);
                        }
                        for (int i = secLoopLines.Count; i < Lines.Count; i++)
                        {
                            recordOutLines.Add(Lines[i]);
                        }
                        //把主体线条加入双击编辑视图，但是不可以操作，把面层加入双击编辑视图，可以操作
                        if (recordInLines != null)
                        {
                            recordInLines.ForEach(line => showEles.Add(line.ToLine()));
                        }
                        if (recordOutLines != null)
                        {
                            foreach (var line in recordOutLines)
                            {
                                _view.EleEditStorage.Add(line.ToLine());
                            }
                        }
                        if (Circles != null) //把面层圆加入编辑
                        {
                            foreach (var item in Circles) { _view.EleEditStorage.Add(item.ToCircle()); }
                        }
                        return true;
                    }
                    else if (_select == 1)
                    {
                        if (!_isshowsurface)
                        {
                            EBDB.Instance.UIGuideDialog("提示", new List<string>() { "请显示面层后再进行编辑" }, new List<string>() { "确定" }); ;
                            return false;
                        }
                        string uril = @"pack://application:,,,/ArchUI;component/Icons/";
                        quickToolBar.AddButton("直线", "EBPlugIn.CreatLine", uril + "F_10.png");
                        quickToolBar.AddButton("弧线", "EBPlugIn.CreatArc", uril + "F_12.png");
                        quickToolBar.AddButton("矩形", "EBPlugIn.CreatRec", uril + "F_16.png");
                        quickToolBar.AddButton("圆", "EBPlugIn.CreatCircle", uril + "F_13.png");
                        recordInLines.Clear();
                        recordOutLines.Clear();
                        recordInCircles.Clear();
                        recordOutCircles.Clear();
                        //分别获取主体线条、面层线条
                        for (int i = 0; i < secLoopLines.Count; i++)
                        {
                            recordInLines.Add(Lines[i]);
                        }
                        for (int i = secLoopLines.Count; i < Lines.Count; i++)
                        {
                            recordOutLines.Add(Lines[i]);
                        }
                        //把主体线条加入双击编辑视图，但是不可以操作，把面层加入双击编辑视图，可以操作
                        if (recordInLines != null)
                        {
                            recordInLines.ForEach(line => _view.EleEditStorage.Add(line.ToLine()));
                        }
                        if (recordOutLines != null)
                        {
                            GFunc.EleDbLineToLine(showEles, recordOutLines);
                        }
                        if (Circles != null) //把面层圆加入编辑
                        {
                            foreach (var item in Circles) { showEles.Add(item.ToCircle()); }
                        }
                        return true;
                    }
                }
            }
            else
            {
                EBDB.Instance.UICmdLine("斜柱暂不支持编辑面层", null, null);
                return false;
            }
            return false;
        }


        /// <summary>
        /// 记录编辑过后的线，用于在编辑过后形成实体
        /// </summary>
        List<DbLine> lines2loop = new List<DbLine>();

        /// <summary>
        /// 记录编辑过后的圆，用于在编辑过后形成实体
        /// </summary>
        List<DbCircle> circle2loop = new List<DbCircle>();


        /// <summary>
        /// 结束编辑，回收线和圆
        /// </summary>
        /// <_param name="showEles"></_param>
        /// <returns></returns>
        public override bool EleEndEdit(List<DbElement> showEles)
        {

            //每次编辑开始清空集合，不然每次编辑都会不断地增加值，只在编辑结束的时候记录本次编辑后所有的线和圆，包括未被编辑的线和圆
            LinesRecorder.Clear();
            CircleRecorder.Clear();
            lines2loop.Clear();
            circle2loop.Clear();
            Lines.Clear();
            Circles.Clear();
            //记录编辑过后的线
            List<DbLine> Editedlines = new List<DbLine>();
            //记录编辑过后的圆
            List<DbCircle> Editedcircles = new List<DbCircle>();
            //当为矩形柱时处理
            if (_columntype == CoTy.Rectangle)
            {
                //首先将矩形柱主体线条加入Lines
                Lines.AddRange(recordInLines);
                if (showEles != null)
                {
                    foreach (DbElement ele in showEles)
                    {
                        outloops.Clear();
                        if (ele is Line || ele is Polyline)
                        {
                            Editedlines.AddRange(ele.Lines);
                        }
                        if (ele is Circle)
                        {
                            Editedcircles.AddRange(ele.Circles);
                        }
                    }
                    GMath.LineMergeAll(Editedlines);
                    //添加线围绕而成的环
                    outloops.AddRange(GMath.GetCwLoopsOfPls(Editedlines));
                    //添加圆围绕而成的环
                    foreach (DbCircle item in Editedcircles)
                    {
                        var circleArces = GMath.GetCircleArc(item);
                        var cirloop = GMath.GetCwLoopsOfPls(circleArces);
                        outloops.AddRange(cirloop);
                    }
                    //限定重绘面层结果有且只有一个闭合环，线或者圆形成都可
                    if (outloops.Count() > 1 || outloops.Count() == 0)
                    {
                        EBDB.Instance.UICmdLine("闭合面层边界有且仅能为一个", null, null);
                        return false;
                    }
                    //把线或者圆围合而成的环加入Lines或Circles
                    else
                    {
                        if (Editedlines.Count != 0)
                        {
                            List<DbLine> outloopslines = new List<DbLine>();
                            outloopslines.AddRange(outloops[0].Lines);
                            Lines.AddRange(outloopslines);
                        }

                        else if (Editedcircles.Count != 0)
                        {
                            Circles.Add(Editedcircles[0]);
                        }

                        else
                        {
                            EBDB.Instance.UICmdLine("请在1:50绘图比例下创建一个面层闭合环", null, null);
                            return false;
                        }
                    }
                }
                //把柱主体线条和编辑过后的线条都加入LinesRecorder、CircleRecorder进行记录
                LinesRecorder.AddRange(recordInLines);
                LinesRecorder.AddRange(Editedlines);
                CircleRecorder.AddRange(recordInCircles);
                CircleRecorder.AddRange(Editedcircles);
                lines2loop.AddRange(Editedlines);
                circle2loop.AddRange(Editedcircles);
                Editedlines.Clear();
                Editedcircles.Clear();
                _ifedited = true;
            }

            if (_columntype == CoTy.Circle)
            {
                Circles.Add(recordInCircles[0]);
                if (showEles != null)
                {
                    foreach (DbElement ele in showEles)
                    {
                        if (ele is Line || ele is Polyline)
                        {
                            Editedlines.AddRange(ele.Lines);
                        }
                        if (ele is Circle)
                        {
                            Editedcircles.AddRange(ele.Circles);
                        }
                    }
                    GMath.LineMergeAll(Editedlines);
                    outloops.Clear();
                    //添加线围绕而成的环
                    outloops.AddRange(GMath.GetCwLoopsOfPls(Editedlines));
                    //添加圆围绕而成的环
                    foreach (DbCircle item in Editedcircles)
                    {
                        var circleArces = GMath.GetCircleArc(item);
                        var cirloop = GMath.GetCwLoopsOfPls(circleArces);
                        outloops.AddRange(cirloop);
                    }

                    //限定重绘面层结果有且只有一个闭合环，线或者圆形成都可
                    if (outloops.Count() > 1 || outloops.Count() == 0)
                    {
                        EBDB.Instance.UICmdLine("闭合面层边界有且仅能为一个", null, null);
                        return false;
                    }

                    //把线或者圆围合而成的环加入Lines或Circles
                    else
                    {
                        if (Editedlines.Count != 0)
                        {
                            Lines.AddRange(outloops[0].Lines);
                        }

                        else if (Editedcircles.Count != 0)
                        {
                            Circles.Add(Editedcircles[0]);
                        }
                        else
                        {
                            EBDB.Instance.UICmdLine("请在1:50绘图比例下创建一个面层闭合环", null, null);
                            return false;
                        }
                    }
                }

                //把柱主体线条和编辑过后的线条都加入LinesRecorder、CircleRecorder进行记录
                LinesRecorder.AddRange(Editedlines);
                CircleRecorder.AddRange(recordInCircles);
                CircleRecorder.AddRange(Editedcircles);
                lines2loop.AddRange(Editedlines);
                circle2loop.AddRange(Editedcircles);
                Editedlines.Clear();
                Editedcircles.Clear();
                _ifedited = true;
            }
            if (_columntype == CoTy.Irregular)
            {
                if (_select == 0)
                {
                    if (showEles != null && showEles.Count > 0)
                    {
                        Lines.AddRange(recordOutLines);
                        foreach (DbElement ele in showEles)
                        {
                            outloops.Clear();
                            if (ele is Line || ele is Polyline)
                            {
                                //ele.EleMove_s(-ConPts[0].X, -ConPts[0].Y);
                                Editedlines.AddRange(ele.Lines);
                            }
                            if (ele is Circle)
                            {
                                //ele.EleMove_s(-ConPts[0].X, -ConPts[0].Y);
                                Editedcircles.AddRange(ele.Circles);
                            }
                        }
                        GMath.LineMergeAll(Editedlines);
                        //添加线围绕而成的环
                        outloops.AddRange(GMath.GetCwLoopsOfPls(Editedlines));
                        //添加圆围绕而成的环
                        foreach (DbCircle item in Editedcircles)
                        {
                            var circleArces = GMath.GetCircleArc(item);
                            var cirloop = GMath.GetCwLoopsOfPls(circleArces);
                            outloops.AddRange(cirloop);
                        }
                        //限定重绘面层结果有且只有一个闭合环，线或者圆形成都可
                        if (outloops.Count() > 1 || outloops.Count() == 0)
                        {
                            EBDB.Instance.UICmdLine("闭合面层边界有且仅能为一个", null, null);
                            return false;
                        }
                        //把线或者圆围合而成的环加入Lines或Circles
                        else
                        {
                            outloops[0].MoveSelf(-ConPts[0].X, -ConPts[0].Y);

                            SecType.MatPanelInfos[0] = (outloops[0], SecType.MatPanelInfos[0].Item2);

                            foreach (var ele in EBDB.Instance.ActivView2D.GView.GetElements(new ArcColumn_Filter()))
                            {
                                if (ele is ArcColumn column && column.Columntype == CoTy.Irregular)
                                {
                                    if (column.SecType.UniqueId == secTypeID)
                                    {
                                        column.ActCutCal2D3D();
                                    }
                                }
                            }
                        }


                    }


                }
                else if (_select == 1)
                {
                    //首先将矩形柱主体线条加入Lines
                    Lines.AddRange(recordInLines);
                    if (showEles != null)
                    {
                        foreach (DbElement ele in showEles)
                        {
                            outloops.Clear();
                            if (ele is Line || ele is Polyline)
                            {
                                Editedlines.AddRange(ele.Lines);
                            }
                            if (ele is Circle)
                            {
                                Editedcircles.AddRange(ele.Circles);
                            }
                        }
                        GMath.LineMergeAll(Editedlines);
                        //添加线围绕而成的环
                        outloops.AddRange(GMath.GetCwLoopsOfPls(Editedlines));
                        //添加圆围绕而成的环
                        foreach (DbCircle item in Editedcircles)
                        {
                            var circleArces = GMath.GetCircleArc(item);
                            var cirloop = GMath.GetCwLoopsOfPls(circleArces);
                            outloops.AddRange(cirloop);
                        }
                        //限定重绘面层结果有且只有一个闭合环，线或者圆形成都可
                        if (outloops.Count() > 1 || outloops.Count() == 0)
                        {
                            EBDB.Instance.UICmdLine("闭合面层边界有且仅能为一个", null, null);
                            return false;
                        }
                        //把线或者圆围合而成的环加入Lines或Circles
                        else
                        {
                            if (Editedlines.Count != 0)
                            {
                                List<DbLine> outloopslines = new List<DbLine>();
                                outloopslines.AddRange(outloops[0].Lines);
                                Lines.AddRange(outloopslines);
                            }

                            else if (Editedcircles.Count != 0)
                            {
                                Circles.Add(Editedcircles[0]);
                            }

                            else
                            {
                                EBDB.Instance.UICmdLine("请在1:50绘图比例下创建一个面层闭合环", null, null);
                                return false;
                            }
                        }
                    }
                    //把柱主体线条和编辑过后的线条都加入LinesRecorder、CircleRecorder进行记录
                    LinesRecorder.AddRange(recordInLines);
                    LinesRecorder.AddRange(Editedlines);
                    CircleRecorder.AddRange(recordInCircles);
                    CircleRecorder.AddRange(Editedcircles);
                    lines2loop.AddRange(Editedlines);
                    circle2loop.AddRange(Editedcircles);
                    Editedlines.Clear();
                    Editedcircles.Clear();
                }
                _ifedited = false;
            }


            EleArea = new DbEleArea(this);
            LayerSet2();
            //Activate();
            CalSolid2D();
            CalSolid();
            EBDB.Instance.UIClearSelSets();


            return true;
        }


        /// <summary>
        /// 把DbCircle集合转换为DbElement集合，用于双击编辑
        /// </summary>
        /// <_param name="showEles"></_param>
        /// <_param name="Editcircles"></_param>
        public void EleDbCircleToCircle(List<DbElement> showEles, List<DbCircle> Editcircles)
        {
            if (Editcircles != null)
            {
                foreach (var item in Editcircles)
                {
                    showEles.Add(item.ToCircle());

                    //var cpt = item.CPt1;
                    //var rad = item.Rad1;
                    //var cir = new Circle(cpt, rad);
                    //cir.ActAndCal2D();
                    //showEles.WindowWidthChange(cir);

                    //_view.EleEditStorage.WindowWidthChange(cir);

                }
            }
        }


        /// <summary>
        /// 属性显示控制方法 - 根据柱子类型和状态动态控制属性面板中哪些属性可见
        /// 这是一个重要的用户界面控制方法，让用户只看到相关的属性
        /// </summary>
        /// <param name="propertyName">属性名称</param>
        /// <returns>true=显示该属性，false=隐藏该属性</returns>
        public bool IsPropertyShow(string propertyName)
        {
            switch (propertyName)
            {
                // 这些偏移属性通常不在界面上显示（内部使用）
                case nameof(LeftRec):     // 矩形柱左偏移
                case nameof(RightRec):    // 矩形柱右偏移
                case nameof(TopRec):      // 矩形柱上偏移
                case nameof(BottomRec):   // 矩形柱下偏移
                case nameof(LeftCir):     // 圆形柱左偏移
                case nameof(RightCir):    // 圆形柱右偏移
                case nameof(TopCir):      // 圆形柱上偏移
                case nameof(BottomCir):   // 圆形柱下偏移
                case nameof(Columntype):  // 柱子类型（内部属性）
                    return false;

                // 矩形柱专有属性 - 只有矩形柱才显示
                case nameof(TransverseWith):  // 横向宽度
                    return _columntype == CoTy.Rectangle;

                case nameof(Verticalwidth):   // 纵向宽度
                    return _columntype == CoTy.Rectangle;

                // 圆形柱专有属性 - 只有圆形柱才显示
                case nameof(Radius):          // 半径
                    return _columntype == CoTy.Circle;

                case nameof(Doubleradius):    // 直径
                    return _columntype == CoTy.Circle;

                // 异形柱专有属性 - 只有异形柱才显示
                case nameof(TypeEdit):        // 截面编辑按钮
                    return _columntype == CoTy.Irregular;

                // 斜柱专有属性 - 只有斜柱才显示
                case nameof(VeAngle):         // 倾斜角度
                    return _ifVer == true;

                // 面层相关属性 - 只有启用面层显示时才显示
                case nameof(Surfthick):       // 面层厚度
                    return Isshowsurface;

                case nameof(Surfheight):      // 面层高度
                    return Isshowsurface;

                case nameof(SurfaceMateria):  // 面层材质
                    return Isshowsurface;

                // 其他属性默认都显示
                default: 
                    return true;
            }
        }
        #endregion
    }
