using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Media.Media3D;

/// <summary>
/// 矩管定位点绑定管理器 - 简化版本
/// 功能：管理矩管方案C与独立定位点图元之间的绑定关系
/// 特点：轻量级设计，专注于核心绑定功能
/// 架构：静态管理器，全局访问，线程安全
/// </summary>
public static class TubeLocatorBindingManager
{
    #region 绑定关系存储
    /// <summary>
    /// 绑定关系字典：矩管ID -> 定位点ID列表
    /// </summary>
    private static readonly Dictionary<string, List<string>> _tubeToLocators = new Dictionary<string, List<string>>();

    /// <summary>
    /// 反向绑定关系字典：定位点ID -> 矩管ID
    /// </summary>
    private static readonly Dictionary<string, string> _locatorToTube = new Dictionary<string, string>();

    /// <summary>
    /// 线程锁对象
    /// </summary>
    private static readonly object _lockObject = new object();
    #endregion

    #region 绑定管理方法
    /// <summary>
    /// 创建绑定关系
    /// </summary>
    /// <param name="tubeId">矩管ID</param>
    /// <param name="locatorId">定位点ID</param>
    /// <param name="locatorType">定位点类型</param>
    /// <returns>绑定是否成功</returns>
    public static bool CreateBinding(string tubeId, string locatorId, TubeLocatorPoint.TubeLocatorType locatorType)
    {
        if (string.IsNullOrEmpty(tubeId) || string.IsNullOrEmpty(locatorId))
            return false;

        lock (_lockObject)
        {
            try
            {
                // 如果定位点已经绑定到其他矩管，先解除绑定
                if (_locatorToTube.ContainsKey(locatorId))
                {
                    RemoveBinding(_locatorToTube[locatorId], locatorId);
                }

                // 创建新的绑定关系
                if (!_tubeToLocators.ContainsKey(tubeId))
                {
                    _tubeToLocators[tubeId] = new List<string>();
                }

                _tubeToLocators[tubeId].Add(locatorId);
                _locatorToTube[locatorId] = tubeId;

                // 同步定位点位置
                SyncLocatorPosition(tubeId, locatorId, locatorType);

                return true;
            }
            catch (Exception ex)
            {
                LogError($"创建绑定关系失败: {ex.Message}");
                return false;
            }
        }
    }

    /// <summary>
    /// 移除绑定关系
    /// </summary>
    /// <param name="tubeId">矩管ID</param>
    /// <param name="locatorId">定位点ID</param>
    /// <returns>移除是否成功</returns>
    public static bool RemoveBinding(string tubeId, string locatorId)
    {
        if (string.IsNullOrEmpty(tubeId) || string.IsNullOrEmpty(locatorId))
            return false;

        lock (_lockObject)
        {
            try
            {
                // 移除正向绑定
                if (_tubeToLocators.ContainsKey(tubeId))
                {
                    _tubeToLocators[tubeId].Remove(locatorId);
                    if (_tubeToLocators[tubeId].Count == 0)
                    {
                        _tubeToLocators.Remove(tubeId);
                    }
                }

                // 移除反向绑定
                _locatorToTube.Remove(locatorId);

                return true;
            }
            catch (Exception ex)
            {
                LogError($"移除绑定关系失败: {ex.Message}");
                return false;
            }
        }
    }

    /// <summary>
    /// 获取矩管的所有定位点
    /// </summary>
    /// <param name="tubeId">矩管ID</param>
    /// <returns>定位点ID列表</returns>
    public static List<string> GetTubeLocators(string tubeId)
    {
        if (string.IsNullOrEmpty(tubeId))
            return new List<string>();

        lock (_lockObject)
        {
            return _tubeToLocators.ContainsKey(tubeId) ? 
                   new List<string>(_tubeToLocators[tubeId]) : 
                   new List<string>();
        }
    }

    /// <summary>
    /// 获取定位点绑定的矩管
    /// </summary>
    /// <param name="locatorId">定位点ID</param>
    /// <returns>矩管ID，如果未绑定则返回空字符串</returns>
    public static string GetLocatorTube(string locatorId)
    {
        if (string.IsNullOrEmpty(locatorId))
            return "";

        lock (_lockObject)
        {
            return _locatorToTube.ContainsKey(locatorId) ? _locatorToTube[locatorId] : "";
        }
    }

    /// <summary>
    /// 检查绑定关系是否存在
    /// </summary>
    /// <param name="tubeId">矩管ID</param>
    /// <param name="locatorId">定位点ID</param>
    /// <returns>绑定关系是否存在</returns>
    public static bool IsBindingExists(string tubeId, string locatorId)
    {
        if (string.IsNullOrEmpty(tubeId) || string.IsNullOrEmpty(locatorId))
            return false;

        lock (_lockObject)
        {
            return _locatorToTube.ContainsKey(locatorId) && _locatorToTube[locatorId] == tubeId;
        }
    }

    /// <summary>
    /// 清理指定矩管的所有绑定关系
    /// </summary>
    /// <param name="tubeId">矩管ID</param>
    public static void ClearTubeBindings(string tubeId)
    {
        if (string.IsNullOrEmpty(tubeId))
            return;

        lock (_lockObject)
        {
            if (_tubeToLocators.ContainsKey(tubeId))
            {
                // 移除所有反向绑定
                foreach (string locatorId in _tubeToLocators[tubeId])
                {
                    _locatorToTube.Remove(locatorId);
                }

                // 移除正向绑定
                _tubeToLocators.Remove(tubeId);
            }
        }
    }

    /// <summary>
    /// 清理指定定位点的绑定关系
    /// </summary>
    /// <param name="locatorId">定位点ID</param>
    public static void ClearLocatorBinding(string locatorId)
    {
        if (string.IsNullOrEmpty(locatorId))
            return;

        lock (_lockObject)
        {
            if (_locatorToTube.ContainsKey(locatorId))
            {
                string tubeId = _locatorToTube[locatorId];
                RemoveBinding(tubeId, locatorId);
            }
        }
    }
    #endregion

    #region 位置同步方法
    /// <summary>
    /// 同步定位点位置到矩管
    /// </summary>
    /// <param name="tubeId">矩管ID</param>
    /// <param name="locatorId">定位点ID</param>
    /// <param name="locatorType">定位点类型</param>
    public static void SyncLocatorPosition(string tubeId, string locatorId, TubeLocatorPoint.TubeLocatorType locatorType)
    {
        try
        {
            // 获取矩管图元
            var tube = GetTubeElement(tubeId);
            var locator = GetLocatorElement(locatorId);

            if (tube == null || locator == null)
                return;

            // 计算定位点应该在的位置
            DbPt targetPosition = CalculateLocatorPosition(tube, locatorType, locator.OffsetX, locator.OffsetY);

            // 更新定位点位置
            locator.SetPosition(targetPosition);
        }
        catch (Exception ex)
        {
            LogError($"同步定位点位置失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 同步矩管位置到定位点
    /// </summary>
    /// <param name="locatorId">定位点ID</param>
    /// <param name="newPosition">定位点新位置</param>
    public static void SyncTubePosition(string locatorId, DbPt newPosition)
    {
        try
        {
            string tubeId = GetLocatorTube(locatorId);
            if (string.IsNullOrEmpty(tubeId))
                return;

            var tube = GetTubeElement(tubeId);
            var locator = GetLocatorElement(locatorId);

            if (tube == null || locator == null)
                return;

            // 根据定位点类型计算矩管应该移动到的位置
            DbPt tubeTargetPosition = CalculateTubePosition(tube, locator, newPosition);

            // 计算移动向量
            DbPt currentCenter = tube._centerPt;
            DbPt moveVector = new DbPt(tubeTargetPosition.X - currentCenter.X, tubeTargetPosition.Y - currentCenter.Y);

            // 移动矩管
            tube.EleMove(moveVector.X, moveVector.Y);
        }
        catch (Exception ex)
        {
            LogError($"同步矩管位置失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 计算定位点应该在的位置
    /// </summary>
    /// <param name="tube">矩管图元</param>
    /// <param name="locatorType">定位点类型</param>
    /// <param name="offsetX">X偏移</param>
    /// <param name="offsetY">Y偏移</param>
    /// <returns>定位点位置</returns>
    private static DbPt CalculateLocatorPosition(SteelTubePlanC tube, TubeLocatorPoint.TubeLocatorType locatorType, double offsetX, double offsetY)
    {
        DbPt center = tube._centerPt;
        double halfWidth = tube.Width / 2.0;
        double halfHeight = tube.Height / 2.0;

        DbPt basePosition;

        // 根据类型计算基础位置
        switch (locatorType)
        {
            case TubeLocatorPoint.TubeLocatorType.TopCenter:
                basePosition = new DbPt(center.X, center.Y + halfHeight);
                break;
            case TubeLocatorPoint.TubeLocatorType.BottomCenter:
                basePosition = new DbPt(center.X, center.Y - halfHeight);
                break;
            case TubeLocatorPoint.TubeLocatorType.LeftCenter:
                basePosition = new DbPt(center.X - halfWidth, center.Y);
                break;
            case TubeLocatorPoint.TubeLocatorType.RightCenter:
                basePosition = new DbPt(center.X + halfWidth, center.Y);
                break;
            case TubeLocatorPoint.TubeLocatorType.GeometryCenter:
                basePosition = center.EleCopy();
                break;
            default:
                basePosition = center.EleCopy();
                break;
        }

        // 应用偏移
        basePosition.X += offsetX;
        basePosition.Y += offsetY;

        // 应用旋转变换
        if (Math.Abs(tube.HoAngle) > 0.001)
        {
            double angleRad = tube.HoAngle * Math.PI / 180.0;
            basePosition.RotateSelf(center, angleRad);
        }

        return basePosition;
    }

    /// <summary>
    /// 计算矩管应该移动到的位置
    /// </summary>
    /// <param name="tube">矩管图元</param>
    /// <param name="locator">定位点图元</param>
    /// <param name="newLocatorPosition">定位点新位置</param>
    /// <returns>矩管新的中心位置</returns>
    private static DbPt CalculateTubePosition(SteelTubePlanC tube, TubeLocatorPoint locator, DbPt newLocatorPosition)
    {
        double halfWidth = tube.Width / 2.0;
        double halfHeight = tube.Height / 2.0;

        // 计算不含偏移的定位点位置
        DbPt locatorBasePosition = new DbPt(newLocatorPosition.X - locator.OffsetX, newLocatorPosition.Y - locator.OffsetY);

        DbPt newTubeCenter;

        // 根据定位点类型计算矩管中心位置
        switch (locator.LocatorType)
        {
            case TubeLocatorPoint.TubeLocatorType.TopCenter:
                newTubeCenter = new DbPt(locatorBasePosition.X, locatorBasePosition.Y - halfHeight);
                break;
            case TubeLocatorPoint.TubeLocatorType.BottomCenter:
                newTubeCenter = new DbPt(locatorBasePosition.X, locatorBasePosition.Y + halfHeight);
                break;
            case TubeLocatorPoint.TubeLocatorType.LeftCenter:
                newTubeCenter = new DbPt(locatorBasePosition.X + halfWidth, locatorBasePosition.Y);
                break;
            case TubeLocatorPoint.TubeLocatorType.RightCenter:
                newTubeCenter = new DbPt(locatorBasePosition.X - halfWidth, locatorBasePosition.Y);
                break;
            case TubeLocatorPoint.TubeLocatorType.GeometryCenter:
                newTubeCenter = locatorBasePosition.EleCopy();
                break;
            default:
                newTubeCenter = locatorBasePosition.EleCopy();
                break;
        }

        return newTubeCenter;
    }
    #endregion

    #region 智能拉伸方法
    /// <summary>
    /// 执行智能拉伸
    /// </summary>
    /// <param name="locatorId">定位点ID</param>
    /// <param name="stretchVector">拉伸向量</param>
    public static void PerformSmartStretch(string locatorId, DbPt stretchVector)
    {
        try
        {
            string tubeId = GetLocatorTube(locatorId);
            if (string.IsNullOrEmpty(tubeId))
                return;

            var tube = GetTubeElement(tubeId);
            var locator = GetLocatorElement(locatorId);

            if (tube == null || locator == null)
                return;

            // 根据定位点类型执行不同的拉伸逻辑
            switch (locator.LocatorType)
            {
                case TubeLocatorPoint.TubeLocatorType.TopCenter:
                case TubeLocatorPoint.TubeLocatorType.BottomCenter:
                    // 垂直拉伸：修改高度
                    PerformVerticalStretch(tube, locator, stretchVector);
                    break;

                case TubeLocatorPoint.TubeLocatorType.LeftCenter:
                case TubeLocatorPoint.TubeLocatorType.RightCenter:
                    // 水平拉伸：修改宽度
                    PerformHorizontalStretch(tube, locator, stretchVector);
                    break;

                case TubeLocatorPoint.TubeLocatorType.GeometryCenter:
                    // 中心点：整体移动
                    SyncTubePosition(locatorId, locator.Position);
                    break;
            }
        }
        catch (Exception ex)
        {
            LogError($"智能拉伸失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 执行垂直拉伸
    /// </summary>
    /// <param name="tube">矩管图元</param>
    /// <param name="locator">定位点图元</param>
    /// <param name="stretchVector">拉伸向量</param>
    private static void PerformVerticalStretch(SteelTubePlanC tube, TubeLocatorPoint locator, DbPt stretchVector)
    {
        // 计算垂直方向的拉伸量
        double verticalStretch = stretchVector.Y;

        // 根据定位点类型决定拉伸方向
        if (locator.LocatorType == TubeLocatorPoint.TubeLocatorType.TopCenter)
        {
            // 上边中点：向上拉伸增加高度
            if (verticalStretch > 0)
            {
                tube.Height = Math.Max(10, tube.Height + Math.Abs(verticalStretch));
            }
        }
        else if (locator.LocatorType == TubeLocatorPoint.TubeLocatorType.BottomCenter)
        {
            // 下边中点：向下拉伸增加高度
            if (verticalStretch < 0)
            {
                tube.Height = Math.Max(10, tube.Height + Math.Abs(verticalStretch));
            }
        }
    }

    /// <summary>
    /// 执行水平拉伸
    /// </summary>
    /// <param name="tube">矩管图元</param>
    /// <param name="locator">定位点图元</param>
    /// <param name="stretchVector">拉伸向量</param>
    private static void PerformHorizontalStretch(SteelTubePlanC tube, TubeLocatorPoint locator, DbPt stretchVector)
    {
        // 计算水平方向的拉伸量
        double horizontalStretch = stretchVector.X;

        // 根据定位点类型决定拉伸方向
        if (locator.LocatorType == TubeLocatorPoint.TubeLocatorType.LeftCenter)
        {
            // 左边中点：向左拉伸增加宽度
            if (horizontalStretch < 0)
            {
                tube.Width = Math.Max(10, tube.Width + Math.Abs(horizontalStretch));
            }
        }
        else if (locator.LocatorType == TubeLocatorPoint.TubeLocatorType.RightCenter)
        {
            // 右边中点：向右拉伸增加宽度
            if (horizontalStretch > 0)
            {
                tube.Width = Math.Max(10, tube.Width + Math.Abs(horizontalStretch));
            }
        }
    }
    #endregion

    #region 辅助方法
    /// <summary>
    /// 获取矩管图元
    /// </summary>
    /// <param name="tubeId">矩管ID</param>
    /// <returns>矩管图元，如果不存在则返回null</returns>
    private static SteelTubePlanC GetTubeElement(string tubeId)
    {
        try
        {
            // 这里需要通过图元管理系统获取图元
            // 具体实现取决于平台的图元管理机制
            // 暂时返回null，实际使用时需要实现
            return null;
        }
        catch (Exception ex)
        {
            LogError($"获取矩管图元失败: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 获取定位点图元
    /// </summary>
    /// <param name="locatorId">定位点ID</param>
    /// <returns>定位点图元，如果不存在则返回null</returns>
    private static TubeLocatorPoint GetLocatorElement(string locatorId)
    {
        try
        {
            // 这里需要通过图元管理系统获取图元
            // 具体实现取决于平台的图元管理机制
            // 暂时返回null，实际使用时需要实现
            return null;
        }
        catch (Exception ex)
        {
            LogError($"获取定位点图元失败: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 记录错误日志
    /// </summary>
    /// <param name="message">错误消息</param>
    private static void LogError(string message)
    {
        // 这里应该记录到系统日志
        // 具体实现取决于平台的日志系统
        Console.WriteLine($"[TubeLocatorBindingManager] 错误: {message}");
    }

    /// <summary>
    /// 清理所有绑定关系
    /// </summary>
    public static void ClearAllBindings()
    {
        lock (_lockObject)
        {
            _tubeToLocators.Clear();
            _locatorToTube.Clear();
        }
    }

    /// <summary>
    /// 获取绑定关系统计信息
    /// </summary>
    /// <returns>绑定关系统计信息</returns>
    public static string GetBindingStatistics()
    {
        lock (_lockObject)
        {
            int tubeCount = _tubeToLocators.Count;
            int locatorCount = _locatorToTube.Count;
            int totalBindings = _locatorToTube.Count;

            return $"矩管数量: {tubeCount}, 定位点数量: {locatorCount}, 绑定关系总数: {totalBindings}";
        }
    }
    #endregion
} 