﻿using EB3DDB;
using EB3DMath;
using EBCore;
using GuiDB;
using GuiFunction;
using GuiMath;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Controls;




namespace DemoElement
{
    /// <summary>
    /// 示例墙扩展
    /// </summary>
    [Serializable] //序列化标识，所有图元必须有这个标签
    [DbElement("示例墙扩展2", MajorType.Str)] //图元中文名称标签
    public class DemoWallComp2 : DbElement
    {
        /// <summary>
        /// 墙截面类型Uid,必要，采用类型对墙体截面数据进行管理时，墙的实例中必须记录类型Uid
        /// </summary>
        public string DemoSecTypeUid = "";

        /// <summary>
        /// 墙截面类型，类型数据不在墙实例中进行保存，只在项目加载时建立引用关系，类型数据统一存放到：Project.ElementTypes列表中
        /// 平台会自动对Project.ElementTypes列表中的数据进行保存和读取
        /// </summary>
        private DemoSecType _demoSecType = null;
        /// <summary>
        /// 墙截面类型，在对类型进行赋值时，应确保类型已在Project.ElementTypes列表中
        /// </summary>
        [Browsable(false)] //默认情况下，属性栏会显示所有属性数据，使用该标签可以手动关闭属性栏对该属性的显示
        public DemoSecType DemoSecType
        {
            get { return _demoSecType; }
            set
            {
                //剔除无效的赋值可以减少算量
                if (_demoSecType != null && _demoSecType.UniqueId == value.UniqueId) { return; }

                TransManager.Instance().Push(a => DemoSecType = a, _demoSecType);
                _demoSecType = value;

                //记录墙截面类型Uid，确保在墙的截面类型更新后，Uid也是最新的
                DemoSecTypeUid = value.UniqueId;

                //ActCutCal2D3D方法的算量较大，在使用该方法前请确保明白该方法使用的场景和效果
                ActCutCal2D3D();
            }
        }

        /// <summary>
        /// 宽度(为了让墙体能在属性栏显示宽度信息，这里依然保留了宽度的属性，只是宽度是从类型数据中获取的)
        /// </summary>
        [Category("几何数据"), DisplayName("宽度"), Description("墙体宽度"), ReadOnly(false)]
        public int Width
        {
            get { return DemoSecType.Width; }
        }

        /// <summary>
        /// 墙类别
        /// </summary>
        private DemoWallType _demoWallType = DemoWallType.ShearWall;
        /// <summary>
        /// 墙类别
        /// </summary>
        [Category("几何数据"), DisplayName("墙类别"), Description("墙类别"), ReadOnly(false), Editor(typeof(EnumEditor), typeof(EnumEditor))]
        public DemoWallType DemoWallType
        {
            get { return _demoWallType; }
            set
            {
                TransManager.Instance().Push(a => DemoWallType = a, _demoWallType);
                _demoWallType = value;
            }
        }

        /// <summary>
        /// 命令按钮-点击运行指定命令
        /// </summary>
        [Category("快捷操作"), DisplayName("命令按钮1"), Description("点击运行指定命令"), ReadOnly(false)]
        [Editor(typeof(MyBtn), typeof(MyBtn))]
        public MyEvent CmdBtton1 { get { return () => EBDB.Instance.CmdRun("DemoCMD.HelloEasyBIM"); } set { } }

        /// <summary>
        /// 命令按钮-点击运行指定方法
        /// </summary>
        [Category("快捷操作"), DisplayName("命令按钮2"), Description("点击运行指定方法"), ReadOnly(false)]
        [Editor(typeof(MyBtn), typeof(MyBtn))]
        public MyEvent CmdBtton2
        {
            get
            {
                return () =>
                {
                    System.Windows.Forms.MessageBox.Show("方法运行成功！");
                };
            }
            set { }
        }

        /// <summary>
        /// 属性栏自定义控件
        /// </summary>
        [Category("快捷操作"), DisplayName("自定义控件"), Description("演示在属性栏显示自定义控件"), ReadOnly(false)]
        [Editor(typeof(CustomControl), typeof(CustomControl))]
        public string ViewTemplateControl { get; set; }


        /// <summary>
        /// 无参构造函数，每个图元必须保留无参构造函数
        /// </summary>
        public DemoWallComp2()
        {
            //每个图元必须在图元中定义是否为三维
            _if3D = true;
        }

        /// <summary>
        /// 创建示例墙
        /// </summary>
        /// <param name="ptSt">墙起点</param>
        /// <param name="ptEnd">墙终点</param>
        /// <param name="demoSecType">墙体类型,确保墙截面已在Project.ElementTypes列表中</param>
        public DemoWallComp2(DbPt ptSt, DbPt ptEnd, DemoSecType demoSecType)
        {
            //指定图元为三维图元，必须
            _if3D = true;

            //记录图元的控制点，必须采用深度复制
            //直接使用 ConPts.Add(ptSt) 可能出现ptSt在外部被修改从而导致图元数据异常
            ConPts.Add(ptSt.EleCopy());
            ConPts.Add(ptEnd.EleCopy());

            //通过输入的两点形成图元中点，并存入ConPts列表中
            DbPt ptMid = GMath.GetMidPt(ptSt, ptEnd);

            //为中点设置一个标识，方便识别点的PtType参数的意义由图元自行定义
            ptMid.PtType = 3;
            ConPts.Add(ptMid);

            //在图元内部只能对私有字段进行赋值，不能对公有属性进行赋值
            _demoSecType = demoSecType;
            DemoSecTypeUid = demoSecType.UniqueId;
        }

        /// <summary>
        /// 保存数据
        /// </summary>
        /// <param name="binaryWriter">写入流</param>
        public override void DataSave(BinaryWriter binaryWriter)
        {
            //图元类标识，必要
            binaryWriter.Write(this.GetType().ToString());

            //版本号，必要，每次版本的升级必须注明时间和变更内容
            //0版：2024.10.30 新增
            binaryWriter.Write(0);

            //图元共有参数，必要，该方法对DbElement类的公共数据进行了保存
            PubSave(binaryWriter);

            //分界线之上的代码为每个图元的固定格式，不能修改
            //-------分界线---------
            //分界线之下的代码为每个图元特有参数，根据图元的需要确定参数是否保存
            //在进行数据保存时，应确保保存的参数尽可能精简

            //图元特有参数,根据每个图元情况进行保存
            binaryWriter.Write(DemoSecTypeUid);
        }

        /// <summary>
        /// 装载数据
        /// </summary>
        /// <param name="binaryReader">读取流</param>
        public override void DataLoad(BinaryReader binaryReader)
        {
            //图元数据的读顺序必须与图元的写顺序完全一致
            //图元的类标识平台已经读取了，在图元中不需要再读
            //在图元的读写方法中不能使用try/catch对错误进行捕捉，
            //读写中不能容忍出现未知的异常

            //每个图元的版本号是相互独立的，并且与软件整体的版本号无关
            //版本号的设定是为了解决软件发布后，在类数据变化时，高版本能兼容低版本的问题
            //在写图元时，必须完全理解版本号的运作逻辑
            //对版本号的理解如果存在偏差，可能在软件发布后导致用户数据丢失等严重后果！

            //读取版本号，必要
            int VerNum = binaryReader.ReadInt32();

            //每个版本号的读写都必须保留
            if (VerNum == 0)
            {
                #region
                //图元共有参数，必要，该方法对DbElement类的公共数据进行了读取
                PubLoad(binaryReader);

                //图元特有参数
                DemoSecTypeUid = binaryReader.ReadString();
                #endregion
            }
        }

        /// <summary>
        /// 返回图元的一个备份(深度复制)，每个图元必须重写该方法
        /// </summary>
        /// <param name="changeUid">深度复制时是否改变 UniqueId true：改变 false：不改变</param>
        /// <returns>新的图元</returns>
        public override DbElement EleCopy(bool changeUid = false)
        {
            //在C#中，两个类如果直接赋值，传递的是引用，两个类本身均指向同一地址，
            //修改任意一个，两个都会被修改
            //图元深度复制的原理是将图元的每个参数深入到值类型进行赋值，
            //确保深度复制后的图元与原图元可以独立修改而不互相影响

            DemoWallComp2 ele = new DemoWallComp2();

            //图元共有参数，必要
            PubCopy(ele);

            //图元特有参数
            ele.DemoSecTypeUid = DemoSecTypeUid;
            ele._demoSecType = _demoSecType;

            if (changeUid) { ele.UniqueId = Guid.NewGuid().ToString(); }//改变Uid
            return ele;
        }

        /// <summary>
        /// 激活，图元激活的本质是通过“控制点”+“参数”计算出所有构成图元的点、线、文字、填充等基本要素
        /// </summary>
        public override void Activate()
        {
            //在图元被移动、拉伸等变化时，图元的动画是通过不断的调用激活函数形成的，
            //因此在每次激活时，需要清空之前计算存储的数据
            //图元的显示要素存储的列表包括：Lines、Texts、Circles、Hatchs四个结合，可以根据图元需要进行选用
            //本图元只涉及对“线”的存储，所以只需要处理线的列表
            Lines.Clear();

            //通过图元的控制点+参数计算出构成图元的线
            //控制点存放在ConPts列表中，一般在构造函数中进行赋值，图元的设计者需要清楚每个控制点的意义

            //墙体中心线
            DbLine cline = new DbLine(ConPts[0], ConPts[1]);

            //通过墙体中线向左偏移半宽形成墙体左侧的线
            DbLine lineL = GMath.LineOffset(cline, true, _demoSecType.Width / 2);
            //添加到图元的线集合中
            Lines.Add(lineL);

            //通过墙体中心线向右偏移半宽形成墙体右侧侧的线
            DbLine lineR = GMath.LineOffset(cline, false, _demoSecType.Width / 2);
            //添加到图元的线集合中
            Lines.Add(lineR);

            //墙体起始端封口线
            Lines.Add(new DbLine(lineL.PtSt, lineR.PtSt));

            //墙体终止端封口线
            Lines.Add(new DbLine(lineL.PtEnd, lineR.PtEnd));

            //计算墙体抽象区域，必要，抽象区域是图元的关系要素，
            //抽象区域的主要作用是用于快速判断两个图元或矩形与图元的相交关系
            //抽象区域对相交关系的判断是模糊判断，不是精确判断
            //抽象区域判断相交的核心逻辑是快速剔除明显不相交的图元
            EleArea = new DbEleArea(this);
        }

        /// <summary>
        /// 通过图元中心线获取图元的底部轮廓点
        /// </summary>
        /// <param name="z">轮廓点底部Z坐标</param>
        /// <returns>图元轮廓点</returns>
        private List<DbPt> GetPts(double z = 0)
        {
            DbLine cline = new DbLine(ConPts[0], ConPts[1]);
            DbLine lineL = GMath.LineOffset(cline, true, _demoSecType.Width / 2);
            DbLine lineR = GMath.LineOffset(cline, false, _demoSecType.Width / 2);
            List<DbPt> pts = new List<DbPt>();
            pts.Add(new DbPt(lineL.PtSt.X, lineL.PtSt.Y, z));
            pts.Add(new DbPt(lineL.PtEnd.X, lineL.PtEnd.Y, z));
            pts.Add(new DbPt(lineR.PtEnd.X, lineR.PtEnd.Y, z));
            pts.Add(new DbPt(lineR.PtSt.X, lineR.PtSt.Y, z));
            return pts;
        }

        /// <summary>
        /// 计算三维实体
        /// </summary>
        public override void CalSolid()
        {
            //构建墙体底部轮廓点集
            List<DbPt> pts = GetPts(_view.BotLevel);

            //通过拉升体创建墙体三维
            _solid = new Solid(pts, _view.TopLevel - _view.BotLevel, new DbPt(0, 0, 1));

            //EasyBIM中提供了多种三维形体的构建接口，包括：
            //拉伸、融合、旋转、放样、放样融合、布尔等
            //通过上述接口，可以构建任意三维形体
            //三维形体的构建逻辑可参考EasyBIM的族编辑器，族编辑器中能创建的形体，都可以通过接口进行创建
        }

        /// <summary>
        /// 返回图元被单独选中时显示的提示内容
        /// </summary>
        /// <param name="str1">提示文字1</param>
        /// <param name="str2">提示文字2</param>
        public override void EleTips(out string str1, out string str2)
        {
            str1 = "示例墙";
            str2 = _demoSecType.GetSecName();
        }

        /// <summary>
        /// 当图元使用了类型时，通过该方法建立图元与类型的关联
        /// </summary>
        /// <returns>关联成功时返回null;关联失败时返回在执行LinkElementType操作前已关联的类型(用于协同时反补被创建者删除的类型)</returns>
        public override DbElementType LinkElementType() 
        {
            //在项目保存时，平台会自动保存 Project.ElementTypes中的所有类型数据
            //由于图元不会直接保存类型的数据，只是保存了类型的Uid,因此使用了类型的图元需要在项目打开时，建立图元与类型的关联
            //在平台调用该方法时，类型的数据已经读取完成，可以直接遍历整个类型列表进行查找
            //EBDB.Instance 是EasyBIM核心库EbDb的静态全局唯一实例，通过EBDB.Instance可以在任意地方访问EasyBIM的核心库
            foreach (DbElementType elementType in EBDB.Instance.GProject.ElementTypes)
            {
                if(elementType.UniqueId == DemoSecTypeUid)
                {
                    //将找到的类型赋值给图元的 _demoSecType 即建立图元与类型的关联
                    _demoSecType = elementType as DemoSecType;
                    break;
                }
            }

            //关联失败返回原类型(构件级协同时可能会发生这种情况，返回原来已关联的类型即可)
            return _demoSecType;
        }

        /// <summary>
        /// 返回单独选中图元时在属性栏下拉框中需要显示的内容
        /// </summary>
        /// <returns></returns>
        public override List<string> ComboTips()
        { 
            //在这里返回图元的类型列表，让用户可以通过切换列表的选中项对墙体类型进行切换
            List<string> sercs = new List<string> ();
            foreach(DbElementType elementType in EBDB.Instance.GProject.ElementTypes)
            {
                if(elementType is DemoSecType)
                {
                    sercs.Add(elementType.GetSecName());
                }
            }
            return sercs;
        }

        /// <summary>
        /// 属性栏下拉框选中项发生变化
        /// </summary>
        /// <param name="newIndex">新选中项的序号</param>
        public override void ComboTipsSelectionChanged(int newIndex) 
        {
            int count = 0;
            foreach (DbElementType elementType in EBDB.Instance.GProject.ElementTypes)
            {
                if (elementType is DemoSecType)
                {
                    if(count == newIndex)
                    {
                        DemoSecType = elementType as DemoSecType;
                        break;
                    }
                    count++;
                }
            }
        }

        /// <summary>
        /// 刷新图元在拉伸后的数据
        /// </summary>
        /// <param name="X">X向平移量</param>
        /// <param name="Y">Y向平移量</param>
        public override void EleMove_s(double X, double Y)
        {
            //对于拉伸操作，被用户拉伸到的点，其Status将被设置为1，未被拉伸到的点Status为0，通过判断Status的值，可以确定点是否被拉伸
            //ConPts中，每个控制点的意义有图元的设计者定义，并且需要记住
            if (ConPts[2].Status == 1)
            {
                foreach (DbPt pt in ConPts)//如果中点被拉伸，则将所有点全部平移 
                {
                    pt.MoveSelf(X, Y);
                }
            }
            else
            {
                if (ConPts[0].Status == 1) { ConPts[0].MoveSelf(X, Y); }
                if (ConPts[1].Status == 1) { ConPts[1].MoveSelf(X, Y); }
                ConPts[2].X = (ConPts[0].X + ConPts[1].X) / 2;
                ConPts[2].Y = (ConPts[0].Y + ConPts[1].Y) / 2;
            }

            //控制点修改完成后调用激活函数，重新计算图元数据
            Activate();
        }

        /// <summary>
        /// 图元三维控制点被拖动后的数据，三维控制点，只支持单控制点拖动
        /// </summary>
        /// <param name="ConPts3D">通过 ConPts3D()函数返回的三维控制点</param>
        /// <param name="X">X向平移量</param>
        /// <param name="Y">Y向平移量</param>
        /// <param name="Z">Z向平移量</param>
        public override void EleMove_s3D(List<DbPt> ConPts3D, double X, double Y, double Z)
        {
            if (ConPts[2].Status == 1)
            {
                foreach (DbPt pt in ConPts)//如果中点被拉伸，则将所有点全部平移 
                {
                    pt.MoveSelf(X, Y, Z);
                }
            }
            else
            {
                if (ConPts[0].Status == 1) { ConPts[0].MoveSelf(X, Y, Z); }
                if (ConPts[1].Status == 1) { ConPts[1].MoveSelf(X, Y, Z); }
                ConPts[2].X = (ConPts[0].X + ConPts[1].X) / 2;
                ConPts[2].Y = (ConPts[0].Y + ConPts[1].Y) / 2;
                ConPts[2].Z = (ConPts[0].Z + ConPts[1].Z) / 2;
            }
            Activate();
        }

        /// <summary>
        /// 图元被选中时执行的附加操作
        /// </summary>
        public override void EleBeSel()
        {
            DbText dbText = new DbText(Lines[0].PtMid, 0, 3, Width.ToString(), 0, 2);

            //如果新加的文字需要与Texts列表中的其他文字进行区分，可以设置文字的Tag值，该值由图元的设计者进行维护和解释
            //在本示例中可以不需要
            //dbText.Tag = 1;

            //计算文字的外部矩形，必要，不调用该方法，文字在双击编辑时显示的编辑框可能异常
            dbText.CalOutRec(ViewSc);

            //将文字添加到图元的显示要素Texts列表中
            Texts.Add(dbText);

            //注意，这里由于临时向图元中添加了文字，导致图元EleArea发生变化，需要重新计算，否则，会导致图元的选中行为异常
            this.EleArea = new DbEleArea(this);

            //计算图元用于二维显示的实体，必要
            CalSolid2D();

            //注意，这里不需要调用Activate方法，因为Activate的本质是计算图元的显示要素，并记录到列表中，
            //这里的操作已经在操作显示要素了，因此，不需要重新调用激活函数
        }

        /// <summary>
        /// 图元被取消选中时执行的附加操作
        /// </summary>
        public override void EleBeUnSel()
        {
            //由于该图元Texts列表中没有其他内容，因此可以在取消选中时直接清空，
            Texts.Clear();

            //临时文字被移除后，重新计算EleArea
            this.EleArea = new DbEleArea(this);

            //计算图元用于二维显示的实体，必要
            CalSolid2D();
        }

        /// <summary>
        /// 图元被删除时执行的附加操作
        /// </summary>
        public override void EleBeDel()
        {
            //检查项目中是否还有其他图元使用了与当前图元相同的类型
            bool ifhave = false;
            foreach (View view in EBDB.Instance.GProject.StanViews)
            {
                if (ifhave) { break; }
                foreach (DbElement ele in view.Floor.Elements)
                {
                    if (ele is DemoWallComp2 demoWall && demoWall.DemoSecTypeUid == this.DemoSecTypeUid)
                    {
                        ifhave = true;
                        break;
                    }
                }
            }

            if (ifhave == false)
            {
                if (EBDB.Instance.GProject.ElementTypes.Contains_uid(this.DemoSecTypeUid))
                {
                    //注意，移除指定类型必须调用RemoveEleType，否则无法回退
                    EBDB.Instance.GProject.RemoveEleType(this.DemoSecType);
                }
            }
        }

        /// <summary>
        /// 图元被复制时执行的附加操作
        /// </summary>
        public override void EleBeCopy()
        {
            bool ifhave = false;
            foreach(DbElementType elementType in EBDB.Instance.GProject.ElementTypes)
            {
                if(this.DemoSecType.IfSame(elementType))
                {
                    //注意，这里即使是相同的截面类型，也需要重新使用当前项目中的截面类型对_demoSecType进行赋值
                    //EleBeCopy在两种情况下会被调用，一是使用Copy命令复制图元后，另一种情况是使用Ctrl+C复制图元，这两种情况运作逻辑是不同的
                    //使用Copy命令时，如果用户重写了EleCopy方法，则复制后的类型可能是从项目的类型表中引用来的，项目中的类型参数发生修改，所有的引用也将修改，这种情况是没有问题的
                    //使用Ctrl+C复制时，采用的是序列化方式对图元进行赋值，图元的类型参数也将被序列化复制，这将使得该类型与项目类型表中的对应类型不是引用关系，这种情况下，项目的类型参数修改后，图元不会修改
                    //因此即使判断出是相同的，也需要重新赋值，确保该类型是引用自项目类型表
                    this._demoSecType = elementType as DemoSecType;

                    ifhave = true;
                    break;
                }
            }

            //没有找到相同的类型则在项目中添加该类型
            if(ifhave == false)
            {
                DemoSecType secType = new DemoSecType { Width = this.DemoSecType.Width};
                EBDB.Instance.GProject.AddEleType(secType);

                //建立图元与类型的关联
                this.DemoSecType = secType;
            }
        }

        /// <summary>
        /// 图元包含的文字被编辑完成后执行的附加操作
        /// </summary>
        public override void EleTextBeEdit() 
        {
            try
            {
                if (this.Texts.Count == 0) { return; }
                int width = Convert.ToInt32(this.Texts[0].TextStr);
                bool ifhave = false;
                foreach(DbElementType elementType in EBDB.Instance.GProject.ElementTypes)
                {
                    if(elementType is DemoSecType secType && secType.Width == width)
                    {
                        this.DemoSecType = secType;
                        ifhave = true;
                        break;
                    }
                }
                if(ifhave == false)
                {
                    DemoSecType demoSecType = new DemoSecType { Width = width };
                    EBDB.Instance.GProject.AddEleType(demoSecType);
                    this.DemoSecType = demoSecType;
                }
            }
            catch 
            {
                EBDB.Instance.UICmdLine("文字值不满足要求！",null,null);
            }
        }

        /// <summary>
        /// 检查图元是否与矩形相交(包含完全在矩形中)
        /// </summary>
        /// <param name="rec">范围矩形(矩形的两边与屏幕平行)</param>
        /// <returns></returns>
        public override Db IfCrossRec(DbRec rec)
        {
            Db db = base.IfCrossRec(rec);
            if(db == null)
            {
                DbLine cline = new DbLine(ConPts[0], ConPts[1]);
                if(GMath.IfCross(rec,cline))
                {
                    db = cline;
                }
            }
            return db;
        }

        /// <summary>
        /// 检查图元是否与矩形相交(包含完全在矩形中)
        /// </summary>
        /// <param name="rec">范围矩形(矩形可任意旋转)</param>
        /// <returns></returns>
        public override Db IfCrossRec_r(DbRec rec)
        {
            Db db = base.IfCrossRec_r(rec);
            if (db == null)
            {
                DbLine cline = new DbLine(ConPts[0], ConPts[1]);
                if (GMath.IfCross(rec, cline,true))
                {
                    db = cline;
                }
            }
            return db;
        }

        /// <summary>
        /// 图元被偏移指定距离后的图元
        /// </summary>
        /// <param name="dis">偏移距离</param>
        /// <param name="CurPt">动态函数过程中鼠标所在点，用以判断左偏还是右偏</param>
        /// <returns>不支持偏移或在特定偏移距离下会偏移失败时返回null，否则返回偏移后的图元</returns>
        public override DbElement EleOffset(double dis, DbPt CurPt) 
        {
            //确定图元用于平移的基准线
            DbLine cline = new DbLine(ConPts[0], ConPts[1]);

            //调用平台提供的平移方法对基准线进行平移
            DbLine newLine = GFunc.LineOffset(cline, dis, CurPt);

            //基于平移后的基准线创建新的图元
            DemoWallComp2 wallComp2 = new DemoWallComp2(newLine.PtSt, newLine.PtEnd, this.DemoSecType);
            wallComp2.Activate();

            return wallComp2; 
        }

        /// <summary>
        /// 基于线的图元被其他图元的线、圆裁剪时的行为:点选
        /// </summary>
        /// <param name="eles">目标图元集</param>
        /// <param name="pt">点击点</param>
        /// <returns>返回true：该图元可裁剪，false：该图元不可裁剪</returns>
        public override bool EleCutWithElesByPt(List<DbElement> eles, DbPt pt)
        {
            //确定图元用于裁剪的线，根据图元的裁剪逻辑进行确定,当前图元就是裁剪时被用户点选选中的图元，
            //在本示例中，我们采用当前图元的中心线作为被裁剪的线
            //用于裁剪的线不一定是视图中显示的线，根据业务需求，可以是任意线
            //裁剪线可以是线也可以是圆
            DbLine theLine = new DbLine(ConPts[0], ConPts[1]);

            //确定参与裁剪的线和圆，该方法传入的参数eles提供了参与裁剪的图元，可以根据这些图元结合图元的裁剪逻辑，确定出用于参与裁剪的线和圆
            //本示例设定如果是DemoWallComp2图元则提取中心线作为参与裁剪的线，如果是其他图元则以其显示的线或圆作为参与裁剪的线
            List<DbLine> eleLines = new List<DbLine>();
            List<DbCircle> eleCircles = new List<DbCircle>();
            foreach (DbElement ele in eles)
            {
                if (ele is DemoWallComp2)
                {
                    eleLines.Add(new DbLine(ele.ConPts[0], ele.ConPts[1]));
                }
                else
                {
                    if (ele.Lines.Count > 0) { eleLines.AddRange(ele.Lines); }
                    if (ele.Circles.Count > 0) { eleCircles.AddRange(ele.Circles); }
                }
            }

            //调用平台提供的默认裁剪算法GFunc.CutLine进行裁剪，默认的裁剪算法基于通用的裁剪行为进行设计，如果图元的裁剪逻辑与通用的裁剪逻辑不一致，可以自行重写
            //如果裁剪线是圆，则调用GFunc.CutCirCle方法，参数与GFunc.CutLine一样
            List<DbLine> cutlines = GFunc.CutLine(pt, theLine, eleLines, eleCircles);

            //基于裁剪后返回的线，修改图元，GFunc.CutLine方法的返回值就是用于裁剪的线被裁剪后得到的新线，可以是一段也可能是多段
            //基于裁剪后得到的线对图元进行修改，即可完成再点选模式下的图元裁剪
            if (cutlines == null) { return false; }
            else if (cutlines.Count == 1)
            {
                //注意：在编辑相关的虚函数中修改控制点需要调用平台提供的"Set"相关的方法对控制点进行修改
                //否则无法进行回退
                this.SetConPtAt(0, cutlines[0].PtSt);
                this.SetConPtAt(1, cutlines[0].PtEnd);
                this.SetConPtAt(2, GMath.GetMidPt(cutlines[0].PtSt, cutlines[0].PtEnd));
                return true;
            }
            else if(cutlines.Count>1)
            {
                //注意：在编辑相关的虚函数中修改控制点需要调用平台提供的"Set"相关的方法对控制点进行修改
                //否则无法进行回退
                this.SetConPtAt(0, cutlines[0].PtSt);
                this.SetConPtAt(1, cutlines[0].PtEnd);
                this.SetConPtAt(2, GMath.GetMidPt(cutlines[0].PtSt, cutlines[0].PtEnd));

                for (int i = 1; i < cutlines.Count; i++)
                {
                    DemoWallComp2 demoWallComp2 = new DemoWallComp2(cutlines[i].PtSt, cutlines[i].PtEnd, this.DemoSecType);
                    demoWallComp2.Activate();
                    this._view.AddElement(demoWallComp2);
                }
                return true;
            }
            else { return false; }
        }

        /// <summary>
        /// 基于线的图元被其他图元的线、圆裁剪时的行为:框选
        /// </summary>
        /// <param name="eles">目标图元集</param>
        /// <param name="rec">矩形框</param>
        /// <returns>返回true：该图元可裁剪，false：该图元不可裁剪</returns>
        public override bool EleCutWithElesByRec(List<DbElement> eles, DbRec rec)
        {
            //确定图元用于裁剪的线，根据图元的裁剪逻辑进行确定,当前图元就是裁剪时被用户选中的图元，
            //在本示例中，我们采用当前图元的中心线作为被裁剪的线
            //用于裁剪的线不一定是视图中显示的线，根据业务需求，可以是任意线
            //裁剪线可以是线也可以是圆
            DbLine theLine = new DbLine(ConPts[0], ConPts[1]);

            //确定参与裁剪的线和圆，该方法传入的参数eles提供了参与裁剪的图元，可以根据这些图元结合图元的裁剪逻辑，确定出用于参与裁剪的线和圆
            //本示例设定如果是DemoWallComp2图元则提取中心线作为参与裁剪的线，如果是其他图元则以其显示的线或圆作为参与裁剪的线
            List<DbLine> eleLines = new List<DbLine>();
            List<DbCircle> eleCircles = new List<DbCircle>();
            foreach (DbElement ele in eles)
            {
                if (ele is DemoWallComp2)
                {
                    eleLines.Add(new DbLine(ele.ConPts[0], ele.ConPts[1]));
                }
                else
                {
                    if (ele.Lines.Count > 0) { eleLines.AddRange(ele.Lines); }
                    if (ele.Circles.Count > 0) { eleCircles.AddRange(ele.Circles); }
                }
            }

            //调用平台提供的默认裁剪算法GFunc.CutLineByRec进行裁剪，默认的裁剪算法基于通用的裁剪行为进行设计，如果图元的裁剪逻辑与通用的裁剪逻辑不一致，可以自行重写
            //如果裁剪线是圆，则调用CutCircleByRec方法，参数与GFunc.CutLineByRec类似
            List<DbLine> cutlines = GFunc.CutLineByRec(rec, theLine, eleLines, eleCircles);

            //基于裁剪后返回的线，修改图元，GFunc.CutLineByRec方法的返回值就是用于裁剪的线被裁剪后得到的新线，可以是一段也可能是多段
            //基于裁剪后得到的线对图元进行修改，即可完成再点选模式下的图元裁剪
            if (cutlines == null) { return false; }
            else if (cutlines.Count == 1)
            {
                //注意：在编辑相关的虚函数中修改控制点需要调用平台提供的"Set"相关的方法对控制点进行修改
                //否则无法进行回退
                this.SetConPtAt(0, cutlines[0].PtSt);
                this.SetConPtAt(1, cutlines[0].PtEnd);
                this.SetConPtAt(2, GMath.GetMidPt(cutlines[0].PtSt, cutlines[0].PtEnd));
                return true;
            }
            else if (cutlines.Count > 1)
            {
                //注意：在编辑相关的虚函数中修改控制点需要调用平台提供的"Set"相关的方法对控制点进行修改
                //否则无法进行回退
                this.SetConPtAt(0, cutlines[0].PtSt);
                this.SetConPtAt(1, cutlines[0].PtEnd);
                this.SetConPtAt(2, GMath.GetMidPt(cutlines[0].PtSt, cutlines[0].PtEnd));

                for (int i = 1; i < cutlines.Count; i++)
                {
                    DemoWallComp2 demoWallComp2 = new DemoWallComp2(cutlines[i].PtSt, cutlines[i].PtEnd, this.DemoSecType);
                    demoWallComp2.Activate();
                    this._view.AddElement(demoWallComp2);
                }
                return true;
            }
            else { return false; }
        }

        /// <summary>
        /// 基于线的图元延伸到其他图元的线、圆时的行为:点选
        /// </summary>
        /// <param name="eles">目标图元集</param>
        /// <param name="pt">点击点</param>
        /// <returns>返回true：该图元可延伸，false：该图元不可延伸</returns>
        public override bool EleExtendWithElesByPt(List<DbElement> eles, DbPt pt)
        {
            //确定图元用于延伸的线，根据图元的延伸逻辑进行确定,当前图元就是延伸时被用户点选选中的图元，
            //在本示例中，我们采用当前图元的中心线作为被延伸的线
            //用于延伸的线不一定是视图中显示的线，根据业务需求，可以是任意线
            DbLine theLine = new DbLine(ConPts[0], ConPts[1]);

            //确定将要延伸到的目标线和圆，该方法传入的参数eles提供了作为延伸目标位置参考的图元，可以根据这些图元结合图元的延伸逻辑，确定出用于延伸的目标线和圆
            //本示例设定如果是DemoWallComp2图元则延伸到其中心线，如果是其他图元则以其显示的线或圆作为延伸目标
            List<DbLine> eleLines = new List<DbLine>();
            List<DbCircle> eleCircles = new List<DbCircle>();
            foreach (DbElement ele in eles)
            {
                if(ele is DemoWallComp2)
                {
                    eleLines.Add(new DbLine(ele.ConPts[0], ele.ConPts[1]));
                }
                else
                {
                    if (ele.Lines.Count > 0) { eleLines.AddRange(ele.Lines); }
                    if (ele.Circles.Count > 0) { eleCircles.AddRange(ele.Circles); }
                }
            }

            //调用平台提供的默认延伸算法GFunc.ExtendLineByPt进行延伸，默认的延伸算法基于通用的延伸行为进行设计，如果图元的延伸逻辑与通用的延伸逻辑不一致，可以自行重写
            DbLine targetLine = GFunc.ExtendLineByPt(theLine, pt, eleLines, eleCircles);

            //基于延伸后返回的线，修改图元，GFunc.CutLine方法的返回值就是用于延伸的线被延伸后得到的新线
            //基于延伸后得到的线对图元进行修改，即可完成再点选模式下的图元延伸
            if (targetLine == null) { return false; }

            //注意：在编辑相关的虚函数中修改控制点需要调用平台提供的"Set"相关的方法对控制点进行修改
            //否则无法进行回退
            this.SetConPtAt(0, targetLine.PtSt);
            this.SetConPtAt(1, targetLine.PtEnd);
            this.SetConPtAt(2, GMath.GetMidPt(targetLine.PtSt, targetLine.PtEnd));
            return true;
        }

        /// <summary>
        /// 基于线的图元延伸到其他图元的线、圆时的行为:框选
        /// </summary>
        /// <param name="eles">目标图元集</param>
        /// <param name="rec">矩形框</param>
        /// <returns>返回true：该图元可延伸，false：该图元不可延伸</returns>
        public override bool EleExtendWithElesByRec(List<DbElement> eles, DbRec rec)
        {
            //确定图元用于延伸的线，根据图元的延伸逻辑进行确定,当前图元就是延伸时被用户点选选中的图元，
            //在本示例中，我们采用当前图元的中心线作为被延伸的线
            //用于延伸的线不一定是视图中显示的线，根据业务需求，可以是任意线
            DbLine theLine = new DbLine(ConPts[0], ConPts[1]);

            //确定将要延伸到的目标线和圆，该方法传入的参数eles提供了作为延伸目标位置参考的图元，可以根据这些图元结合图元的延伸逻辑，确定出用于延伸的目标线和圆
            //本示例设定如果是DemoWallComp2图元则延伸到其中心线，如果是其他图元则以其显示的线或圆作为延伸目标
            List<DbLine> eleLines = new List<DbLine>();
            List<DbCircle> eleCircles = new List<DbCircle>();
            foreach (DbElement ele in eles)
            {
                if (ele is DemoWallComp2)
                {
                    eleLines.Add(new DbLine(ele.ConPts[0], ele.ConPts[1]));
                }
                else
                {
                    if (ele.Lines.Count > 0) { eleLines.AddRange(ele.Lines); }
                    if (ele.Circles.Count > 0) { eleCircles.AddRange(ele.Circles); }
                }
            }

            //调用平台提供的默认延伸算法GFunc.ExtendLineByRec进行延伸，默认的延伸算法基于通用的延伸行为进行设计，如果图元的延伸逻辑与通用的延伸逻辑不一致，可以自行重写
            DbLine targetLine = GFunc.ExtendLineByRec(theLine, rec, eleLines, eleCircles);

            //基于延伸后返回的线，修改图元，GFunc.ExtendLineByRec方法的返回值就是用于延伸的线被延伸后得到的新线
            //基于延伸后得到的线对图元进行修改，即可完成再点选模式下的图元延伸
            if (targetLine == null) { return false; }

            //注意：在编辑相关的虚函数中修改控制点需要调用平台提供的"Set"相关的方法对控制点进行修改
            //否则无法进行回退
            this.SetConPtAt(0, targetLine.PtSt);
            this.SetConPtAt(1, targetLine.PtEnd);
            this.SetConPtAt(2, GMath.GetMidPt(targetLine.PtSt, targetLine.PtEnd));
            return true;
        }

        /// <summary>
        /// 与另一个图元发生倒角时的行为
        /// </summary>
        /// <param name="thisPt">倒角时当前图元的点击点</param>
        /// <param name="ele">参与倒角的另一个图元</param>
        /// <param name="pt">倒角时另一个图元的点击点</param>
        /// <param name="rad">圆角半径，0为倒直角</param>
        /// <returns>返回true：该图元可倒角，false：该图元不可倒角</returns>
        public override bool EleFilletWithEle(DbPt thisPt, DbElement ele, DbPt pt, double rad = 0)
        {
            //不允许与其他图元进行倒角
            if (!(ele is DemoWallComp2)) { return false; }

            //确定图元用于倒角的线
            DbLine thisLine = new DbLine(ConPts[0], ConPts[1]);
            DbLine otherLine = new DbLine(ele.ConPts[0], ele.ConPts[1]);

            //调用平台提供的方法，求解倒角后的线
            GFunc.TwoLineFillet(ref thisLine, ref otherLine, thisPt, pt);

            //根据倒角后的线修改图元
            this.SetConPtAt(0, thisLine.PtSt);
            this.SetConPtAt(1, thisLine.PtEnd);
            this.SetConPtAt(2, GMath.GetMidPt(thisLine.PtSt, thisLine.PtEnd));

            //修改另一个图元的数据
            //在一次倒角操作中，该虚函数只会调用一次，因此需要将另一个图元的参数一并修改
            ele.SetConPtAt(0, otherLine.PtSt);
            ele.SetConPtAt(1, otherLine.PtEnd);
            ele.SetConPtAt(2, GMath.GetMidPt(otherLine.PtSt, otherLine.PtEnd));
            return true;
        }

        /// <summary>
        /// 与另一个图元合并时的行为
        /// </summary>
        /// <param name="ele">参与合并的另一个图元</param>
        /// <returns>返回true：该图元可合并，false：该图元不可合并</returns>
        public override bool EleMergeWithEle(DbElement ele)
        {
            //不允许与其他图元进行合并
            if (!(ele is DemoWallComp2)) { return false; }

            //确定用于图元合并的线
            List<DbLine> lines = new List<DbLine>();
            lines.Add(new DbLine(ConPts[0], ConPts[1]));
            lines.Add(new DbLine(ele.ConPts[0], ele.ConPts[1]));

            //调用平台提供的方法对线进行合并，可以根据业务需要进行重写
            //默认的方法要求给出的线必须在一条直线上且收尾连接
            //合并成功后lines列表中的线数量将变为1，且线的坐标为合并后的坐标
            GMath.LineMergeAll(lines);

            //根据合并后的线修改图元
            if(lines.Count == 1)
            {
                //根据合并后的线修改图元
                this.SetConPtAt(0, lines[0].PtSt);
                this.SetConPtAt(1, lines[0].PtEnd);
                this.SetConPtAt(2, GMath.GetMidPt(lines[0].PtSt, lines[0].PtEnd));

                //同时将第二个图元删除
                ele._view.Delete(ele);
                return true;
            }
            return false;
        }

        /// <summary>
        /// 图元在某一点或两点进行打断时的行为
        /// </summary>
        /// <param name="pt1">打断点</param>
        /// <param name="pt2">打断点</param>
        /// <returns>返回true：该图元可打断，false：该图元不可打断</returns>
        public override bool EleBreakWithPt(DbPt pt1, DbPt pt2)
        {
            //确定图元打断中心线
            DbLine cline = new DbLine(ConPts[0], ConPts[1]);
          
            //将打断点投影到中心线上
            DbPt ft1 = GMath.FootPt(cline.PtSt, cline.PtEnd,pt1);
            DbPt ft2 = GMath.FootPt(cline.PtSt, cline.PtEnd, pt2);

            DbPt ptEnd = ConPts[1].EleCopy();
            if (GMath.Distance(ConPts[0], ft1) <= GMath.Distance(ConPts[0], ft2))
            {
                //根据打断点修改当前图元
                this.SetConPtAt(0, ConPts[0]);
                this.SetConPtAt(1, ft1);
                this.SetConPtAt(2, GMath.GetMidPt(ConPts[0], ft1));

                //添加打断后的新图元
                DemoWallComp2 wallComp2 = new DemoWallComp2(ft2, ptEnd, this.DemoSecType);
                wallComp2.Activate();
                this._view.AddElement(wallComp2);
            }
            else
            {
                //根据打断点修改当前图元
                this.SetConPtAt(0, ConPts[0]);
                this.SetConPtAt(1, ft2);
                this.SetConPtAt(2, GMath.GetMidPt(ConPts[0], ft2));

                //添加打断后的新图元
                DemoWallComp2 wallComp2 = new DemoWallComp2(ft1, ptEnd, this.DemoSecType);
                wallComp2.Activate();
                this._view.AddElement(wallComp2);
            }
            return true;
        }

        /// <summary>
        /// 图元被双击进入编辑状态时执行的操作
        /// </summary>
        /// <param name="showEles">图元被双击编辑时需要显示的图元,可根据需要将图元展开并存放到showEles列表中</param>
        /// <param name="quickToolBar">图元被双击编辑时可自定义的的快捷工具条</param>
        /// <returns>true:需要进入图元编辑状态 false：该图元无编辑状态</returns>
        public override bool EleBeginEdit(List<DbElement> showEles, QuickToolBar quickToolBar)
        {
            //通过传入的quickToolBar参数，可以自定义双击编辑状态下工具条的样式
            //图标资源地址
            string uri = @"pack://application:,,,/DemoUI;component/Resources/";
            //向工具条中添加一个默认的命令按钮，AddButton方法的参数：toolTip是按钮的提示信息，funcName是要调用的命令的类名，用法与UI界面用法一致，imgUri是按钮需要显示的图标
            quickToolBar.AddButton("绘制直线", "EBPlugIn.CreatLine", uri+ "A_01.png");
            //向工具条添加一个自定义控件
            Button button = new Button { Content = "自定义按钮", Width = 80, Height = 28,Margin = new System.Windows.Thickness(0,0,5,0) };
            quickToolBar.AddControl(button);

            //定义双击编辑状态下图元需要显示的内容，图元需要显示的内容添加到参数showEles列表中
            //在本示例中，我们将图元Lines列表中的所有线(DbLine)转换为Line并添加到showEles进行显示
            //根据业务需求，需要在双击编辑情况下显示的任何图元都可以通过添加到showEles列表进行显示
            foreach (DbLine dbLine in this.Lines)
            {
                Line line = new Line(dbLine.PtSt, dbLine.PtEnd);
                line.Activate();

                //注意这里需要手动调用CalSolid2D用于二维显示，否则会报错
                line.CalSolid2D();

                showEles.Add(line);
            }

            //平台通过返回值确定图元是否支持双击编辑
            //返回 true：表明该图元支持双击编辑，进入双击编辑模式
            //返回 false：表明该图元不支持双击编辑,不会进入双击编辑模式
            //在该虚函数的扩展用法中，可以返回false让平台不进入双击编辑模式，但是在该方法中弹出其他对话框，实现其他业务需求
            return true;
        }

        /// <summary>
        /// 图元结束编辑状态时执行的操作
        /// </summary>
        /// <param name="showEles">图元被双击编辑时展开显示的图元</param>
        /// <returns>返回 true:图元编辑正常结束 false:图元编辑结束被取消</returns>
        public override bool EleEndEdit(List<DbElement> showEles)
        {
            //当用户点击图元编辑状态下的工具条上的【确定】按钮后，该方法会被调用
            //通过传入的参数showEles可以获取用户在双击编辑模式下修改后的所有图元
            //通过对这些图元进行分析，可以转换为图元需要的参数和数据，从而完成图元的深度编辑
            //在本示例中不再对获取的数据作任何处理
            foreach(DbElement ele in showEles)
            {
                //从编辑后的图元中提取有效信息，完成图元的深度编辑
            }

            //平台通过返回值确定当前用户的编辑是否满足程序的需求
            //返回 true：用户编辑后的结果满足程序需求，双击编辑结束
            //返回 false:用户编辑后的结果不满足程序需求，需要修改后，才能退出编辑模型
            //通常在获取到用户编辑后的图元后，进行数据检查，确认编辑后的数据是否满足要求，对于不满足要求的，提醒用户进行修改，将返回值设置为 false 阻止本次退出
            return true;
        }

        /// <summary>
        /// 图元放弃编辑状态时执行的附加操作：如解除或恢复锁定平面等
        /// 调用EleCancelEdit前已经创建了事务Transaction("放弃编辑")
        /// EleCancelEdit中的附加操作应该考虑事务，满足Undo时能复原至放弃编辑前的状态
        /// </summary>
        public override void EleCancelEdit() 
        {
            //当用户点击图元编辑状态下的工具条上的【确定】按钮后，该方法会被调用
            //【取消】按钮的逻辑是放弃图元在双击编辑状态下的所有修改
            //大部分情况下，平台可以自动将图元恢复到双击编辑前的状态，无需重写该方法
            //对于少部分特殊情况，可能需要重写该方法，确保用户在取消编辑后，图元的所有参数能回到双击编辑前的状态
        }

        /// <summary>
        /// 三维图元形体发生变更时找出因受到本图元影响也应该裁剪的图元，
        /// 该方法在图元形体发生变更后被调用
        /// 形体变更包括：移动、拉伸、拖拽、变形等，
        /// 找出的相关图元直接添加到cutReleEles列表中，
        /// 在向cutReleEles列表添加图元时应确保列表内图元不重复
        /// 为了减小裁剪运算的算量，cutReleEles 列表应尽可能准确
        /// </summary>
        /// <param name="cutReleEles">图元裁剪时相关的三维构件</param>
        /// <param name="oldArea">构件在形体变更前的抽象区域</param>
        /// <param name="view">构件所在视图，当为null时，需根据图元所在楼层的ID查找到楼层</param>
        public override void FindCutReleEle(List<DbElement> cutReleEles, DbEleArea oldArea, View view = null)
        {
            //在选中的图元发生形变时，所有图元依次调用该方法，在该方法中，需要将所有受影响的图元添加到cutReleEles列表中，
            //cutReleEles列表是一个全局变量，当选中的所有图元都将受影响的图元添加进去后，最终会形成一个完整的受影响图元列表
            //添加到cutReleEles列表的图元应确保不重复，平台不会进行去重，需要开发者在添加时自行判断并去重
            //在该方法中只需要通过抽象区域对受影响的图元进行粗略查找即可，不需要进行精确的分析
            //在调用该方法时，该图元的抽象区域已经是形变后的，该图元形变前的抽象区域通过参数oldArea传入
            //需要通过形变前的抽象区域和形变后的抽象区域找出所有受影响图元
            //FindCutReleEle的基本逻辑就是完成受影响图元的粗略查找，不要将其他无关的操作放到该方法中

            //遍历本层所有图元查找受影响图元，根据图元的业务需求，也可在其他视图或视图参照中查找
            Floor floor = this._view.Floor;
            foreach (DbElement ele in floor.Elements)
            {
                if(ele is DemoWallComp2)//确定参与裁剪的图元类别，本示例设定DemoWallComp2图元只与DemoWallComp2图元进行裁剪
                {
                    if(this.EleArea.IfCross(ele.EleArea) || oldArea.IfCross(ele.EleArea))//用形变前的抽象区域和形变后的抽象区域进行粗略查找
                    {
                        if(!cutReleEles.Contains(ele))//检查列表中是否已有相同图元，避免重复添加
                        {
                            //ele.ElePreCut();//如果需要将图元恢复到初始状态，一般在这里添加ElePreCut方法,本示例不需要
                            cutReleEles.Add(ele);//将找出的受影响图元加入到列表
                        }
                    }
                }
            }

            //如果是三维图元，通过平台提供的FindLevelMark方法将标高图元加入到受影响图元列表中，实现标高数据的联动
            //如果不调用下面的代码，则该图元标高发生变化时，标高图元显示的标高数据不会更新
            if (_if3D) { FindLevelMark(cutReleEles, oldArea); }
        }

        /// <summary>
        /// 裁剪预处理，该方法在协同同步时会单独被调用
        /// </summary>
        public override void ElePreCut()
        {
            //注意：图元裁剪的预处理方法ElePreCut并不是所有图元都需要重写
            //ElePreCut方法的基本逻辑是将裁剪后的图元恢复到初始状态，以确保之前的裁剪对图元的改变不会对后面的裁剪造成影响
            //只有之前的裁剪导致数据发生变化，影响之后的裁剪时，才需要重新该方法
            //在ElePreCut方法中不要添加与恢复图元初始转态无关的内容
            //在一般的裁剪流程中，平台不会自动调用该方法，需要图元的开发者自己调用，如果需要的话，一般在FindCutReleEle方法中对找到的受影响图元调用一次
            //之所以将ElePreCut方法做专门的封装，是因为在构件级协同同步数据时，平台需要调用该方法将所有图元恢复到初始状态，然后再统一裁剪
            //在本示例中，判断两个DemoWallComp2图元是否需要进行裁剪采用的逻辑是中心线是否相交，图元裁剪后中心线不会变化，因此即使发生裁剪也不会对之后的裁剪产生影响，所以不需要重新ElePreCut方法
        }

        /// <summary>0
        /// 图元裁剪
        /// </summary>
        public override void EleCut()
        {
            //遍历本视图所有图元，查找与当前图元存在裁剪关系的图元，根据业务需求，也可以在其他视图查找
            //注意：必须遍历本视图所有图元进行查找，不能只在受影响的图元中查找，否则可能无法找全所有图元
            //在图元裁剪时需要进行精确查找，和精确计算，算量较大，FindCutReleEle方法的目的本质上是缩小精确计算的范围，提升程序性能
            //在本示例中设定两个DemoWallComp2图元的中心线相交则可进行裁剪，真实的应用场景请根据业务需求进行判定
            //由于每个裁剪相关的图元都会调用EleCut方法，因此，在EleCut方法中，图元只需要修改自身，不用修改其他图元
            Floor floor = this._view.Floor;
            List<DemoWallComp2> releEles = new List<DemoWallComp2>();//找到的图元存放到这个列表
            DbLine cline = new DbLine(ConPts[0], ConPts[1]);
            foreach (DbElement ele in floor.Elements)
            {
                //通过抽象区域进行快速筛选
                //注意：在floor.Elements列表中图元较多时（万级），先判断this.EleArea.IfCross(ele.EleArea) 和先判断this.UniqueId != ele.UniqueId具有明显的性能差异
                //比如10000个图元整体移动时，需要调用EleCut方法10000次，每个图元遍历图元列表也是10000次，总共10000x10000
                //如果将this.UniqueId != ele.UniqueId判断放在前面，基本每个图元都需要判断一次，再判断EleArea，因为相同的图元只有一个
                //如果将this.EleArea.IfCross(ele.EleArea)放在前面，则大部分图元不需要进行后一步的UniqueId判断
                //在10000x10000次this.UniqueId != ele.UniqueId判定中，时间是秒级的，会造成明显的卡顿
                if (ele is DemoWallComp2 demoWall && this.EleArea.IfCross(ele.EleArea) && this.UniqueId != ele.UniqueId)
                {
                    DbLine midline = new DbLine(ele.ConPts[0], ele.ConPts[1]);
                    if(GMath.LiSegmentCross(cline, midline)!=null)//判定两线段是否有交点
                    {
                        releEles.Add(demoWall);
                    }
                }
            }

            //下面是图元的裁剪逻辑，在本示例中通过面域计算裁剪后的线，具体裁剪需求应根据业务需求进行设定
            //通过当前图元的轮廓构件面域DbRegion用于裁剪计算 
            //一般情况下，如果图元需要使用面域进行裁剪计算，通常会将面域相关数据作为图元的参数保存下来，而不是在裁剪时临时计算
            //本示例为了避免图元的参数过于复杂，所以临时计算面域
            List<DbPt> pts = GetPts();
            DbRegion dbRegion = new DbRegion(pts);

            //计算出其他有裁剪关系的图元的面域
            List<DbRegion> midRegions = new List<DbRegion>();
            foreach(DemoWallComp2 demoWall in releEles)
            {
                List<DbPt> midpts = demoWall.GetPts();
                DbRegion midRegion = new DbRegion(midpts);
                midRegions.Add(midRegion);
            }

            //通过平台提供的面域相关算法计算出当前图元的面域减去其他图元的面域后的新面域
            DbRegion newRegion = GMath.GetSubtractRegion(dbRegion, midRegions);

            //清除图元原有的线，将新计算出的线放入图元的Lines列表中
            Lines.Clear();
            if (newRegion!=null && newRegion.Loops.Count>0)
            {
                foreach(DbLoop dbLoop in newRegion.Loops)
                {
                    foreach(DbLine mline in dbLoop.Lines)
                    {
                        if(GMath.IfLineOnRegion(mline, dbRegion))//这里通过IfLineOnRegion方法剔除不需要的线
                        {
                            Lines.Add(mline);
                        }
                    }
                }
            }

            //计算图元用于显示的元素，该方法必须调用，否则图元无法在平面视图显示
            CalSolid2D(); 
        }






    }

    /// <summary>
    /// 示例墙截面类型
    /// </summary>
    [Serializable]
    public class DemoSecType : DbElementType
    {
        /// <summary>
        /// 墙体厚度
        /// </summary>
        public int Width = 0;

        /// <summary>
        /// 保存数据
        /// </summary>
        /// <param name="binaryWriter">写入流</param>
        public override void DataSave(BinaryWriter binaryWriter)
        {
            //类标识，必要
            binaryWriter.Write(this.GetType().ToString());

            //版本号，必要，每次版本的升级必须注明时间和变更内容
            //0版：2024.10.30 新增
            binaryWriter.Write(0);

            //类型所有参数，由于类型基类DbElementType的参数较简单，没有提供保存公有参数的方法，因此基类中的两个参数也需要保存
            binaryWriter.Write(UniqueId);
            binaryWriter.Write(CreatorId);
            binaryWriter.Write(Width);
        }

        /// <summary>
        /// 读取数据
        /// </summary>
        /// <param name="binaryReader"></param>
        public override void DataLoad(BinaryReader binaryReader) 
        {
            //读取版本号，必要
            int VerNum = binaryReader.ReadInt32();

            //每个版本号的读写都必须保留
            if (VerNum == 0)
            {
                #region
                //类型需要保存的参数
                UniqueId = binaryReader.ReadString();
                CreatorId = binaryReader.ReadString();
                Width = binaryReader.ReadInt32();
                #endregion
            }
        }

        /// <summary>
        /// 返回类型名称
        /// </summary>
        /// <returns>类型名称</returns>
        public override string GetSecName() { return "墙厚 =" + Width.ToString(); }

        /// <summary>
        /// 在项目中类型参数被改变时，重算所有使用了该类型的图元
        /// </summary>
        public void TypeChange()
        {
            //平台没有自动维护类型参数被改变时相关图元的行为
            //需要图元的设计者自行维护
            //参考逻辑：遍历整个项目，找到所有使用了该类型的图元，重算图元数据
            foreach(View view in EBDB.Instance.GProject.StanViews)
            {
                foreach(DbElement ele in view.Floor.Elements)
                {
                    if(ele is DemoWallComp2)
                    {
                        ele.ActCutCal2D3D();
                    }
                }
            }
        }

        /// <summary>
        /// 判断两个类型是否相同
        /// </summary>
        /// <param name="dbElementType">与当前图元比较的类型</param>
        /// <returns>返回：true 两个类型相同 false 两个类型不同</returns>
        public override bool IfSame(DbElementType dbElementType)
        {
            if (dbElementType is DemoSecType secType && secType.UniqueId == this.UniqueId)
            {
                return true;
            }
            else { return false; }
        }
    }

}
