/// <summary>
/// 🎯 CurtainWallFrameType的角钢扩展 - 支持标准化角钢生成
/// </summary>
public partial class CurtainWallFrameType
{
    #region 🔥 角钢专用属性和方法

    /// <summary>
    /// 当前角钢的标准规格（仅Type=3时有效）
    /// </summary>
    private AngleSteelSpec _currentAngleSpec = null;

    /// <summary>
    /// 是否为标准角钢（仅在Type=3时有意义）
    /// </summary>
    public bool IsStandardAngleSteel => Type == 3 && _currentAngleSpec != null;

    /// <summary>
    /// 获取角钢的标准规格信息
    /// </summary>
    public AngleSteelSpec AngleSpec => _currentAngleSpec;

    /// <summary>
    /// 🎯 重写角钢参数设置，加入标准化约束
    /// </summary>
    public void SetAngleSteelParameters(int sideLength, int thickness, bool forceStandard = true)
    {
        if (Type != 3)
        {
            throw new InvalidOperationException("只有角钢类型(Type=3)才能设置角钢参数");
        }

        if (forceStandard)
        {
            // 🔥 强制使用标准规格
            var standardSpec = AngleSteelStandards.FindStandardSpec(sideLength, thickness);
            if (standardSpec == null)
            {
                // 找最接近的标准规格
                standardSpec = AngleSteelStandards.GetNearestStandardSpec(sideLength, thickness);
                
                // 提示用户参数已调整
                var message = $"参数 L{sideLength}×{thickness} 不符合国标，已调整为标准规格 L{standardSpec.SideLength}×{standardSpec.Thickness}";
                // 这里可以通过事件或回调通知UI
                OnParameterAdjusted?.Invoke(message);
            }
            
            // 应用标准规格
            ApplyStandardAngleSpec(standardSpec);
        }
        else
        {
            // 🔥 允许非标准参数（用于特殊情况）
            B = sideLength;
            H = sideLength; // 等边角钢
            T = thickness;
            
            // 按经验公式计算圆弧半径
            D = (int)(thickness / 3.0); // r1 = d/3
            U = CalculateMiddleRadius(sideLength, thickness); // 中间圆弧半径
            
            _currentAngleSpec = null; // 清除标准规格引用
            
            // 重新生成几何
            GenerateStandardGeometry();
        }
    }

    /// <summary>
    /// 应用标准角钢规格
    /// </summary>
    private void ApplyStandardAngleSpec(AngleSteelSpec spec)
    {
        _currentAngleSpec = spec;
        
        // 设置基本参数
        B = spec.SideLength;
        H = spec.SideLength; // 等边角钢
        T = spec.Thickness;
        D = (int)spec.TopRadius; // 顶端圆弧半径 r1
        U = (int)spec.MiddleRadius; // 中间圆弧半径 r
        
        // 设置工程特性
        Area = spec.Area;
        WeightPerMeter = spec.WeightPerMeter;
        
        // 更新名称
        Name = spec.GetStandardName();
        
        // 重新生成几何
        GenerateStandardGeometry();
    }

    /// <summary>
    /// 根据边长和厚度计算中间圆弧半径的经验公式
    /// </summary>
    private int CalculateMiddleRadius(int sideLength, int thickness)
    {
        // 根据常见规律的经验公式
        if (sideLength <= 30) return (int)(3.5 + thickness * 0.1);
        if (sideLength <= 45) return (int)(5.0 + thickness * 0.2);
        if (sideLength <= 63) return (int)(7.0 + thickness * 0.1);
        if (sideLength <= 90) return (int)(9.0 + thickness * 0.1);
        if (sideLength <= 125) return (int)(10.0 + thickness * 0.15);
        if (sideLength <= 160) return (int)(13.0 + thickness * 0.1);
        return (int)(15.0 + thickness * 0.1);
    }

    /// <summary>
    /// 参数调整事件（当非标准参数被调整为标准参数时触发）
    /// </summary>
    public event Action<string> OnParameterAdjusted;

    #endregion

    #region 🔥 重写的角钢几何生成方法

    /// <summary>
    /// 🎯 生成标准角钢几何（包含圆弧）
    /// </summary>
    private void GenerateAngleSteel()
    {
        double sideLength = B;
        double thickness = T;
        double topRadius = D; // r1 = d/3
        double middleRadius = U; // 中间圆弧半径 r

        // 🔥 生成带圆弧的等边角钢几何
        GenerateAngleSteelWithArcs(sideLength, thickness, topRadius, middleRadius);
        
        // 添加轮廓点（用于面积计算等）
        GenerateAngleSteelOutlinePoints(sideLength, thickness);
    }

    /// <summary>
    /// 🎯 生成带圆弧的角钢几何
    /// </summary>
    private void GenerateAngleSteelWithArcs(double sideLength, double thickness, double topRadius, double middleRadius)
    {
        // 🔥 关键坐标计算（以内角点为原点）
        // 角钢内角点在几何中心的左下方
        double offsetX = -sideLength / 2.0;
        double offsetY = -sideLength / 2.0;
        
        // 🔥 外轮廓关键点
        var outerPoints = new List<DbPt>
        {
            // 从内角点开始，逆时针方向
            new DbPt(offsetX, offsetY), // 内角点
            new DbPt(offsetX + sideLength, offsetY), // 右下外角
            new DbPt(offsetX + sideLength, offsetY + thickness), // 右下内拐点
            new DbPt(offsetX + thickness, offsetY + thickness), // 内角内点
            new DbPt(offsetX + thickness, offsetY + sideLength), // 左上内拐点  
            new DbPt(offsetX, offsetY + sideLength) // 左上外角
        };

        // 🔥 添加圆弧处理
        var smoothedPoints = new List<DbPt>();
        
        for (int i = 0; i < outerPoints.Count; i++)
        {
            var currentPt = outerPoints[i];
            var nextPt = outerPoints[(i + 1) % outerPoints.Count];
            
            // 判断是否需要添加圆弧
            if (ShouldAddArc(i, topRadius, middleRadius))
            {
                var arcRadius = GetArcRadius(i, topRadius, middleRadius);
                var arcPoints = GenerateArcPoints(currentPt, nextPt, arcRadius, 8);
                smoothedPoints.AddRange(arcPoints);
            }
            else
            {
                smoothedPoints.Add(currentPt);
            }
        }

        // 🔥 生成线段和圆弧
        for (int i = 0; i < smoothedPoints.Count; i++)
        {
            var currentPt = smoothedPoints[i];
            var nextPt = smoothedPoints[(i + 1) % smoothedPoints.Count];
            
            // 检查是否为圆弧点
            if (currentPt.PtType == 1) // 圆弧中点
            {
                // 创建圆弧线段
                var startPt = smoothedPoints[(i - 1 + smoothedPoints.Count) % smoothedPoints.Count];
                var endPt = nextPt;
                var arcLine = new DbLine(startPt, endPt, currentPt);
                FrameLines.Add(arcLine);
                i++; // 跳过下一个点
            }
            else
            {
                // 创建直线段
                FrameLines.Add(new DbLine(currentPt, nextPt));
            }
        }
    }

    /// <summary>
    /// 判断指定位置是否需要添加圆弧
    /// </summary>
    private bool ShouldAddArc(int pointIndex, double topRadius, double middleRadius)
    {
        // 顶端圆弧：索引1（右下外角）和索引5（左上外角）
        if (pointIndex == 1 || pointIndex == 5)
            return topRadius > 0.5;
            
        // 中间圆弧：索引3（内角内点）
        if (pointIndex == 3)
            return middleRadius > 0.5;
            
        return false;
    }

    /// <summary>
    /// 获取指定位置的圆弧半径
    /// </summary>
    private double GetArcRadius(int pointIndex, double topRadius, double middleRadius)
    {
        if (pointIndex == 1 || pointIndex == 5) // 顶端圆弧
            return topRadius;
        if (pointIndex == 3) // 中间圆弧
            return middleRadius;
        return 0;
    }

    /// <summary>
    /// 在两点之间生成圆弧点
    /// </summary>
    private List<DbPt> GenerateArcPoints(DbPt startPt, DbPt endPt, double radius, int segments = 8)
    {
        var points = new List<DbPt>();
        
        // 🔥 简化处理：生成圆弧近似点
        // 在实际项目中，这里应该使用更精确的圆弧计算
        
        // 计算圆弧中心和角度
        var midPt = new DbPt((startPt.X + endPt.X) / 2, (startPt.Y + endPt.Y) / 2);
        var direction = new DbPt(endPt.X - startPt.X, endPt.Y - startPt.Y);
        var length = Math.Sqrt(direction.X * direction.X + direction.Y * direction.Y);
        
        if (length < 0.001) 
        {
            points.Add(startPt);
            return points;
        }
        
        // 归一化方向向量
        direction.X /= length;
        direction.Y /= length;
        
        // 计算法向量（向内）
        var normal = new DbPt(-direction.Y, direction.X);
        
        // 圆弧中心
        var arcCenter = new DbPt(
            midPt.X + normal.X * radius,
            midPt.Y + normal.Y * radius
        );
        
        // 生成圆弧点
        points.Add(startPt);
        
        for (int i = 1; i < segments; i++)
        {
            double t = (double)i / segments;
            // 简化的圆弧插值
            var arcPt = new DbPt(
                startPt.X + t * (endPt.X - startPt.X) + Math.Sin(t * Math.PI) * radius * normal.X,
                startPt.Y + t * (endPt.Y - startPt.Y) + Math.Sin(t * Math.PI) * radius * normal.Y
            );
            
            if (i == segments / 2) // 中点标记为圆弧点
            {
                arcPt.PtType = 1;
            }
            
            points.Add(arcPt);
        }
        
        return points;
    }

    /// <summary>
    /// 生成角钢轮廓点（用于面积计算）
    /// </summary>
    private void GenerateAngleSteelOutlinePoints(double sideLength, double thickness)
    {
        FrameOutPts.Clear();
        
        double offsetX = -sideLength / 2.0;
        double offsetY = -sideLength / 2.0;
        
        // 简化的轮廓点（不包含圆弧）
        FrameOutPts.Add(new DbPt(offsetX, offsetY)); // 内角点
        FrameOutPts.Add(new DbPt(offsetX + sideLength, offsetY)); // 右下外角
        FrameOutPts.Add(new DbPt(offsetX + sideLength, offsetY + thickness)); // 右下内角
        FrameOutPts.Add(new DbPt(offsetX + thickness, offsetY + thickness)); // 内角内点
        FrameOutPts.Add(new DbPt(offsetX + thickness, offsetY + sideLength)); // 左上内角
        FrameOutPts.Add(new DbPt(offsetX, offsetY + sideLength)); // 左上外角
    }

    #endregion

    #region 🔥 角钢专用验证方法

    /// <summary>
    /// 验证角钢参数是否合理
    /// </summary>
    public ValidationResult ValidateAngleSteelParameters()
    {
        var result = new ValidationResult();
        
        if (Type != 3)
        {
            result.AddError("当前不是角钢类型");
            return result;
        }
        
        // 检查是否为标准规格
        if (!AngleSteelStandards.IsStandardSpec(B, T))
        {
            result.AddWarning($"L{B}×{T} 不是标准角钢规格");
            
            // 推荐最接近的标准规格
            var nearestSpec = AngleSteelStandards.GetNearestStandardSpec(B, T);
            if (nearestSpec != null)
            {
                result.AddWarning($"建议使用标准规格：{nearestSpec.GetStandardName()}");
            }
        }
        
        // 检查几何参数合理性
        if (B != H)
        {
            result.AddError("等边角钢的两边长必须相等");
        }
        
        if (T >= B * 0.8)
        {
            result.AddError("厚度过大，应小于边长的80%");
        }
        
        // 检查圆弧半径
        double expectedTopRadius = AngleSteelStandards.CalculateTopRadius(T);
        if (Math.Abs(D - expectedTopRadius) > 0.5)
        {
            result.AddWarning($"顶端圆弧半径建议为 {expectedTopRadius:F1}mm (d/3)");
        }
        
        return result;
    }

    /// <summary>
    /// 获取角钢可选的边长列表
    /// </summary>
    public List<int> GetAvailableAngleSideLengths()
    {
        return AngleSteelStandards.GetAvailableSideLengths();
    }

    /// <summary>
    /// 获取指定边长的可选厚度列表
    /// </summary>
    public List<int> GetAvailableAngleThicknesses(int sideLength)
    {
        return AngleSteelStandards.GetAvailableThicknesses(sideLength);
    }

    #endregion
}

/// <summary>
/// 🎯 验证结果类
/// </summary>
public class ValidationResult
{
    public List<string> Errors { get; } = new List<string>();
    public List<string> Warnings { get; } = new List<string>();
    
    public bool IsValid => Errors.Count == 0;
    public bool HasWarnings => Warnings.Count > 0;
    
    public void AddError(string message) => Errors.Add(message);
    public void AddWarning(string message) => Warnings.Add(message);
    
    public void Merge(ValidationResult other)
    {
        Errors.AddRange(other.Errors);
        Warnings.AddRange(other.Warnings);
    }
    
    public override string ToString()
    {
        var messages = new List<string>();
        if (Errors.Count > 0)
            messages.Add($"错误: {string.Join("; ", Errors)}");
        if (Warnings.Count > 0)
            messages.Add($"警告: {string.Join("; ", Warnings)}");
        return string.Join(" | ", messages);
    }
}