    /// <summary>
    /// 幕墙龙骨图元 
    /// </summary>
    [Serializable]
    [DbElement("幕墙龙骨", MajorType.CurtainWall)]
    public class CurtainWallFrameElement : CurtainWallFrame
    {
        #region 定位点属性
        /// <summary>
        /// 定位点半径
        /// </summary>
        private double _anchorRadius = 6.0;
        /// <summary>
        /// 定位点半径
        /// </summary>
        [Category("定位点属性"), DisplayName("定位点半径"), Description("定位点显示图像的半径"), ReadOnly(false)]
        public double AnchorRadius
        {
            get { return _anchorRadius; }
            set
            {
                if (Math.Abs(_anchorRadius - value) < 0.001) return;
                TransManager.Instance().Push(a => AnchorRadius = a, _anchorRadius);
                _anchorRadius = value;
                if (AutoActivate) { ActCutCal2D3D(); }
            }
        }

        /// <summary>
        /// 是否显示定位点
        /// </summary>
        private bool _showAnchor = false;
        /// <summary>
        /// 是否显示定位点
        /// </summary>
        [Category("定位点属性"), DisplayName("显示定位点"), Description("是否显示定位点图形"), ReadOnly(false)]
        public bool ShowAnchor
        {
            get { return _showAnchor; }
            set
            {
                if (_showAnchor == value) return;
                TransManager.Instance().Push(a => ShowAnchor = a, _showAnchor);
                _showAnchor = value;
                if (AutoActivate) { ActCutCal2D3D(); }
            }
        }
        #endregion

        #region 显示控制
        /// <summary>
        /// 强制虚线
        /// </summary>
        protected bool _ifForecDash = false;
        /// <summary>
        /// 强制虚线
        /// </summary>
        [Category("图面表达"), DisplayName("强制虚线"), Description("强制虚线"), ReadOnly(false)]
        public bool IfForceDash { get { return _ifForecDash; } set { TransManager.Instance().Push(a => IfForceDash = a, _ifForecDash); _ifForecDash = value; if (AutoActivate) { ActCutCal2D3D(); } } }

        /// <summary>
        /// 是否填充
        /// </summary>
        private bool _ifHatch = true;
        /// <summary>
        /// 是否填充
        /// </summary>
        [Category("显示属性"), DisplayName("是否填充"), Description("龙骨截面是否显示填充"), ReadOnly(false)]
        public bool IfHatch
        {
            get { return _ifHatch; }
            set
            {
                if (_ifHatch == value) return;
                TransManager.Instance().Push(a => IfHatch = a, _ifHatch);
                _ifHatch = value;
                if (AutoActivate) { ActCutCal2D3D(); }
            }
        }

        /// <summary>
        /// 表面处理方式
        /// </summary>
        private SurfaceTreatmentType _surfaceTreatment = new SurfaceTreatmentType();
        /// <summary>
        /// 表面处理方式
        /// </summary>
        [Category("表面处理方式"), DisplayName("表面处理方式"), Description("表面处理方式"), ReadOnly(false)]
        [Editor(typeof(EnumEditor), typeof(EnumEditor))]
        public SurfaceTreatmentType SurfaceTreatment
        {
            get => _surfaceTreatment;
            set
            {
                if (_surfaceTreatment == value) return;
                TransManager.Instance().Push(a => SurfaceTreatment = a, _surfaceTreatment);
                _surfaceTreatment = value;
                if (AutoActivate) { ActCutCal2D3D(); }
            }
        }


        #endregion

        /// <summary>
        /// 变换矩阵
        /// </summary>
        private Matrix3D _transformMatrix = Matrix3D.Identity;

        /// <summary>
        /// 是否应用变换矩阵
        /// </summary>
        private bool _hasTransform = false;

        /// <summary>
        /// 是否镜像
        /// </summary>
        private bool _ifMirror = false;

        #region 构造函数
        /// <summary>
        /// 无参构造函数
        /// </summary>
        public CurtainWallFrameElement()
        {
            _if3D = false;
        }

        /// <summary>
        /// 标准构造函数 
        /// </summary>
        /// <param name="frameType">截面类型</param>
        /// <param name="insertPt">插入点</param>
        /// <param name="frameAngle">旋转角度</param>
        /// <param name="offsetX">X偏移（截面左右移动）</param>
        /// <param name="offsetY">Y偏移（截面上下移动）</param>
        public CurtainWallFrameElement(CurtainWallFrameType frameType, DbPt insertPt, double frameAngle = 0.0, double offsetX = 0, double offsetY = 0.0)
        {
            _if3D = false;
            LayerSet("幕墙龙骨");

            FrameType = frameType;
            FrameTypeUId = frameType.UniqueId;

            // 设置定位点功能
            // 定位点就是插入点位置
            _anchorPosition = insertPt.EleCopy();
            ConPts.Add(_anchorPosition);
            _showAnchor = true;
            _offsetX = offsetX;
            _offsetY = offsetY;
            _frameAngle = frameAngle;
        }

        #endregion

        #region 核心绘制-图元激活
        /// <summary>
        /// 图元激活
        /// </summary>
        public override void Activate()
        {
            // 清空几何
            //Lines.Clear();
            //Circles.Clear();
            //Hatchs.Clear();
            //ConPts.Clear();

            // 计算绘制位置
            // 定位点位于截面下边中点偏移30mm处的概念：
            // 1. 先计算截面的下边中点位置
            // 2. 定位点相对于截面下边中点偏移30mm（向下）
            // 3. 实际绘制时，截面位置 = 定位点位置 + 截面下边中点相对原点的偏移 - 30mm（向上） + offsetX/offsetY
            
            // 计算截面几何的边界
            double frameHeight = FrameType?.H ?? 120; // 截面高度
            double sectionBottomCenterOffset = frameHeight / 2.0; // 截面下边中点相对截面中心的偏移
            double anchorOffset = 30.0; // 定位点相对截面下边中点的偏移（向下30mm）
            
            // 实际绘制位置计算：
            // 绘制位置 = 定位点位置 + (截面中心相对定位点的偏移) + 用户控制的偏移
            // 截面中心相对定位点的偏移 = 截面下边中点偏移 + 定位点偏移 = frameHeight/2 + 30
            double drawX = ConPts[0].X + _offsetX;
            double drawY = ConPts[0].Y + sectionBottomCenterOffset + anchorOffset + _offsetY;

            Lines = FrameType.RotateAndMove(_frameAngle, drawX, drawY);
            Circles = FrameType.RotateAndMove(drawX, drawY);
            FrameOutPts = FrameType.RotateAndMovePts(_frameAngle, drawX, drawY);

            // 处理自定义截面
            if (FrameType.Type == 7 && FrameOutPts.Count == 0 && Lines.Count > 0)
            {
                Lines.ForEach(p => FrameOutPts.Add(p.PtSt));
            }

            // 填充处理
            if (_ifHatch && Lines.Count > 0)
            {
                DbHatch hatch = new DbHatch(Lines, 1, 1);
                hatch.LayerId = PreLayerManage.GetLayerId("填充层");
                Hatchs.Add(hatch);
            }

            // 后处理
            LayerChange(layerManage.GetLayer(_layerId));
            if (_styleIndex >= 0) { foreach (DbLine line in Lines) { line.StyleIndex = _styleIndex; } }
            if (_colorIndex >= 0) { foreach (DbLine line in Lines) { line.ColorIndex = _colorIndex; } }
            if (_widthIndex >= 0) { foreach (DbLine line in Lines) { line.WidthIndex = _widthIndex; } }

            // 定位点显示处理
            if (_showAnchor)
            {
                DrawAnchorPoint();
            }

            EleArea = new DbEleArea(this);
            CalSolid2D();
        }
        #endregion

        #region 定位点绘制
        /// <summary>
        /// 绘制定位点标识 - 十字形状（可选的视觉辅助）
        /// 注意：定位点本身通过ConPts显示，这里只是额外的视觉标识
        /// </summary>
        private void DrawAnchorPoint()
        {
            // 绘制X形状的定位点（两条对角线）
            double halfSize = _anchorRadius * 1.5;

            // 第一条对角线（左上到右下）
            DbLine diagonalLine1 = new DbLine(_anchorPosition.Move(-halfSize, halfSize), _anchorPosition.Move(halfSize, -halfSize));

            // 第二条对角线（左下到右上）
            DbLine diagonalLine2 = new DbLine(_anchorPosition.Move(-halfSize, -halfSize), _anchorPosition.Move(halfSize, halfSize));

            diagonalLine1.SetStatus(0, 2, 0);
            diagonalLine2.SetStatus(0, 2, 0);
            diagonalLine1.LayerId = PreLayerManage.GetLayerId("通用-非打印");
            diagonalLine2.LayerId = PreLayerManage.GetLayerId("通用-非打印");
            diagonalLine1.StyleIndex = 2;
            diagonalLine2.StyleIndex = 2;
            Lines.Add(diagonalLine1);
            Lines.Add(diagonalLine2);
        }

        #endregion

        #region 图元操作重写

        /// <summary>
        /// 图元移动
        /// </summary>
        public override void EleMove(double X, double Y)
        {
            _anchorPosition.MoveSelf(X, Y);

            //_insertPt.MoveSelf(X, Y);
        }

        /// <summary>
        /// 图元旋转
        /// </summary>
        public override void EleMove_r(DbPt rotCenter, double angle)
        {
            _anchorPosition.RotateSelf(rotCenter, angle);

            if (_hasTransform)
            {
                // 更新变换矩阵
                double cos = Math.Cos(angle);
                double sin = Math.Sin(angle);
                Matrix3D rotationMatrix = new Matrix3D(
                    cos, -sin, 0, 0,
                    sin, cos, 0, 0,
                    0, 0, 1, 0,
                    0, 0, 0, 1);
                _transformMatrix = Matrix3D.Multiply(rotationMatrix, _transformMatrix);
            }
        }

        /// <summary>
        /// 图元镜像
        /// </summary>
        public override void EleMove_m(DbLine mirrorLine)
        {
            // 镜像定位点
            //MirrorPoint(_anchorPosition, mirrorLine);
            _anchorPosition.MirrorSelf(mirrorLine);
            // 整合旋转到变换矩阵
            IntegrateRotationIntoTransform();

            // 更新镜像变换
            UpdateTransformMatrixForMirror(mirrorLine);

            // 重置旋转角度
            FrameAngle = 0;

            // 更新镜像状态
            _ifMirror = !_ifMirror;
        }

        /// <summary>
        /// 点镜像计算
        /// </summary>
        //private void MirrorPoint(DbPt point, DbLine mirrorLine)
        //{
        //    DbPt lineDir = new DbPt(mirrorLine.PtEnd.X - mirrorLine.PtSt.X,
        //                           mirrorLine.PtEnd.Y - mirrorLine.PtSt.Y);
        //    double lineLength = Math.Sqrt(lineDir.X * lineDir.X + lineDir.Y * lineDir.Y);

        //    if (lineLength < 0.001) return;

        //    lineDir.X /= lineLength;
        //    lineDir.Y /= lineLength;

        //    DbPt normal = new DbPt(-lineDir.Y, lineDir.X);
        //    DbPt pointToLine = new DbPt(point.X - mirrorLine.PtSt.X,
        //                               point.Y - mirrorLine.PtSt.Y);

        //    double distanceToLine = pointToLine.X * normal.X + pointToLine.Y * normal.Y;

        //    point.X = point.X - 2 * distanceToLine * normal.X;
        //    point.Y = point.Y - 2 * distanceToLine * normal.Y;
        //}

        /// <summary>
        /// 整合旋转到变换矩阵
        /// </summary>
        private void IntegrateRotationIntoTransform()
        {
            if (Math.Abs(FrameAngleRadian) > 0.001)
            {
                double cos = Math.Cos(FrameAngleRadian);
                double sin = Math.Sin(FrameAngleRadian);

                Matrix3D rotationMatrix = new Matrix3D(
                    cos, -sin, 0, 0,
                    sin, cos, 0, 0,
                    0, 0, 1, 0,
                    0, 0, 0, 1);

                if (_hasTransform)
                {
                    _transformMatrix = Matrix3D.Multiply(_transformMatrix, rotationMatrix);
                }
                else
                {
                    _transformMatrix = rotationMatrix;
                }

                _hasTransform = true;
            }
        }

        /// <summary>
        /// 更新镜像变换矩阵
        /// </summary>
        private void UpdateTransformMatrixForMirror(DbLine mirrorLine)
        {
            DbPt lineDir = new DbPt(mirrorLine.PtEnd.X - mirrorLine.PtSt.X,
                                   mirrorLine.PtEnd.Y - mirrorLine.PtSt.Y);
            double lineLength = Math.Sqrt(lineDir.X * lineDir.X + lineDir.Y * lineDir.Y);

            if (lineLength < 0.001) return;

            lineDir.X /= lineLength;
            lineDir.Y /= lineLength;

            DbPt normal = new DbPt(-lineDir.Y, lineDir.X);
            double nx = normal.X;
            double ny = normal.Y;

            Matrix3D mirrorMatrix = new Matrix3D(
                1 - 2 * nx * nx, -2 * nx * ny, 0, 0,
                -2 * nx * ny, 1 - 2 * ny * ny, 0, 0,
                0, 0, 1, 0,
                0, 0, 0, 1);

            _transformMatrix = Matrix3D.Multiply(mirrorMatrix, _transformMatrix);
            _hasTransform = true;
        }

        #endregion

        #region 数据持久化

        /// <summary>
        /// 保存数据
        /// </summary>
        public override void DataSave(BinaryWriter binaryWriter)
        {
            binaryWriter.Write(this.GetType().ToString());
            binaryWriter.Write(0); // 版本号

            // 保存基类数据
            PubSave(binaryWriter);

            // 保存截面关联
            binaryWriter.Write(_frameTypeUId);

            // 保存几何属性
            binaryWriter.Write(_frameAngle);

            // 保存定位点数据
            binaryWriter.Write(_anchorPosition.X);
            binaryWriter.Write(_anchorPosition.Y);
            binaryWriter.Write(_offsetX);
            binaryWriter.Write(_offsetY);
            // 保存显示属性
            binaryWriter.Write(_anchorRadius);
            binaryWriter.Write(_showAnchor);
            binaryWriter.Write(_ifHatch);

            // 保存变换状态
            binaryWriter.Write(_ifMirror);
            binaryWriter.Write(_hasTransform);
            if (_hasTransform)
            {
                binaryWriter.Write(_transformMatrix.M11);
                binaryWriter.Write(_transformMatrix.M12);
                binaryWriter.Write(_transformMatrix.M21);
                binaryWriter.Write(_transformMatrix.M22);
            }

            binaryWriter.Write(_ifForecDash);
            binaryWriter.Write((int)_surfaceTreatment);
        }

        /// <summary>
        /// 加载数据
        /// </summary>
        public override void DataLoad(BinaryReader binaryReader)
        {
            int version = binaryReader.ReadInt32();

            if (version == 0)
            {
                // 加载基类数据
                PubLoad(binaryReader);

                // 加载截面关联
                _frameTypeUId = binaryReader.ReadString();

                // 加载几何属性
                _frameAngle = binaryReader.ReadDouble();

                // 加载定位点数据
                _anchorPosition = new DbPt(binaryReader.ReadDouble(), binaryReader.ReadDouble());
                _offsetX = binaryReader.ReadDouble();
                _offsetY = binaryReader.ReadDouble();

                // 加载显示属性
                _anchorRadius = binaryReader.ReadDouble();
                _showAnchor = binaryReader.ReadBoolean();
                _ifHatch = binaryReader.ReadBoolean();

                // 加载变换状态
                _ifMirror = binaryReader.ReadBoolean();
                _hasTransform = binaryReader.ReadBoolean();
                if (_hasTransform)
                {
                    double m11 = binaryReader.ReadDouble();
                    double m12 = binaryReader.ReadDouble();
                    double m21 = binaryReader.ReadDouble();
                    double m22 = binaryReader.ReadDouble();

                    _transformMatrix = new Matrix3D(
                        m11, m12, 0, 0,
                        m21, m22, 0, 0,
                        0, 0, 1, 0,
                        0, 0, 0, 1);
                }

                _ifForecDash = binaryReader.ReadBoolean();
                _surfaceTreatment = (SurfaceTreatmentType)binaryReader.ReadInt32();

            }
        }

        /// <summary>
        /// 深度复制
        /// </summary>
        public override DbElement EleCopy(bool changeUid = false)
        {
            CurtainWallFrameElement copy = new CurtainWallFrameElement();

            // 复制基类数据
            PubCopy(copy);

            // 复制特有属性
            copy._anchorRadius = _anchorRadius;
            copy._showAnchor = _showAnchor;
            copy._ifHatch = _ifHatch;

            // 复制变换状态
            copy._ifMirror = _ifMirror;
            copy._hasTransform = _hasTransform;
            copy._transformMatrix = _transformMatrix;
            copy._ifForecDash = _ifForecDash;
            copy._surfaceTreatment = _surfaceTreatment;

            if (changeUid)
            {
                copy.UniqueId = Guid.NewGuid().ToString();
            }

            return copy;
        }

        #endregion

        /// <summary>
        /// 重写提示信息
        /// </summary>
        public override void EleTips(out string str1, out string str2)
        {
            str1 = "幕墙龙骨";
            
            if (FrameType != null)
            {
                string sectionInfo = GetSectionDescription();
                string offsetInfo = GetOffsetDescription();
                string anchorInfo = _showAnchor ? " [显示定位点]" : "";
                
                str2 = $"{sectionInfo}, {offsetInfo}{anchorInfo}";
            }
            else
            {
                str2 = "截面未定义";
            }
        }






        #region 定位点功能扩展
        /// <summary>
        /// 更新定位点位置（重写基类方法以同步_anchorPosition）
        /// </summary>
        public override void SetAnchorPosition(DbPt anchorPos)
        {
            base.SetAnchorPosition(anchorPos);
            _anchorPosition = anchorPos.EleCopy();
        }

        /// <summary>
        /// 获取定位点位置（重写基类方法以返回_anchorPosition）
        /// </summary>
        public override DbPt GetAnchorPosition()
        {
            return _anchorPosition.EleCopy();
        }
        #endregion

    }

