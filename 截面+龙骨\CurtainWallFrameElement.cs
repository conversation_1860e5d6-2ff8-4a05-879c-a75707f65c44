using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using GuiDB;
using EBCore;

/// <summary>
/// 🎯 幕墙龙骨图元 - 唯一具体实现类（完全参考ConcreteColumn架构）
/// 通过 FrameType.Type 区分不同截面类型：
/// Type=1: 圆角矩管, Type=2: 直角矩管, Type=3: 角钢, Type=4: 槽钢, Type=5: 工字钢, Type=50: 自定义截面
/// 通过 EnableAnchor 控制定位点功能的开启/关闭
/// </summary>
[Serializable]
[DbElement("幕墙龙骨", MajorType.CurtainWall)]
public class CurtainWallFrameElement : CurtainWallFrame
{
    #region 🔥 幕墙龙骨特有的定位点属性

    /// <summary>
    /// 定位点半径（幕墙龙骨特有）
    /// </summary>
    private double _anchorRadius = 6.0;
    [Category("定位点属性"), DisplayName("定位点半径"), Description("定位点显示图像的半径"), ReadOnly(false)]
    public double AnchorRadius
    {
        get { return _anchorRadius; }
        set
        {
            if (Math.Abs(_anchorRadius - value) < 0.001) return;
            TransManager.Instance().Push(a => AnchorRadius = a, _anchorRadius);
            _anchorRadius = value;
            if (AutoActivate && _enableAnchor) { ActCutCal2D3D(); }
        }
    }

    /// <summary>
    /// 是否显示定位点（幕墙龙骨特有）
    /// </summary>
    private bool _showAnchor = false;
    [Category("定位点属性"), DisplayName("显示定位点"), Description("是否显示定位点图形"), ReadOnly(false)]
    public bool ShowAnchor
    {
        get { return _showAnchor; }
        set
        {
            if (_showAnchor == value) return;
            TransManager.Instance().Push(a => ShowAnchor = a, _showAnchor);
            _showAnchor = value;
            if (AutoActivate && _enableAnchor) { ActCutCal2D3D(); }
        }
    }

    /// <summary>
    /// 重写基类的EnableAnchor属性，添加特有逻辑
    /// </summary>
    public override bool EnableAnchor
    {
        get { return base.EnableAnchor; }
        set
        {
            if (base.EnableAnchor == value) return;
            base.EnableAnchor = value;
            
            // 启用/禁用定位点时，重置相关参数
            if (!_enableAnchor)
            {
                _showAnchor = false;
                _offsetX = 0.0;
                _offsetY = 0.0;
            }
            else
            {
                _showAnchor = true;
                _offsetY = 30.0; // 默认偏移30mm
            }
        }
    }

    #endregion

    #region 🔥 变换矩阵（用于镜像等复杂变换）

    /// <summary>
    /// 变换矩阵 - 用于跟踪龙骨的完整变换状态（镜像等）
    /// </summary>
    private Matrix3D _transformMatrix = Matrix3D.Identity;

    /// <summary>
    /// 是否应用了变换矩阵
    /// </summary>
    private bool _hasTransform = false;

    /// <summary>
    /// 是否处于镜像状态
    /// </summary>
    private bool _isMirrored = false;

    /// <summary>
    /// 是否处于镜像状态（公共访问属性）
    /// </summary>
    [Category("状态信息"), DisplayName("镜像状态"), Description("当前图元是否处于镜像状态"), ReadOnly(true)]
    public bool IsMirrored
    {
        get { return _isMirrored; }
    }

    #endregion

    #region 🔥 显示控制

    /// <summary>
    /// 是否填充
    /// </summary>
    private bool _ifHatch = true;
    /// <summary>
    /// 是否填充
    /// </summary>
    [Category("显示属性"), DisplayName("是否填充"), Description("龙骨截面是否显示填充"), ReadOnly(false)]
    public bool IfHatch
    {
        get { return _ifHatch; }
        set
        {
            if (_ifHatch == value) return;
            TransManager.Instance().Push(a => IfHatch = a, _ifHatch);
            _ifHatch = value;
            if (AutoActivate) { ActCutCal2D3D(); }
        }
    }

    #endregion

    #region 🔥 截面类型特定功能

    /// <summary>
    /// 根据截面类型返回特定的圆角半径计算（只对矩管类型有效）
    /// </summary>
    [Category("截面参数"), DisplayName("计算圆角半径"), Description("根据壁厚自动计算的圆角半径"), ReadOnly(true)]
    public double CalculatedRadius
    {
        get
        {
            var frameType = GetCurrentFrameType();
            if (frameType == null) return 0;

            switch (frameType.Type)
            {
                case 1: // 圆角矩管
                    return CalculateRoundedTubeRadius();
                case 2: // 直角矩管  
                    return 0; // 直角无圆角
                case 3: // 角钢
                case 4: // 槽钢
                case 5: // 工字钢
                case 50: // 自定义截面
                default:
                    return 0;
            }
        }
    }

    /// <summary>
    /// 根据壁厚自动计算圆角半径（圆角矩管专用）
    /// 规则：
    /// - 当壁厚t≤3时，r=2*t
    /// - 当3<t≤6时，r=2.5*t  
    /// - 当6<t≤10时，r=3*t
    /// - 当t>10时，r=3*t
    /// </summary>
    private double CalculateRoundedTubeRadius()
    {
        double thickness = Thickness;
        if (thickness <= 3)
        {
            return 2 * thickness;
        }
        else if (thickness <= 6)
        {
            return 2.5 * thickness;
        }
        else if (thickness <= 10)
        {
            return 3 * thickness;
        }
        else
        {
            return 3 * thickness;
        }
    }

    #endregion

    #region 🔥 构造函数

    /// <summary>
    /// 无参构造函数
    /// </summary>
    public CurtainWallFrameElement() : base()
    {
        _if3D = false;
        LayerSet("幕墙龙骨");

        // 🔥 创建默认的圆角矩管截面类型（参考ConcreteColumn构造函数）
        if (FrameType == null)
        {
            var defaultFrameType = new CurtainWallFrameType(1, 60, 120, 3, 6); // Type=1表示圆角矩管
            SetFrameType(defaultFrameType);
        }
    }

    /// <summary>
    /// 🎯 标准构造函数（参考ConcreteColumn构造函数）
    /// </summary>
    /// <param name="frameType">截面类型</param>
    /// <param name="insertPt">插入点</param>
    /// <param name="frameAngle">旋转角度</param>
    /// <param name="frameLength">长度</param>
    /// <param name="enableAnchor">是否启用定位点</param>
    public CurtainWallFrameElement(CurtainWallFrameType frameType, DbPt insertPt, double frameAngle = 0, 
        double frameLength = 1000, bool enableAnchor = false) : base()
    {
        _if3D = false;
        LayerSet("幕墙龙骨");

        // 设置基本属性
        _insertPt = insertPt.EleCopy();
        FrameAngle = frameAngle;
        FrameLength = frameLength;

        // 设置截面类型
        SetFrameType(frameType);

        // 设置定位点功能
        _enableAnchor = enableAnchor;
        if (_enableAnchor)
        {
            _anchorPosition = insertPt.EleCopy();
            _showAnchor = true;
            _offsetY = 30.0; // 默认偏移30mm
        }
    }

    /// <summary>
    /// 🎯 根据龙骨位置创建（带定位点功能）
    /// </summary>
    public static CurtainWallFrameElement CreateWithAnchor(DbPt frameBottomCenter, CurtainWallFrameType frameType,
        double offsetX = 0, double offsetY = 30.0)
    {
        // 定位点位于龙骨下边中点下方offsetY处
        DbPt anchorPosition = frameBottomCenter.Move(0, -offsetY);

        var element = new CurtainWallFrameElement(frameType, frameBottomCenter, 0, 1000, true);
        element._anchorPosition = anchorPosition;
        element._offsetX = offsetX;
        element._offsetY = offsetY;

        return element;
    }

    #endregion

    #region 🔥 核心绘制逻辑（参考ConcreteColumn.Activate）

    /// <summary>
    /// 🎯 图元激活 - 完全参考ConcreteColumn的架构
    /// </summary>
    public override void Activate()
    {
        // 清空所有图形元素
        Lines.Clear();
        Circles.Clear();
        Hatchs.Clear();
        ConPts.Clear();

        // 1. 如果启用定位点，绘制定位点
        if (_enableAnchor && _showAnchor)
        {
            DrawAnchorPoint();
        }

        // 2. 根据截面类型绘制对应几何图形（核心逻辑）
        var frameType = GetCurrentFrameType();
        if (frameType != null)
        {
            switch (frameType.Type)
            {
                case 1: // 圆角矩管
                    DrawRoundedRectTube();
                    break;
                case 2: // 直角矩管
                    DrawStraightRectTube();
                    break;
                case 3: // 角钢
                    DrawAngleSteel();
                    break;
                case 4: // 槽钢
                    DrawChannelSteel();
                    break;
                case 5: // 工字钢
                    DrawIBeam();
                    break;
                case 50: // 自定义截面
                    DrawCustomSection();
                    break;
                default:
                    DrawRoundedRectTube(); // 默认绘制圆角矩管
                    break;
            }
        }

        // 3. 设置控制点（根据是否启用定位点）
        if (_enableAnchor)
        {
            SetupAnchorControlPoints();
        }
        else
        {
            SetupStandardControlPoints();
        }

        // 4. 标准后处理（参考ConcreteColumn）
        LayerChange(layerManage.GetLayer(_layerId));
        if (_styleIndex >= 0) { foreach (DbLine line in Lines) { line.StyleIndex = _styleIndex; } }
        if (_colorIndex >= 0) { foreach (DbLine line in Lines) { line.ColorIndex = _colorIndex; } }
        if (_widthIndex >= 0) { foreach (DbLine line in Lines) { line.WidthIndex = _widthIndex; } }

        EleArea = new DbEleArea(this);
        CalSolid2D();
    }

    #endregion

    #region 🔥 位置计算逻辑

    /// <summary>
    /// 🎯 计算龙骨的实际绘制位置（使用基类的通用方法）
    /// </summary>
    private DbPt CalculateDrawPosition()
    {
        return base.CalculateDrawPosition();
    }

    /// <summary>
    /// 🎯 重写基类虚拟方法：从定位点计算实际位置（考虑旋转和变换）
    /// </summary>
    protected override DbPt CalculatePositionFromAnchor()
    {
        // 计算原始偏移向量
        DbPt offsetVector = new DbPt(_offsetX, _offsetY);

        // 计算龙骨下边中点位置（定位点 + 原始偏移）
        DbPt frameBottomCenter = new DbPt(_anchorPosition.X + offsetVector.X, _anchorPosition.Y + offsetVector.Y);

        // 计算向上的方向向量
        DbPt upVector = new DbPt(0, Height / 2.0);

        // 计算龙骨几何中心位置（下边中点 + 向上偏移）
        DbPt frameCenter = new DbPt(frameBottomCenter.X + upVector.X, frameBottomCenter.Y + upVector.Y);

        // 🔥 统一变换处理，避免双重变换
        double deltaX = frameCenter.X - _anchorPosition.X;
        double deltaY = frameCenter.Y - _anchorPosition.Y;

        if (_hasTransform)
        {
            // 如果有变换矩阵，只应用一次变换
            double transformedDeltaX = _transformMatrix.M11 * deltaX + _transformMatrix.M12 * deltaY;
            double transformedDeltaY = _transformMatrix.M21 * deltaX + _transformMatrix.M22 * deltaY;

            frameCenter = new DbPt(_anchorPosition.X + transformedDeltaX, _anchorPosition.Y + transformedDeltaY);
        }
        else if (Math.Abs(FrameAngle) > 0.001)
        {
            // 如果没有变换矩阵，但有旋转角度，应用旋转变换
            double rotatedDeltaX = deltaX * Math.Cos(FrameAngle) - deltaY * Math.Sin(FrameAngle);
            double rotatedDeltaY = deltaX * Math.Sin(FrameAngle) + deltaY * Math.Cos(FrameAngle);

            frameCenter = new DbPt(_anchorPosition.X + rotatedDeltaX, _anchorPosition.Y + rotatedDeltaY);
        }

        return frameCenter;
    }

    #endregion

    #region 🔥 具体绘制方法（根据截面类型）

    /// <summary>
    /// 🎯 绘制定位点 - 显示为X形状
    /// </summary>
    private void DrawAnchorPoint()
    {
        // 绘制X形状的定位点（两条对角线）
        double halfSize = _anchorRadius * 1.5;

        // 第一条对角线（左上到右下）
        DbLine diagonalLine1 = new DbLine(_anchorPosition.Move(-halfSize, halfSize), _anchorPosition.Move(halfSize, -halfSize));

        // 第二条对角线（左下到右上）
        DbLine diagonalLine2 = new DbLine(_anchorPosition.Move(-halfSize, -halfSize), _anchorPosition.Move(halfSize, halfSize));

        diagonalLine1.SetStatus(0, 2, 0);
        diagonalLine2.SetStatus(0, 2, 0);
        diagonalLine1.LayerId = PreLayerManage.GetLayerId("通用-非打印");
        diagonalLine2.LayerId = PreLayerManage.GetLayerId("通用-非打印");
        diagonalLine1.StyleIndex = 2;
        diagonalLine2.StyleIndex = 2;
        Lines.Add(diagonalLine1);
        Lines.Add(diagonalLine2);
    }

    /// <summary>
    /// 🎯 绘制圆角矩管 (Type=1)
    /// </summary>
    private void DrawRoundedRectTube()
    {
        var frameType = GetCurrentFrameType();
        if (frameType == null) return;

        // 使用CurtainWallFrameType中已有的几何生成逻辑
        DbPt drawPosition = CalculateDrawPosition();
        
        // 获取变换后的几何图形
        var lines = frameType.RotateAndMove(FrameAngle, drawPosition.X, drawPosition.Y);
        var circles = frameType.RotateAndMoveCircles(drawPosition.X, drawPosition.Y);

        // 添加到绘制列表
        Lines.AddRange(lines);
        Circles.AddRange(circles);

        // 添加填充
        if (_ifHatch && Lines.Count > 0)
        {
            DbHatch hatch = new DbHatch(Lines, 1, 1);
            hatch.LayerId = PreLayerManage.GetLayerId("填充层");
            Hatchs.Add(hatch);
        }
    }

    /// <summary>
    /// 🎯 绘制直角矩管 (Type=2)
    /// </summary>
    private void DrawStraightRectTube()
    {
        // 与圆角矩管类似，但使用直角几何
        var frameType = GetCurrentFrameType();
        if (frameType == null) return;

        DbPt drawPosition = CalculateDrawPosition();
        
        var lines = frameType.RotateAndMove(FrameAngle, drawPosition.X, drawPosition.Y);
        var circles = frameType.RotateAndMoveCircles(drawPosition.X, drawPosition.Y);

        Lines.AddRange(lines);
        Circles.AddRange(circles);

        if (_ifHatch && Lines.Count > 0)
        {
            DbHatch hatch = new DbHatch(Lines, 1, 1);
            hatch.LayerId = PreLayerManage.GetLayerId("填充层");
            Hatchs.Add(hatch);
        }
    }

    /// <summary>
    /// 🎯 绘制角钢 (Type=3)
    /// </summary>
    private void DrawAngleSteel()
    {
        var frameType = GetCurrentFrameType();
        if (frameType == null) return;

        DbPt drawPosition = CalculateDrawPosition();
        
        var lines = frameType.RotateAndMove(FrameAngle, drawPosition.X, drawPosition.Y);
        var circles = frameType.RotateAndMoveCircles(drawPosition.X, drawPosition.Y);

        Lines.AddRange(lines);
        Circles.AddRange(circles);

        if (_ifHatch && Lines.Count > 0)
        {
            DbHatch hatch = new DbHatch(Lines, 1, 1);
            hatch.LayerId = PreLayerManage.GetLayerId("填充层");
            Hatchs.Add(hatch);
        }
    }

    /// <summary>
    /// 🎯 绘制槽钢 (Type=4)
    /// </summary>
    private void DrawChannelSteel()
    {
        var frameType = GetCurrentFrameType();
        if (frameType == null) return;

        DbPt drawPosition = CalculateDrawPosition();
        
        var lines = frameType.RotateAndMove(FrameAngle, drawPosition.X, drawPosition.Y);
        var circles = frameType.RotateAndMoveCircles(drawPosition.X, drawPosition.Y);

        Lines.AddRange(lines);
        Circles.AddRange(circles);

        if (_ifHatch && Lines.Count > 0)
        {
            DbHatch hatch = new DbHatch(Lines, 1, 1);
            hatch.LayerId = PreLayerManage.GetLayerId("填充层");
            Hatchs.Add(hatch);
        }
    }

    /// <summary>
    /// 🎯 绘制工字钢 (Type=5)
    /// </summary>
    private void DrawIBeam()
    {
        var frameType = GetCurrentFrameType();
        if (frameType == null) return;

        DbPt drawPosition = CalculateDrawPosition();
        
        var lines = frameType.RotateAndMove(FrameAngle, drawPosition.X, drawPosition.Y);
        var circles = frameType.RotateAndMoveCircles(drawPosition.X, drawPosition.Y);

        Lines.AddRange(lines);
        Circles.AddRange(circles);

        if (_ifHatch && Lines.Count > 0)
        {
            DbHatch hatch = new DbHatch(Lines, 1, 1);
            hatch.LayerId = PreLayerManage.GetLayerId("填充层");
            Hatchs.Add(hatch);
        }
    }

    /// <summary>
    /// 🎯 绘制自定义截面 (Type=50)
    /// </summary>
    private void DrawCustomSection()
    {
        var frameType = GetCurrentFrameType();
        if (frameType == null) return;

        DbPt drawPosition = CalculateDrawPosition();
        
        // 自定义截面直接使用存储的几何图形
        var lines = frameType.RotateAndMove(FrameAngle, drawPosition.X, drawPosition.Y);
        var circles = frameType.RotateAndMoveCircles(drawPosition.X, drawPosition.Y);

        Lines.AddRange(lines);
        Circles.AddRange(circles);

        if (_ifHatch && Lines.Count > 0)
        {
            DbHatch hatch = new DbHatch(Lines, 1, 1);
            hatch.LayerId = PreLayerManage.GetLayerId("填充层");
            Hatchs.Add(hatch);
        }
    }

    #endregion

    #region 🔥 控制点设置

    /// <summary>
    /// 🎯 设置定位点控制点
    /// </summary>
    private void SetupAnchorControlPoints()
    {
        ConPts.Clear();

        // 添加定位点作为主控制点
        DbPt anchorControlPt = _anchorPosition.EleCopy();
        anchorControlPt.PtType = 0; // 主控制点
        ConPts.Add(anchorControlPt);

        // 添加龙骨下边中点作为辅助控制点
        DbPt offsetVector = new DbPt(_offsetX, _offsetY);
        DbPt frameBottomCenter = new DbPt(_anchorPosition.X + offsetVector.X, _anchorPosition.Y + offsetVector.Y);

        // 统一的变换处理逻辑
        if (_hasTransform)
        {
            double deltaX = frameBottomCenter.X - _anchorPosition.X;
            double deltaY = frameBottomCenter.Y - _anchorPosition.Y;

            double transformedDeltaX = _transformMatrix.M11 * deltaX + _transformMatrix.M12 * deltaY;
            double transformedDeltaY = _transformMatrix.M21 * deltaX + _transformMatrix.M22 * deltaY;

            frameBottomCenter = new DbPt(_anchorPosition.X + transformedDeltaX, _anchorPosition.Y + transformedDeltaY);
        }
        else if (Math.Abs(FrameAngle) > 0.001)
        {
            frameBottomCenter.RotateSelf(_anchorPosition, FrameAngle);
        }

        frameBottomCenter.PtType = 1; // 辅助控制点
        ConPts.Add(frameBottomCenter);

        // 添加龙骨几何中心作为辅助控制点
        DbPt componentCenter = CalculateDrawPosition();
        componentCenter.PtType = 2; // 辅助控制点
        ConPts.Add(componentCenter);
    }

    /// <summary>
    /// 🎯 设置标准控制点（不使用定位点时）
    /// </summary>
    private void SetupStandardControlPoints()
    {
        ConPts.Clear();

        // 添加插入点作为主控制点
        DbPt insertControlPt = _insertPt.EleCopy();
        insertControlPt.PtType = 0; // 主控制点
        ConPts.Add(insertControlPt);

        // 可以根据需要添加其他控制点
    }

    #endregion

    #region 🔥 图元操作重写（参考ConcreteColumn）

    /// <summary>
    /// 🎯 图元移动
    /// </summary>
    public override void EleMove(double X, double Y)
    {
        // 如果启用定位点，移动定位点位置
        if (_enableAnchor)
        {
            _anchorPosition.MoveSelf(X, Y);
        }
        
        // 移动插入点（基类功能）
        _insertPt.MoveSelf(X, Y);
    }

    /// <summary>
    /// 🎯 图元旋转
    /// </summary>
    public override void EleMove_r(DbPt rotCenter, double angle)
    {
        // 如果启用定位点，旋转定位点位置
        if (_enableAnchor)
        {
            _anchorPosition.RotateSelf(rotCenter, angle);

            // 🔥 关键：更新构件角度
            if (_hasTransform)
            {
                // 如果已有变换矩阵（镜像后状态），直接更新变换矩阵
                double cos = Math.Cos(angle);
                double sin = Math.Sin(angle);

                Matrix3D rotationMatrix = new Matrix3D(
                    cos, -sin, 0, 0,
                    sin, cos, 0, 0,
                    0, 0, 1, 0,
                    0, 0, 0, 1
                );

                // 应用新的旋转：新旋转矩阵 × 现有变换矩阵
                _transformMatrix = Matrix3D.Multiply(rotationMatrix, _transformMatrix);
            }
            else
            {
                // 如果没有变换矩阵，更新基类的旋转角度
                FrameAngle += angle;
                // 标准化角度
                while (FrameAngle > Math.PI) FrameAngle -= 2 * Math.PI;
                while (FrameAngle < -Math.PI) FrameAngle += 2 * Math.PI;
            }

            // 直接激活
            if (AutoActivate) { ActCutCal2D3D(); }
        }
        else
        {
            // 旋转插入点（基类功能）
            _insertPt.RotateSelf(rotCenter, angle);
        }
    }

    /// <summary>
    /// 🎯 图元镜像
    /// </summary>
    public override void EleMove_m(DbLine mirrorLine)
    {
        if (_enableAnchor)
        {
            // 1. 镜像定位点位置
            MirrorPointCorrectly(_anchorPosition, mirrorLine);

            // 2. 将旋转角度信息整合到变换矩阵中
            IntegrateRotationIntoTransform();

            // 3. 更新变换矩阵以包含镜像
            UpdateTransformMatrixForMirror(mirrorLine);

            // 4. 镜像后重置旋转角度，因为旋转信息已经整合到变换矩阵中
            FrameAngle = 0;

            // 5. 设置镜像状态标识
            _isMirrored = !_isMirrored;
        }

        // 6. 镜像插入点（基类功能）
        MirrorPointCorrectly(_insertPt, mirrorLine);
    }

    /// <summary>
    /// 🎯 正确的点镜像计算方法
    /// </summary>
    private void MirrorPointCorrectly(DbPt point, DbLine mirrorLine)
    {
        // 计算镜像线的方向向量和法向量
        DbPt lineDir = new DbPt(mirrorLine.PtEnd.X - mirrorLine.PtSt.X,
                               mirrorLine.PtEnd.Y - mirrorLine.PtSt.Y);
        double lineLength = Math.Sqrt(lineDir.X * lineDir.X + lineDir.Y * lineDir.Y);

        if (lineLength < 0.001) return; // 镜像线长度为0，无法镜像

        // 归一化方向向量
        lineDir.X /= lineLength;
        lineDir.Y /= lineLength;

        // 计算法向量（垂直于镜像线）
        DbPt normal = new DbPt(-lineDir.Y, lineDir.X);

        // 计算点到镜像线上任意一点的向量
        DbPt pointToLine = new DbPt(point.X - mirrorLine.PtSt.X,
                                   point.Y - mirrorLine.PtSt.Y);

        // 计算点到镜像线的有向距离（沿法向量方向）
        double distanceToLine = pointToLine.X * normal.X + pointToLine.Y * normal.Y;

        // 计算镜像后的点位置
        point.X = point.X - 2 * distanceToLine * normal.X;
        point.Y = point.Y - 2 * distanceToLine * normal.Y;
    }

    /// <summary>
    /// 🎯 将旋转角度信息整合到变换矩阵中
    /// </summary>
    private void IntegrateRotationIntoTransform()
    {
        if (Math.Abs(FrameAngle) > 0.001)
        {
            // 创建旋转矩阵
            double cos = Math.Cos(FrameAngle);
            double sin = Math.Sin(FrameAngle);

            Matrix3D rotationMatrix = new Matrix3D(
                cos, -sin, 0, 0,
                sin, cos, 0, 0,
                0, 0, 1, 0,
                0, 0, 0, 1
            );

            // 组合变换矩阵
            if (_hasTransform)
            {
                _transformMatrix = Matrix3D.Multiply(_transformMatrix, rotationMatrix);
            }
            else
            {
                _transformMatrix = rotationMatrix;
            }

            _hasTransform = true;
        }
    }

    /// <summary>
    /// 🎯 更新变换矩阵以包含镜像
    /// </summary>
    private void UpdateTransformMatrixForMirror(DbLine mirrorLine)
    {
        // 计算镜像线的方向向量
        DbPt lineDir = new DbPt(mirrorLine.PtEnd.X - mirrorLine.PtSt.X,
                               mirrorLine.PtEnd.Y - mirrorLine.PtSt.Y);
        double lineLength = Math.Sqrt(lineDir.X * lineDir.X + lineDir.Y * lineDir.Y);

        if (lineLength < 0.001) return;

        // 归一化方向向量
        lineDir.X /= lineLength;
        lineDir.Y /= lineLength;

        // 计算法向量
        DbPt normal;
        if (Math.Abs(lineDir.X) < 0.001) // 垂直线
        {
            normal = new DbPt(1, 0);
        }
        else if (Math.Abs(lineDir.Y) < 0.001) // 水平线
        {
            normal = new DbPt(0, 1);
        }
        else
        {
            normal = new DbPt(-lineDir.Y, lineDir.X);
        }

        // 使用标准镜像矩阵公式：I - 2 * n * n^T
        double nx = normal.X;
        double ny = normal.Y;

        Matrix3D mirrorMatrix = new Matrix3D(
            1 - 2 * nx * nx, -2 * nx * ny, 0, 0,
            -2 * nx * ny, 1 - 2 * ny * ny, 0, 0,
            0, 0, 1, 0,
            0, 0, 0, 1
        );

        // 组合变换矩阵：镜像矩阵 × 旋转矩阵
        _transformMatrix = Matrix3D.Multiply(mirrorMatrix, _transformMatrix);
        _hasTransform = true;
    }

    #endregion

    #region 🔥 数据持久化（参考ConcreteColumn）

    /// <summary>
    /// 🎯 保存数据
    /// </summary>
    public override void DataSave(BinaryWriter binaryWriter)
    {
        base.DataSave(binaryWriter);

        // 版本号
        binaryWriter.Write(1);

        // 子类特有的定位点属性
        binaryWriter.Write(_anchorRadius);
        binaryWriter.Write(_showAnchor);

        // 显示属性
        binaryWriter.Write(_ifHatch);

        // 变换矩阵数据
        binaryWriter.Write(_isMirrored);
        binaryWriter.Write(_hasTransform);
        if (_hasTransform)
        {
            binaryWriter.Write(_transformMatrix.M11);
            binaryWriter.Write(_transformMatrix.M12);
            binaryWriter.Write(_transformMatrix.M21);
            binaryWriter.Write(_transformMatrix.M22);
        }
    }

    /// <summary>
    /// 🎯 加载数据
    /// </summary>
    public override void DataLoad(BinaryReader binaryReader)
    {
        base.DataLoad(binaryReader);

        // 读取版本号
        int verNum = binaryReader.ReadInt32();

        // 子类特有的定位点属性
        _anchorRadius = binaryReader.ReadDouble();
        _showAnchor = binaryReader.ReadBoolean();

        // 显示属性
        _ifHatch = binaryReader.ReadBoolean();

        // 变换矩阵数据
        _isMirrored = binaryReader.ReadBoolean();
        _hasTransform = binaryReader.ReadBoolean();
        if (_hasTransform)
        {
            double m11 = binaryReader.ReadDouble();
            double m12 = binaryReader.ReadDouble();
            double m21 = binaryReader.ReadDouble();
            double m22 = binaryReader.ReadDouble();

            _transformMatrix = new Matrix3D(
                m11, m12, 0, 0,
                m21, m22, 0, 0,
                0, 0, 1, 0,
                0, 0, 0, 1
            );
        }
    }

    /// <summary>
    /// 🎯 深度复制（参考ConcreteColumn）
    /// </summary>
    public override DbElement EleCopy(bool changeUid = false)
    {
        var copy = new CurtainWallFrameElement();

        // 复制基类属性
        copy._insertPt = _insertPt.EleCopy();
        copy._frameTypeUId = _frameTypeUId;
        copy.FrameAngle = FrameAngle;
        copy.FrameLength = FrameLength;

        // 复制基类定位点功能属性
        copy._enableAnchor = _enableAnchor;
        copy._anchorPosition = _anchorPosition.EleCopy();
        copy._offsetX = _offsetX;
        copy._offsetY = _offsetY;

        // 复制子类特有的定位点属性
        copy._anchorRadius = _anchorRadius;
        copy._showAnchor = _showAnchor;

        // 复制显示属性
        copy._ifHatch = _ifHatch;

        // 复制变换矩阵
        copy._isMirrored = _isMirrored;
        copy._hasTransform = _hasTransform;
        copy._transformMatrix = _transformMatrix;

        // 复制基类虚拟截面状态
        copy._useVirtualType = _useVirtualType;
        if (_virtualFrameType != null)
        {
            copy._virtualFrameType = _virtualFrameType.Clone();
        }

        if (changeUid)
        {
            copy.UniqueId = Guid.NewGuid().ToString();
        }

        return copy;
    }

    #endregion

    #region 🔥 调试和信息方法（参考ConcreteColumn）

    /// <summary>
    /// 🎯 重写提示信息
    /// </summary>
    public override void EleTips(out string str1, out string str2)
    {
        var frameType = GetCurrentFrameType();
        if (frameType != null)
        {
            str1 = $"幕墙龙骨: {frameType.GetSecName()}";
            
            string anchorInfo = _enableAnchor ? " [定位点]" : "";
            string customInfo = CustomizationStatus;
            
            switch (frameType.Type)
            {
                case 1: // 圆角矩管
                    str2 = $"圆角矩管 {Width}×{Height}×{Thickness}mm, R{CalculatedRadius:F1}mm{customInfo}{anchorInfo}";
                    break;
                case 2: // 直角矩管
                    str2 = $"直角矩管 {Width}×{Height}×{Thickness}mm{customInfo}{anchorInfo}";
                    break;
                case 3: // 角钢
                    str2 = $"角钢 {Width}×{Height}×{Thickness}mm{customInfo}{anchorInfo}";
                    break;
                case 4: // 槽钢
                    str2 = $"槽钢 {Width}×{Height}×{Thickness}mm{customInfo}{anchorInfo}";
                    break;
                case 5: // 工字钢
                    str2 = $"工字钢 {Width}×{Height}×{Thickness}mm{customInfo}{anchorInfo}";
                    break;
                case 50: // 自定义截面
                    str2 = $"自定义截面{customInfo}{anchorInfo}";
                    break;
                default:
                    str2 = $"未知截面{customInfo}{anchorInfo}";
                    break;
            }
        }
        else
        {
            str1 = "幕墙龙骨";
            str2 = "截面未定义";
        }
    }

    #endregion
} 