using System;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// 🎯 角钢系统使用示例和测试类
/// 演示如何在现有系统中正确使用角钢功能
/// </summary>
public class AngleSteelUsageExample
{
    #region 🔥 基本使用示例

    /// <summary>
    /// 🎯 示例1：创建标准角钢
    /// </summary>
    public static void Example1_CreateStandardAngleSteel()
    {
        Console.WriteLine("=== 示例1：创建标准角钢 ===");
        
        try
        {
            // 1. 创建标准L50×5角钢截面类型
            var angleFrameType = new CurtainWallFrameType();
            
            // 设置为角钢类型
            angleFrameType.Type = 3;
            
            // 🔥 使用扩展方法设置标准角钢参数
            angleFrameType.SetAngleSteelParameters(50, 5, forceStandard: true);
            
            Console.WriteLine($"创建角钢规格: {angleFrameType.GetSecName()}");
            Console.WriteLine($"是否标准规格: {angleFrameType.IsStandardAngleSteel}");
            if (angleFrameType.IsStandardAngleSteel)
            {
                var spec = angleFrameType.AngleSpec;
                Console.WriteLine($"标准信息: {spec.GetFullDescription()}");
                Console.WriteLine($"截面积: {spec.Area} cm²");
                Console.WriteLine($"理论重量: {spec.WeightPerMeter} kg/m");
                Console.WriteLine($"顶端圆弧半径: {spec.TopRadius} mm");
                Console.WriteLine($"中间圆弧半径: {spec.MiddleRadius} mm");
            }
            
            // 2. 创建角钢图元
            var insertPoint = new DbPt(1000, 2000);
            var angleElement = new CurtainWallFrameElement(angleFrameType, insertPoint, 0, 3000, false);
            
            Console.WriteLine($"角钢图元创建成功，标准规格: {angleElement.AngleStandardSpec}");
            Console.WriteLine($"每米重量: {angleElement.AngleWeightPerMeter} kg/m");
            
        }
        catch (Exception ex)
        {
            Console.WriteLine($"创建失败: {ex.Message}");
        }
        
        Console.WriteLine();
    }

    /// <summary>
    /// 🎯 示例2：处理非标准角钢参数
    /// </summary>
    public static void Example2_HandleNonStandardParameters()
    {
        Console.WriteLine("=== 示例2：处理非标准角钢参数 ===");
        
        try
        {
            var angleFrameType = new CurtainWallFrameType();
            angleFrameType.Type = 3;
            
            // 🔥 设置参数调整事件处理
            angleFrameType.OnParameterAdjusted += (message) =>
            {
                Console.WriteLine($"参数调整通知: {message}");
            };
            
            // 尝试设置非标准参数 L52×6（不存在的规格）
            Console.WriteLine("尝试设置非标准参数 L52×6...");
            angleFrameType.SetAngleSteelParameters(52, 6, forceStandard: true);
            
            Console.WriteLine($"实际创建的规格: {angleFrameType.GetSecName()}");
            Console.WriteLine($"是否标准规格: {angleFrameType.IsStandardAngleSteel}");
            
        }
        catch (Exception ex)
        {
            Console.WriteLine($"处理失败: {ex.Message}");
        }
        
        Console.WriteLine();
    }

    /// <summary>
    /// 🎯 示例3：角钢参数验证
    /// </summary>
    public static void Example3_ValidateAngleParameters()
    {
        Console.WriteLine("=== 示例3：角钢参数验证 ===");
        
        // 创建几个不同的角钢进行验证
        var testCases = new[]
        {
            new { SideLength = 50, Thickness = 5, Description = "标准 L50×5" },
            new { SideLength = 52, Thickness = 6, Description = "非标准 L52×6" },
            new { SideLength = 50, Thickness = 45, Description = "厚度过大 L50×45" },
            new { SideLength = 63, Thickness = 8, Description = "标准 L63×8" }
        };
        
        foreach (var testCase in testCases)
        {
            Console.WriteLine($"\\n测试 {testCase.Description}:");
            
            try
            {
                var angleFrameType = new CurtainWallFrameType();
                angleFrameType.Type = 3;
                angleFrameType.SetAngleSteelParameters(testCase.SideLength, testCase.Thickness, forceStandard: false);
                
                // 使用扩展方法验证参数
                var validation = angleFrameType.ValidateAngleSteelParameters();
                
                Console.WriteLine($"  验证结果: {(validation.IsValid ? "通过" : "失败")}");
                
                if (validation.Errors.Count > 0)
                {
                    Console.WriteLine($"  错误: {string.Join("; ", validation.Errors)}");
                }
                
                if (validation.Warnings.Count > 0)
                {
                    Console.WriteLine($"  警告: {string.Join("; ", validation.Warnings)}");
                }
                
                Console.WriteLine($"  是否标准规格: {AngleSteelStandards.IsStandardSpec(testCase.SideLength, testCase.Thickness)}");
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  测试失败: {ex.Message}");
            }
        }
        
        Console.WriteLine();
    }

    /// <summary>
    /// 🎯 示例4：角钢图元的属性操作
    /// </summary>
    public static void Example4_AngleElementProperties()
    {
        Console.WriteLine("=== 示例4：角钢图元的属性操作 ===");
        
        try
        {
            // 创建标准角钢
            var angleFrameType = new CurtainWallFrameType();
            angleFrameType.Type = 3;
            angleFrameType.SetAngleSteelParameters(75, 8, forceStandard: true);
            
            var angleElement = new CurtainWallFrameElement(angleFrameType, new DbPt(0, 0), 0, 4000, false);
            
            Console.WriteLine("初始状态:");
            Console.WriteLine($"  边长: {angleElement.AngleSideLength} mm");
            Console.WriteLine($"  厚度: {angleElement.Thickness} mm");
            Console.WriteLine($"  标准规格: {angleElement.AngleStandardSpec}");
            Console.WriteLine($"  是否标准: {angleElement.IsStandardAngle}");
            Console.WriteLine($"  每米重量: {angleElement.AngleWeightPerMeter} kg/m");
            
            // 🔥 注册非标准参数检测事件
            angleElement.OnNonStandardAngleParameterDetected += (sideLength, thickness, nearestSpec) =>
            {
                Console.WriteLine($"  检测到非标准参数 L{sideLength}×{thickness}");
                Console.WriteLine($"  建议使用标准规格: {nearestSpec?.GetStandardName()}");
            };
            
            Console.WriteLine("\\n修改边长为76mm (非标准):");
            angleElement.AngleSideLength = 76;
            
            Console.WriteLine($"  修改后边长: {angleElement.AngleSideLength} mm");
            Console.WriteLine($"  是否标准: {angleElement.IsStandardAngle}");
            Console.WriteLine($"  标准规格: {angleElement.AngleStandardSpec}");
            
            // 验证参数
            var validation = angleElement.ValidateAngleParameters();
            if (!validation.IsValid || validation.HasWarnings)
            {
                Console.WriteLine($"  验证结果: {validation}");
            }
            
            Console.WriteLine("\\n重置为标准规格:");
            angleElement.ResetToStandardAngleSpec();
            
            Console.WriteLine($"  重置后边长: {angleElement.AngleSideLength} mm");
            Console.WriteLine($"  标准规格: {angleElement.AngleStandardSpec}");
            Console.WriteLine($"  是否标准: {angleElement.IsStandardAngle}");
            
        }
        catch (Exception ex)
        {
            Console.WriteLine($"操作失败: {ex.Message}");
        }
        
        Console.WriteLine();
    }

    /// <summary>
    /// 🎯 示例5：获取可用规格列表
    /// </summary>
    public static void Example5_GetAvailableSpecifications()
    {
        Console.WriteLine("=== 示例5：获取可用规格列表 ===");
        
        // 获取所有可用边长
        var availableSideLengths = AngleSteelStandards.GetAvailableSideLengths();
        Console.WriteLine($"可用边长数量: {availableSideLengths.Count}");
        Console.WriteLine($"边长范围: {availableSideLengths.First()}mm - {availableSideLengths.Last()}mm");
        Console.WriteLine($"常用边长: {string.Join(", ", availableSideLengths.Take(10))}mm...");
        
        // 获取特定边长的可用厚度
        int testSideLength = 50;
        var availableThicknesses = AngleSteelStandards.GetAvailableThicknesses(testSideLength);
        Console.WriteLine($"\\nL{testSideLength}可用厚度: {string.Join(", ", availableThicknesses)}mm");
        
        // 显示几个典型规格的详细信息
        Console.WriteLine("\\n典型角钢规格:");
        var typicalSpecs = new[] { 40, 50, 63, 75, 100, 125 };
        
        foreach (var sideLength in typicalSpecs)
        {
            var thicknesses = AngleSteelStandards.GetAvailableThicknesses(sideLength);
            if (thicknesses.Count > 0)
            {
                Console.WriteLine($"  L{sideLength}: {string.Join(", ", thicknesses.Select(t => $"{t}mm"))}");
            }
        }
        
        Console.WriteLine();
    }

    #endregion

    #region 🔥 综合测试方法

    /// <summary>
    /// 🎯 运行所有角钢系统测试
    /// </summary>
    public static void RunAllTests()
    {
        Console.WriteLine("🔥 角钢系统集成测试开始");
        Console.WriteLine(new string('=', 50));
        
        try
        {
            Example1_CreateStandardAngleSteel();
            Example2_HandleNonStandardParameters();
            Example3_ValidateAngleParameters();
            Example4_AngleElementProperties();
            Example5_GetAvailableSpecifications();
            
            Console.WriteLine("✅ 所有测试完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 测试过程中发生错误: {ex.Message}");
            Console.WriteLine($"详细信息: {ex.StackTrace}");
        }
        
        Console.WriteLine(new string('=', 50));
    }

    /// <summary>
    /// 🎯 性能测试：大量角钢创建
    /// </summary>
    public static void PerformanceTest_CreateManyAngles()
    {
        Console.WriteLine("=== 性能测试：大量角钢创建 ===");
        
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        int testCount = 1000;
        
        try
        {
            var createdAngles = new List<CurtainWallFrameElement>();
            var availableSpecs = AngleSteelStandards.GetAllStandardSpecs().Take(10).ToList();
            
            for (int i = 0; i < testCount; i++)
            {
                var spec = availableSpecs[i % availableSpecs.Count];
                
                var angleFrameType = new CurtainWallFrameType();
                angleFrameType.Type = 3;
                angleFrameType.SetAngleSteelParameters(spec.SideLength, spec.Thickness, forceStandard: true);
                
                var angleElement = new CurtainWallFrameElement(
                    angleFrameType, 
                    new DbPt(i * 100, i * 50), 
                    i * 0.1, 
                    3000 + i, 
                    false
                );
                
                createdAngles.Add(angleElement);
            }
            
            stopwatch.Stop();
            
            Console.WriteLine($"创建 {testCount} 个角钢耗时: {stopwatch.ElapsedMilliseconds} ms");
            Console.WriteLine($"平均每个角钢: {(double)stopwatch.ElapsedMilliseconds / testCount:F2} ms");
            Console.WriteLine($"标准规格角钢数量: {createdAngles.Count(a => a.IsStandardAngle)}");
            
        }
        catch (Exception ex)
        {
            Console.WriteLine($"性能测试失败: {ex.Message}");
        }
        
        Console.WriteLine();
    }

    #endregion

    #region 🔥 集成到现有系统的指导

    /// <summary>
    /// 🎯 集成指导：如何在现有项目中使用角钢系统
    /// </summary>
    public static void IntegrationGuidance()
    {
        Console.WriteLine("=== 集成指导：如何在现有项目中使用角钢系统 ===");
        Console.WriteLine();
        
        Console.WriteLine("1. 📁 文件集成:");
        Console.WriteLine("   - 将 AngleSteelStandards.cs 添加到项目");
        Console.WriteLine("   - 将 CurtainWallFrameType_AngleSteel_Extension.cs 添加到项目");
        Console.WriteLine("   - 将 CurtainWallFrameElement_AngleSteel_Extension.cs 添加到项目");
        Console.WriteLine();
        
        Console.WriteLine("2. 🔄 现有代码修改:");
        Console.WriteLine("   - 在 CurtainWallFrameType.GenerateStandardGeometry() 中");
        Console.WriteLine("     将 case 3 的处理改为调用新的 GenerateAngleSteel() 方法");
        Console.WriteLine("   - 确保 CurtainWallFrameElement 继承了角钢扩展功能");
        Console.WriteLine();
        
        Console.WriteLine("3. 🎨 UI界面适配:");
        Console.WriteLine("   - 属性栏中添加角钢专用属性显示");
        Console.WriteLine("   - 边长选择下拉框，从 GetAvailableSideLengths() 获取选项");
        Console.WriteLine("   - 厚度选择下拉框，根据边长动态更新选项");
        Console.WriteLine("   - 添加标准规格验证提示");
        Console.WriteLine();
        
        Console.WriteLine("4. 📊 数据持久化:");
        Console.WriteLine("   - 确保 AngleSteelSpec 信息正确保存和加载");
        Console.WriteLine("   - 虚拟截面的角钢特殊参数处理");
        Console.WriteLine();
        
        Console.WriteLine("5. 🔍 使用建议:");
        Console.WriteLine("   - 优先使用标准规格，forceStandard=true");
        Console.WriteLine("   - 非标准参数时，提供用户选择机制");
        Console.WriteLine("   - 利用验证机制给用户及时反馈");
        Console.WriteLine("   - 注册事件处理器，提供良好的用户体验");
        Console.WriteLine();
        
        Console.WriteLine("6. 🚀 性能优化:");
        Console.WriteLine("   - AngleSteelStandards 数据为静态，无需重复加载");
        Console.WriteLine("   - 几何缓存机制对角钢同样有效");
        Console.WriteLine("   - 标准规格查询已优化，支持大量并发访问");
        Console.WriteLine();
    }

    #endregion

    #region 🔥 主测试入口

    /// <summary>
    /// 🎯 主测试方法
    /// </summary>
    public static void Main()
    {
        Console.WriteLine("🏗️ 角钢系统集成测试程序");
        Console.WriteLine($"测试时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        Console.WriteLine();
        
        // 运行所有示例
        RunAllTests();
        
        // 运行性能测试
        PerformanceTest_CreateManyAngles();
        
        // 显示集成指导
        IntegrationGuidance();
        
        Console.WriteLine("🎯 测试程序结束，按任意键退出...");
        Console.ReadKey();
    }

    #endregion
}