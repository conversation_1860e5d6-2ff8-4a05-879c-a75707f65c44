using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SQLite;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

/// <summary>
/// 🎯 国标截面数据库访问类
/// 提供对SQLite数据库的完整访问接口
/// </summary>
public class StandardSectionDatabase
{
    #region 🔥 数据库连接管理

    private readonly string _connectionString;
    private readonly string _databasePath;
    
    /// <summary>
    /// 数据库文件默认路径
    /// </summary>
    public static string DefaultDatabasePath => Path.Combine(
        Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
        "CurtainWallFrameSystem", 
        "StandardSections.db"
    );

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="databasePath">数据库文件路径，为空则使用默认路径</param>
    public StandardSectionDatabase(string databasePath = null)
    {
        _databasePath = databasePath ?? DefaultDatabasePath;
        _connectionString = $"Data Source={_databasePath};Version=3;";
        
        // 确保数据库目录存在
        var directory = Path.GetDirectoryName(_databasePath);
        if (!Directory.Exists(directory))
        {
            Directory.CreateDirectory(directory);
        }
    }

    /// <summary>
    /// 获取数据库连接
    /// </summary>
    /// <returns>SQLite连接对象</returns>
    private SQLiteConnection GetConnection()
    {
        return new SQLiteConnection(_connectionString);
    }

    /// <summary>
    /// 检查数据库是否存在且有效
    /// </summary>
    /// <returns>是否有效</returns>
    public bool IsDatabaseValid()
    {
        try
        {
            if (!File.Exists(_databasePath)) return false;
            
            using (var connection = GetConnection())
            {
                connection.Open();
                
                // 检查关键表是否存在
                var cmd = new SQLiteCommand("SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='StandardSections'", connection);
                var result = cmd.ExecuteScalar();
                return Convert.ToInt32(result) > 0;
            }
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 初始化数据库（创建表结构）
    /// </summary>
    public async Task<bool> InitializeDatabaseAsync()
    {
        try
        {
            using (var connection = GetConnection())
            {
                await connection.OpenAsync();
                
                // 读取SQL脚本并执行
                var schemaScript = GetSchemaScript();
                var commands = schemaScript.Split(new[] { ";" }, StringSplitOptions.RemoveEmptyEntries);
                
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        foreach (var commandText in commands)
                        {
                            if (string.IsNullOrWhiteSpace(commandText)) continue;
                            
                            using (var cmd = new SQLiteCommand(commandText.Trim(), connection, transaction))
                            {
                                await cmd.ExecuteNonQueryAsync();
                            }
                        }
                        
                        transaction.Commit();
                        return true;
                    }
                    catch
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            throw new DatabaseInitializationException($"数据库初始化失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 获取数据库架构脚本
    /// </summary>
    private string GetSchemaScript()
    {
        // 在实际项目中，这里应该从嵌入资源或外部文件读取
        // 这里为了简化，直接返回基本的表创建脚本
        return @"
            -- 如果需要，可以在这里加载完整的schema脚本
            -- 或者从 StandardSections_Schema.sql 文件读取
        ";
    }

    #endregion

    #region 🔥 截面类型管理

    /// <summary>
    /// 获取所有截面类型
    /// </summary>
    public async Task<List<SectionTypeInfo>> GetSectionTypesAsync()
    {
        var sectionTypes = new List<SectionTypeInfo>();
        
        using (var connection = GetConnection())
        {
            await connection.OpenAsync();
            
            var sql = @"
                SELECT TypeId, TypeCode, TypeName, TypeNameEn, Standard, Description, SortOrder
                FROM SectionTypes 
                WHERE IsActive = 1 
                ORDER BY SortOrder, TypeName";
                
            using (var cmd = new SQLiteCommand(sql, connection))
            using (var reader = await cmd.ExecuteReaderAsync())
            {
                while (await reader.ReadAsync())
                {
                    sectionTypes.Add(new SectionTypeInfo
                    {
                        TypeId = reader.GetInt32("TypeId"),
                        TypeCode = reader.GetString("TypeCode"),
                        TypeName = reader.GetString("TypeName"),
                        TypeNameEn = reader.IsDBNull("TypeNameEn") ? null : reader.GetString("TypeNameEn"),
                        Standard = reader.GetString("Standard"),
                        Description = reader.IsDBNull("Description") ? null : reader.GetString("Description"),
                        SortOrder = reader.GetInt32("SortOrder")
                    });
                }
            }
        }
        
        return sectionTypes;
    }

    /// <summary>
    /// 根据类型代码获取截面类型
    /// </summary>
    public async Task<SectionTypeInfo> GetSectionTypeAsync(string typeCode)
    {
        using (var connection = GetConnection())
        {
            await connection.OpenAsync();
            
            var sql = @"
                SELECT TypeId, TypeCode, TypeName, TypeNameEn, Standard, Description, SortOrder
                FROM SectionTypes 
                WHERE TypeCode = @TypeCode AND IsActive = 1";
                
            using (var cmd = new SQLiteCommand(sql, connection))
            {
                cmd.Parameters.AddWithValue("@TypeCode", typeCode);
                
                using (var reader = await cmd.ExecuteReaderAsync())
                {
                    if (await reader.ReadAsync())
                    {
                        return new SectionTypeInfo
                        {
                            TypeId = reader.GetInt32("TypeId"),
                            TypeCode = reader.GetString("TypeCode"),
                            TypeName = reader.GetString("TypeName"),
                            TypeNameEn = reader.IsDBNull("TypeNameEn") ? null : reader.GetString("TypeNameEn"),
                            Standard = reader.GetString("Standard"),
                            Description = reader.IsDBNull("Description") ? null : reader.GetString("Description"),
                            SortOrder = reader.GetInt32("SortOrder")
                        };
                    }
                }
            }
        }
        
        return null;
    }

    #endregion

    #region 🔥 标准截面数据查询

    /// <summary>
    /// 根据截面类型获取所有标准截面
    /// </summary>
    public async Task<List<StandardSectionInfo>> GetStandardSectionsAsync(string typeCode)
    {
        var sections = new List<StandardSectionInfo>();
        
        using (var connection = GetConnection())
        {
            await connection.OpenAsync();
            
            var sql = @"
                SELECT s.*, st.TypeCode, st.TypeName, st.Standard
                FROM StandardSections s
                INNER JOIN SectionTypes st ON s.TypeId = st.TypeId
                WHERE st.TypeCode = @TypeCode AND s.IsActive = 1 AND st.IsActive = 1
                ORDER BY s.SortOrder, s.B, s.H, s.T";
                
            using (var cmd = new SQLiteCommand(sql, connection))
            {
                cmd.Parameters.AddWithValue("@TypeCode", typeCode);
                
                using (var reader = await cmd.ExecuteReaderAsync())
                {
                    while (await reader.ReadAsync())
                    {
                        sections.Add(MapToStandardSectionInfo(reader));
                    }
                }
            }
        }
        
        return sections;
    }

    /// <summary>
    /// 根据精确参数查找标准截面
    /// </summary>
    public async Task<StandardSectionInfo> FindStandardSectionAsync(string typeCode, double b, double h = 0, double t = 0)
    {
        using (var connection = GetConnection())
        {
            await connection.OpenAsync();
            
            var sql = @"
                SELECT s.*, st.TypeCode, st.TypeName, st.Standard
                FROM StandardSections s
                INNER JOIN SectionTypes st ON s.TypeId = st.TypeId
                WHERE st.TypeCode = @TypeCode 
                  AND ABS(s.B - @B) < 0.01 
                  AND ABS(s.H - @H) < 0.01 
                  AND ABS(s.T - @T) < 0.01
                  AND s.IsActive = 1 AND st.IsActive = 1
                LIMIT 1";
                
            using (var cmd = new SQLiteCommand(sql, connection))
            {
                cmd.Parameters.AddWithValue("@TypeCode", typeCode);
                cmd.Parameters.AddWithValue("@B", b);
                cmd.Parameters.AddWithValue("@H", h);
                cmd.Parameters.AddWithValue("@T", t);
                
                using (var reader = await cmd.ExecuteReaderAsync())
                {
                    if (await reader.ReadAsync())
                    {
                        return MapToStandardSectionInfo(reader);
                    }
                }
            }
        }
        
        return null;
    }

    /// <summary>
    /// 查找最近的标准截面
    /// </summary>
    public async Task<StandardSectionInfo> FindNearestStandardSectionAsync(string typeCode, double b, double h = 0, double t = 0)
    {
        using (var connection = GetConnection())
        {
            await connection.OpenAsync();
            
            var sql = @"
                SELECT s.*, st.TypeCode, st.TypeName, st.Standard,
                       (ABS(s.B - @B) + ABS(s.H - @H) + ABS(s.T - @T)) as Distance
                FROM StandardSections s
                INNER JOIN SectionTypes st ON s.TypeId = st.TypeId
                WHERE st.TypeCode = @TypeCode AND s.IsActive = 1 AND st.IsActive = 1
                ORDER BY Distance
                LIMIT 1";
                
            using (var cmd = new SQLiteCommand(sql, connection))
            {
                cmd.Parameters.AddWithValue("@TypeCode", typeCode);
                cmd.Parameters.AddWithValue("@B", b);
                cmd.Parameters.AddWithValue("@H", h);
                cmd.Parameters.AddWithValue("@T", t);
                
                using (var reader = await cmd.ExecuteReaderAsync())
                {
                    if (await reader.ReadAsync())
                    {
                        return MapToStandardSectionInfo(reader);
                    }
                }
            }
        }
        
        return null;
    }

    /// <summary>
    /// 获取常用截面
    /// </summary>
    public async Task<List<StandardSectionInfo>> GetCommonSectionsAsync(string typeCode = null)
    {
        var sections = new List<StandardSectionInfo>();
        
        using (var connection = GetConnection())
        {
            await connection.OpenAsync();
            
            var sql = @"
                SELECT s.*, st.TypeCode, st.TypeName, st.Standard
                FROM StandardSections s
                INNER JOIN SectionTypes st ON s.TypeId = st.TypeId
                WHERE s.IsCommon = 1 AND s.IsActive = 1 AND st.IsActive = 1";
                
            if (!string.IsNullOrEmpty(typeCode))
            {
                sql += " AND st.TypeCode = @TypeCode";
            }
            
            sql += " ORDER BY st.SortOrder, s.SortOrder, s.B, s.H, s.T";
                
            using (var cmd = new SQLiteCommand(sql, connection))
            {
                if (!string.IsNullOrEmpty(typeCode))
                {
                    cmd.Parameters.AddWithValue("@TypeCode", typeCode);
                }
                
                using (var reader = await cmd.ExecuteReaderAsync())
                {
                    while (await reader.ReadAsync())
                    {
                        sections.Add(MapToStandardSectionInfo(reader));
                    }
                }
            }
        }
        
        return sections;
    }

    /// <summary>
    /// 根据尺寸范围查询截面
    /// </summary>
    public async Task<List<StandardSectionInfo>> GetSectionsByRangeAsync(string typeCode, 
        double minB = 0, double maxB = double.MaxValue,
        double minH = 0, double maxH = double.MaxValue,
        double minT = 0, double maxT = double.MaxValue)
    {
        var sections = new List<StandardSectionInfo>();
        
        using (var connection = GetConnection())
        {
            await connection.OpenAsync();
            
            var sql = @"
                SELECT s.*, st.TypeCode, st.TypeName, st.Standard
                FROM StandardSections s
                INNER JOIN SectionTypes st ON s.TypeId = st.TypeId
                WHERE st.TypeCode = @TypeCode 
                  AND s.B >= @MinB AND s.B <= @MaxB
                  AND s.H >= @MinH AND s.H <= @MaxH  
                  AND s.T >= @MinT AND s.T <= @MaxT
                  AND s.IsActive = 1 AND st.IsActive = 1
                ORDER BY s.B, s.H, s.T";
                
            using (var cmd = new SQLiteCommand(sql, connection))
            {
                cmd.Parameters.AddWithValue("@TypeCode", typeCode);
                cmd.Parameters.AddWithValue("@MinB", minB);
                cmd.Parameters.AddWithValue("@MaxB", maxB);
                cmd.Parameters.AddWithValue("@MinH", minH);
                cmd.Parameters.AddWithValue("@MaxH", maxH);
                cmd.Parameters.AddWithValue("@MinT", minT);
                cmd.Parameters.AddWithValue("@MaxT", maxT);
                
                using (var reader = await cmd.ExecuteReaderAsync())
                {
                    while (await reader.ReadAsync())
                    {
                        sections.Add(MapToStandardSectionInfo(reader));
                    }
                }
            }
        }
        
        return sections;
    }

    #endregion

    #region 🔥 材料数据查询

    /// <summary>
    /// 获取所有材料
    /// </summary>
    public async Task<List<MaterialInfo>> GetMaterialsAsync()
    {
        var materials = new List<MaterialInfo>();
        
        using (var connection = GetConnection())
        {
            await connection.OpenAsync();
            
            var sql = @"
                SELECT MaterialId, MaterialCode, MaterialName, MaterialNameEn, Standard,
                       Density, ElasticModulus, ShearModulus, PoissonRatio,
                       YieldStrength, TensileStrength, ThermalExpansion, ThermalConductivity,
                       Description
                FROM Materials 
                WHERE IsActive = 1 
                ORDER BY MaterialCode";
                
            using (var cmd = new SQLiteCommand(sql, connection))
            using (var reader = await cmd.ExecuteReaderAsync())
            {
                while (await reader.ReadAsync())
                {
                    materials.Add(new MaterialInfo
                    {
                        MaterialId = reader.GetInt32("MaterialId"),
                        MaterialCode = reader.GetString("MaterialCode"),
                        MaterialName = reader.GetString("MaterialName"),
                        MaterialNameEn = reader.IsDBNull("MaterialNameEn") ? null : reader.GetString("MaterialNameEn"),
                        Standard = reader.IsDBNull("Standard") ? null : reader.GetString("Standard"),
                        Density = reader.GetDouble("Density"),
                        ElasticModulus = reader.GetDouble("ElasticModulus"),
                        ShearModulus = reader.GetDouble("ShearModulus"),
                        PoissonRatio = reader.GetDouble("PoissonRatio"),
                        YieldStrength = reader.GetDouble("YieldStrength"),
                        TensileStrength = reader.GetDouble("TensileStrength"),
                        ThermalExpansion = reader.GetDouble("ThermalExpansion"),
                        ThermalConductivity = reader.GetDouble("ThermalConductivity"),
                        Description = reader.IsDBNull("Description") ? null : reader.GetString("Description")
                    });
                }
            }
        }
        
        return materials;
    }

    /// <summary>
    /// 根据材料代码获取材料信息
    /// </summary>
    public async Task<MaterialInfo> GetMaterialAsync(string materialCode)
    {
        using (var connection = GetConnection())
        {
            await connection.OpenAsync();
            
            var sql = @"
                SELECT MaterialId, MaterialCode, MaterialName, MaterialNameEn, Standard,
                       Density, ElasticModulus, ShearModulus, PoissonRatio,
                       YieldStrength, TensileStrength, ThermalExpansion, ThermalConductivity,
                       Description
                FROM Materials 
                WHERE MaterialCode = @MaterialCode AND IsActive = 1";
                
            using (var cmd = new SQLiteCommand(sql, connection))
            {
                cmd.Parameters.AddWithValue("@MaterialCode", materialCode);
                
                using (var reader = await cmd.ExecuteReaderAsync())
                {
                    if (await reader.ReadAsync())
                    {
                        return new MaterialInfo
                        {
                            MaterialId = reader.GetInt32("MaterialId"),
                            MaterialCode = reader.GetString("MaterialCode"),
                            MaterialName = reader.GetString("MaterialName"),
                            MaterialNameEn = reader.IsDBNull("MaterialNameEn") ? null : reader.GetString("MaterialNameEn"),
                            Standard = reader.IsDBNull("Standard") ? null : reader.GetString("Standard"),
                            Density = reader.GetDouble("Density"),
                            ElasticModulus = reader.GetDouble("ElasticModulus"),
                            ShearModulus = reader.GetDouble("ShearModulus"),
                            PoissonRatio = reader.GetDouble("PoissonRatio"),
                            YieldStrength = reader.GetDouble("YieldStrength"),
                            TensileStrength = reader.GetDouble("TensileStrength"),
                            ThermalExpansion = reader.GetDouble("ThermalExpansion"),
                            ThermalConductivity = reader.GetDouble("ThermalConductivity"),
                            Description = reader.IsDBNull("Description") ? null : reader.GetString("Description")
                        };
                    }
                }
            }
        }
        
        return null;
    }

    #endregion

    #region 🔥 数据库统计和信息

    /// <summary>
    /// 获取数据库统计信息
    /// </summary>
    public async Task<DatabaseStatistics> GetStatisticsAsync()
    {
        var statistics = new DatabaseStatistics();
        
        using (var connection = GetConnection())
        {
            await connection.OpenAsync();
            
            // 获取各类型截面数量
            var sql = @"
                SELECT st.TypeCode, st.TypeName, COUNT(s.SectionId) as SectionCount
                FROM SectionTypes st
                LEFT JOIN StandardSections s ON st.TypeId = s.TypeId AND s.IsActive = 1
                WHERE st.IsActive = 1
                GROUP BY st.TypeId, st.TypeCode, st.TypeName
                ORDER BY st.SortOrder";
                
            using (var cmd = new SQLiteCommand(sql, connection))
            using (var reader = await cmd.ExecuteReaderAsync())
            {
                while (await reader.ReadAsync())
                {
                    statistics.SectionTypeCounts.Add(new SectionTypeCount
                    {
                        TypeCode = reader.GetString("TypeCode"),
                        TypeName = reader.GetString("TypeName"),
                        Count = reader.GetInt32("SectionCount")
                    });
                }
            }
            
            // 获取总数量
            statistics.TotalSections = statistics.SectionTypeCounts.Sum(x => x.Count);
            
            // 获取材料数量
            using (var cmd = new SQLiteCommand("SELECT COUNT(*) FROM Materials WHERE IsActive = 1", connection))
            {
                statistics.TotalMaterials = Convert.ToInt32(await cmd.ExecuteScalarAsync());
            }
            
            // 获取数据库版本信息
            using (var cmd = new SQLiteCommand("SELECT Value FROM DatabaseInfo WHERE Key = 'Version'", connection))
            {
                var result = await cmd.ExecuteScalarAsync();
                statistics.DatabaseVersion = result?.ToString() ?? "Unknown";
            }
        }
        
        return statistics;
    }

    /// <summary>
    /// 获取数据库信息
    /// </summary>
    public async Task<Dictionary<string, string>> GetDatabaseInfoAsync()
    {
        var info = new Dictionary<string, string>();
        
        using (var connection = GetConnection())
        {
            await connection.OpenAsync();
            
            var sql = "SELECT Key, Value FROM DatabaseInfo";
            using (var cmd = new SQLiteCommand(sql, connection))
            using (var reader = await cmd.ExecuteReaderAsync())
            {
                while (await reader.ReadAsync())
                {
                    info[reader.GetString("Key")] = reader.GetString("Value");
                }
            }
        }
        
        return info;
    }

    #endregion

    #region 🔥 辅助方法

    /// <summary>
    /// 将DataReader映射为StandardSectionInfo对象
    /// </summary>
    private StandardSectionInfo MapToStandardSectionInfo(SQLiteDataReader reader)
    {
        return new StandardSectionInfo
        {
            SectionId = reader.GetInt32("SectionId"),
            TypeCode = reader.GetString("TypeCode"),
            TypeName = reader.GetString("TypeName"),
            Standard = reader.GetString("Standard"),
            SectionCode = reader.GetString("SectionCode"),
            SectionName = reader.GetString("SectionName"),
            
            // 几何参数
            B = reader.GetDouble("B"),
            H = reader.GetDouble("H"),
            T = reader.GetDouble("T"),
            T1 = reader.IsDBNull("T1") ? 0 : reader.GetDouble("T1"),
            R = reader.IsDBNull("R") ? 0 : reader.GetDouble("R"),
            R1 = reader.IsDBNull("R1") ? 0 : reader.GetDouble("R1"),
            D = reader.IsDBNull("D") ? 0 : reader.GetDouble("D"),
            U = reader.IsDBNull("U") ? 0 : reader.GetDouble("U"),
            F = reader.IsDBNull("F") ? 0 : reader.GetDouble("F"),
            
            // 截面特性
            Area = reader.GetDouble("Area"),
            WeightPerMeter = reader.GetDouble("WeightPerMeter"),
            Ix = reader.IsDBNull("Ix") ? 0 : reader.GetDouble("Ix"),
            Iy = reader.IsDBNull("Iy") ? 0 : reader.GetDouble("Iy"),
            Iz = reader.IsDBNull("Iz") ? 0 : reader.GetDouble("Iz"),
            Wx = reader.IsDBNull("Wx") ? 0 : reader.GetDouble("Wx"),
            Wy = reader.IsDBNull("Wy") ? 0 : reader.GetDouble("Wy"),
            ix = reader.IsDBNull("ix") ? 0 : reader.GetDouble("ix"),
            iy = reader.IsDBNull("iy") ? 0 : reader.GetDouble("iy"),
            iz = reader.IsDBNull("iz") ? 0 : reader.GetDouble("iz"),
            Xc = reader.IsDBNull("Xc") ? 0 : reader.GetDouble("Xc"),
            Yc = reader.IsDBNull("Yc") ? 0 : reader.GetDouble("Yc"),
            
            // 元数据
            IsCommon = reader.GetBoolean("IsCommon"),
            IsPreferred = reader.IsDBNull("IsPreferred") ? false : reader.GetBoolean("IsPreferred"),
            MinLength = reader.IsDBNull("MinLength") ? 0 : reader.GetDouble("MinLength"),
            MaxLength = reader.IsDBNull("MaxLength") ? 0 : reader.GetDouble("MaxLength"),
            Tolerance = reader.IsDBNull("Tolerance") ? null : reader.GetString("Tolerance"),
            SurfaceTreatment = reader.IsDBNull("SurfaceTreatment") ? null : reader.GetString("SurfaceTreatment"),
            Remarks = reader.IsDBNull("Remarks") ? null : reader.GetString("Remarks")
        };
    }

    #endregion

    #region 🔥 资源清理

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        // SQLite连接会自动释放，这里预留扩展
    }

    #endregion
}

/// <summary>
/// 🎯 数据库初始化异常
/// </summary>
public class DatabaseInitializationException : Exception
{
    public DatabaseInitializationException(string message) : base(message) { }
    public DatabaseInitializationException(string message, Exception innerException) : base(message, innerException) { }
}