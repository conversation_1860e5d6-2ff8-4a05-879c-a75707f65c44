/// <summary>
/// 🎯 CurtainWallFrameElement的角钢扩展 - 支持角钢的特殊属性和操作
/// </summary>
public partial class CurtainWallFrameElement
{
    #region 🔥 角钢专用属性

    /// <summary>
    /// 🎯 边长属性（角钢专用，覆盖基类的Width属性）
    /// </summary>
    [Category("截面参数"), DisplayName("边长(mm)"), Description("等边角钢的边长")]
    public int AngleSideLength
    {
        get
        {
            if (FrameType?.Type != 3) return 0;
            return Width; // 角钢的Width就是边长
        }
        set
        {
            if (FrameType?.Type != 3) return;
            
            // 🔥 角钢的特殊处理：边长变化时，高度也要同步变化（等边角钢）
            if (FrameType != null)
            {
                EnsureVirtualFrameType();
                _virtualB = value;
                _virtualH = value; // 🔥 关键：等边角钢的两边长必须相等
                
                if (_virtualFrameType != null)
                {
                    _virtualFrameType.B = value;
                    _virtualFrameType.H = value;
                    
                    // 🔥 尝试应用标准规格约束
                    TryApplyAngleStandardConstraints();
                }
                
                if (AutoActivate) { ActCutCal2D3D(); }
                CheckAndRevertToSharedType();
            }
        }
    }

    /// <summary>
    /// 🎯 角钢厚度属性（覆盖基类Thickness，加入标准化约束）
    /// </summary>
    [Category("截面参数"), DisplayName("厚度(mm)"), Description("角钢厚度")]
    public new int Thickness
    {
        get
        {
            return base.Thickness;
        }
        set
        {
            if (FrameType?.Type == 3) // 角钢类型
            {
                // 🔥 角钢厚度变化时，应用标准化约束
                base.Thickness = value;
                TryApplyAngleStandardConstraints();
            }
            else
            {
                base.Thickness = value;
            }
        }
    }

    /// <summary>
    /// 🎯 角钢标准规格名称（只读）
    /// </summary>
    [Category("截面信息"), DisplayName("标准规格"), Description("当前角钢的国标规格名称"), ReadOnly(true)]
    public string AngleStandardSpec
    {
        get
        {
            if (FrameType?.Type != 3) return "非角钢类型";
            
            var frameType = GetCurrentFrameType();
            if (frameType != null && frameType is CurtainWallFrameType cwFrameType)
            {
                if (cwFrameType.IsStandardAngleSteel)
                {
                    return cwFrameType.AngleSpec?.GetStandardName() ?? "未知规格";
                }
                else if (AngleSteelStandards.IsStandardSpec(frameType.B, frameType.T))
                {
                    return AngleSteelStandards.GetStandardName(frameType.B, frameType.T);
                }
                else
                {
                    return $"L{frameType.B}×{frameType.T} (非标)";
                }
            }
            
            return "未设置";
        }
    }

    /// <summary>
    /// 🎯 角钢是否为标准规格（只读）
    /// </summary>
    [Category("截面信息"), DisplayName("标准规格状态"), Description("是否符合国标规格"), ReadOnly(true)]
    public bool IsStandardAngle
    {
        get
        {
            if (FrameType?.Type != 3) return false;
            var frameType = GetCurrentFrameType();
            return frameType != null && AngleSteelStandards.IsStandardSpec(frameType.B, frameType.T);
        }
    }

    /// <summary>
    /// 🎯 角钢重量（基于标准规格）
    /// </summary>
    [Category("截面信息"), DisplayName("每米重量(kg/m)"), Description("基于标准规格的理论重量"), ReadOnly(true)]
    public double AngleWeightPerMeter
    {
        get
        {
            if (FrameType?.Type != 3) return 0;
            
            var frameType = GetCurrentFrameType();
            if (frameType != null)
            {
                var spec = AngleSteelStandards.FindStandardSpec(frameType.B, frameType.T);
                return spec?.WeightPerMeter ?? 0;
            }
            return 0;
        }
    }

    #endregion

    #region 🔥 角钢专用方法

    /// <summary>
    /// 🎯 尝试应用角钢标准化约束
    /// </summary>
    private void TryApplyAngleStandardConstraints()
    {
        if (FrameType?.Type != 3 || _virtualFrameType == null) return;

        int currentSideLength = _virtualFrameType.B;
        int currentThickness = _virtualFrameType.T;

        // 🔥 检查是否为标准规格
        var standardSpec = AngleSteelStandards.FindStandardSpec(currentSideLength, currentThickness);
        
        if (standardSpec == null)
        {
            // 🔥 如果不是标准规格，提供选择：使用最接近的标准规格 或 保持当前参数
            var nearestSpec = AngleSteelStandards.GetNearestStandardSpec(currentSideLength, currentThickness);
            
            // 在实际项目中，这里可以弹出对话框让用户选择
            // 现在默认保持用户输入的参数，但标记为非标准
            OnNonStandardAngleParameterDetected?.Invoke(currentSideLength, currentThickness, nearestSpec);
        }
        else
        {
            // 🔥 应用标准规格的完整参数
            ApplyStandardAngleSpec(standardSpec);
        }
    }

    /// <summary>
    /// 🎯 应用标准角钢规格
    /// </summary>
    private void ApplyStandardAngleSpec(AngleSteelSpec spec)
    {
        if (_virtualFrameType == null) return;

        // 应用标准参数
        _virtualFrameType.B = spec.SideLength;
        _virtualFrameType.H = spec.SideLength; // 等边角钢
        _virtualFrameType.T = spec.Thickness;
        _virtualFrameType.D = (int)spec.TopRadius; // 顶端圆弧半径
        _virtualFrameType.U = (int)spec.MiddleRadius; // 中间圆弧半径
        _virtualFrameType.Area = spec.Area;
        _virtualFrameType.WeightPerMeter = spec.WeightPerMeter;

        // 同步虚拟参数
        _virtualB = spec.SideLength;
        _virtualH = spec.SideLength;
        _virtualT = spec.Thickness;
        _virtualD = (int)spec.TopRadius;

        // 如果虚拟截面是CurtainWallFrameType的扩展，设置标准规格信息
        if (_virtualFrameType is CurtainWallFrameType cwFrameType)
        {
            cwFrameType.ApplyStandardAngleSpec(spec); // 这个方法需要在扩展类中实现
        }
    }

    /// <summary>
    /// 🎯 强制应用标准角钢规格
    /// </summary>
    public void ApplyStandardAngleSpec(int sideLength, int thickness)
    {
        if (FrameType?.Type != 3)
        {
            throw new InvalidOperationException("只有角钢类型才能应用标准角钢规格");
        }

        var spec = AngleSteelStandards.FindStandardSpec(sideLength, thickness);
        if (spec == null)
        {
            throw new ArgumentException($"L{sideLength}×{thickness} 不是标准角钢规格");
        }

        EnsureVirtualFrameType();
        ApplyStandardAngleSpec(spec);
        
        if (AutoActivate) { ActCutCal2D3D(); }
    }

    /// <summary>
    /// 🎯 获取当前角钢的可选厚度列表
    /// </summary>
    public List<int> GetAvailableThicknesses()
    {
        if (FrameType?.Type != 3) return new List<int>();
        
        var frameType = GetCurrentFrameType();
        if (frameType != null)
        {
            return AngleSteelStandards.GetAvailableThicknesses(frameType.B);
        }
        
        return new List<int>();
    }

    /// <summary>
    /// 🎯 获取所有可选的角钢边长
    /// </summary>
    public List<int> GetAvailableAngleSideLengths()
    {
        return AngleSteelStandards.GetAvailableSideLengths();
    }

    /// <summary>
    /// 🎯 验证当前角钢参数
    /// </summary>
    public ValidationResult ValidateAngleParameters()
    {
        var result = new ValidationResult();
        
        if (FrameType?.Type != 3)
        {
            result.AddError("当前不是角钢类型");
            return result;
        }

        var frameType = GetCurrentFrameType();
        if (frameType == null)
        {
            result.AddError("截面类型未设置");
            return result;
        }

        // 等边检查
        if (frameType.B != frameType.H)
        {
            result.AddError("等边角钢的两边长必须相等");
        }

        // 标准规格检查
        if (!AngleSteelStandards.IsStandardSpec(frameType.B, frameType.T))
        {
            result.AddWarning($"L{frameType.B}×{frameType.T} 不是标准规格");
            
            var nearestSpec = AngleSteelStandards.GetNearestStandardSpec(frameType.B, frameType.T);
            result.AddWarning($"最近的标准规格：{nearestSpec?.GetStandardName()}");
        }

        // 几何合理性检查
        if (frameType.T >= frameType.B * 0.8)
        {
            result.AddError("厚度过大，建议小于边长的80%");
        }

        // 圆弧半径检查
        double expectedTopRadius = AngleSteelStandards.CalculateTopRadius(frameType.T);
        if (Math.Abs(frameType.D - expectedTopRadius) > 0.5)
        {
            result.AddWarning($"顶端圆弧半径建议为 {expectedTopRadius:F1}mm");
        }

        return result;
    }

    /// <summary>
    /// 🎯 重置为标准角钢规格
    /// </summary>
    public void ResetToStandardAngleSpec()
    {
        if (FrameType?.Type != 3) return;

        var frameType = GetCurrentFrameType();
        if (frameType != null)
        {
            var nearestSpec = AngleSteelStandards.GetNearestStandardSpec(frameType.B, frameType.T);
            if (nearestSpec != null)
            {
                ApplyStandardAngleSpec(nearestSpec.SideLength, nearestSpec.Thickness);
            }
        }
    }

    #endregion

    #region 🔥 事件和回调

    /// <summary>
    /// 检测到非标准角钢参数时的事件
    /// </summary>
    public event Action<int, int, AngleSteelSpec> OnNonStandardAngleParameterDetected;

    #endregion

    #region 🔥 重写基类方法以支持角钢特殊逻辑

    /// <summary>
    /// 🎯 重写提示信息，为角钢提供专门的显示
    /// </summary>
    public override void EleTips(out string str1, out string str2)
    {
        var frameType = GetCurrentFrameType();
        if (frameType != null && frameType.Type == 3) // 角钢
        {
            str1 = $"等边角钢: {AngleStandardSpec}";
            
            string anchorInfo = _enableAnchor ? " [定位点]" : "";
            string customInfo = CustomizationStatus;
            string standardInfo = IsStandardAngle ? " (国标)" : " (非标)";
            
            str2 = $"L{Width}×{Thickness}mm{standardInfo}, 长度: {_frameLength:F0}mm{customInfo}{anchorInfo}";
            
            // 如果是标准规格，显示重量信息
            if (IsStandardAngle && AngleWeightPerMeter > 0)
            {
                str2 += $", {AngleWeightPerMeter:F2}kg/m";
            }
        }
        else
        {
            // 非角钢类型，使用基类实现
            base.EleTips(out str1, out str2);
        }
    }

    #endregion
}